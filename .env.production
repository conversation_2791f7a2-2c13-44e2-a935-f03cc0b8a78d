VITE_BRIDGE_TESTNET_ENDPOINT=https://testnet-bridge.bitlayer.org
VITE_BITLAYER_TESTNET_BRIDGE=******************************************

VITE_BRIDGE_TARGET_BLOCK_CHAINS=btc_testnet,sepolia
VITE_BRIDGE_SOURCE_BLOCK_CHAINS=sepolia
VITE_BRIDGE_BLOCKED_TOKENS={"ethereum:bitlayer_mainnet":[],"bitlayer_mainnet:ethereum":[],"bitlayer_testnet:btc_testnet":["2584333:39"],"btc_testnet:bitlayer_testnet":["sats","2584333:39"],"bitlayer_testnet:sepolia":["zkUSDT","TST"],"sepolia:bitlayer_testnet":["zkUSDT","TST"],"bsc:bitlayer_mainnet":["USDT","USDC"],"bitlayer_mainnet:bsc":["USDT","USDC"]}

VITE_BITCOIN_CLIENT_ENDPOINT=https://btc-tx-builder.bitlayer.org

VITE_USER_CENTER_ENDPOINT=https://api-activity.bitlayer.org
VITE_USER_CENTER_BASE_ROUTE=/task-center
VITE_USER_CARS_BASE_ROUTE=/gacha-game
VITE_STATIC_ASSET_BASE_URL=https://static.bitlayer.org
VITE_GUESSING_GAME_BASE_ROUTE=/guess
VITE_GUESSING_GAME_CONTRACT=******************************************

VITE_SENTRY_DSN=https://<EMAIL>/4508636174811136

VITE_QUICK_JUMP_API_URL=https://bridge.bitlayer.org/quick-jump
VITE_QUICK_JUMP_GAS_DISCOUNT_CONTRACT=******************************************

VITE_BINANCE_BOOSTER=******************************************

VITE_USER_CENTER_BN_ENDPOINT=https://bn-api-activity.bitlayer.org

