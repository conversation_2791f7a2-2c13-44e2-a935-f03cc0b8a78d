// import * as Sentry from '@sentry/cloudflare';

// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore - the server build file is generated by `remix vite:build`
// eslint-disable-next-line import/no-unresolved
import { mode } from '../build/server';

// export const onRequest = [
//   // Make sure Sentry is the first middleware
//   Sentry.sentryPagesPlugin<{ SENTRY_DSN?: string }>((context) => ({
//     environment: mode,
//     dsn: context.env.SENTRY_DSN,
//     // Set tracesSampleRate to 1.0 to capture 100% of spans for tracing.
//     tracesSampleRate: 1.0,
//   })),
//   // Add more middlewares here
// ];
