name: Publish

on:
  workflow_dispatch:
    inputs:
      password:
        description: 'Publish password'
        required: true

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Check password
        env:
          VERIFIED: ${{ contains( inputs.password, secrets.PUBLISH_PASSWORD ) }}
        run: test "$VERIFIED" = "true"

      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'

      - name: Install # optional, --immutable
        run: |
          yarn config set strict-ssl false
          yarn install --frozen-lockfile

      - name: Build
        run: |
          export NODE_OPTIONS="--max_old_space_size=4096"
          yarn build --mode production

      - id: deploy
        name: Deploy
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CF_WORKER_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: deploy --name=${{ vars.CF_PROD_WORKER_NAME }} --env production

      - name: Notify publish success
        if: ${{ success() }}
        uses: joelwmale/webhook-action@2.4.1
        with:
          url: ${{ secrets.WEBHOOK_URL }}
          body: |
            {
              "msg_type": "post",
              "content": {
                "post": {
                  "en": {
                    "title": "Website published",
                    "content": [
                      [
                        {
                          "tag": "text",
                          "text": "🚀 The website has been published. The preview URL is "
                        },
                        {
                          "tag": "a",
                          "text": "${{ steps.deploy.outputs.deployment-url }}",
                          "href": "${{ steps.deploy.outputs.deployment-url }}"
                        }
                      ]
                    ]
                  }
                }
              }
            }

      - name: Notify publish failure
        if: ${{ failure() }}
        uses: joelwmale/webhook-action@2.4.1
        with:
          url: ${{ secrets.WEBHOOK_URL }}
          body: |
            {
              "msg_type": "post",
              "content": {
                "post": {
                  "en": {
                    "title": "Website publish failed",
                    "content": [
                      [
                        {
                          "tag": "text",
                          "text": "🚧 Website publish failed!. Check the "
                        },
                        {
                          "tag": "a",
                          "text": "Action ${{ github.run_id }}",
                          "href": "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                        },
                        {
                          "tag": "text",
                          "text": " for detail."
                        }
                      ]
                    ]
                  }
                }
              }
            }
