name: Deploy Preview Version

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - 'main'

  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'yarn'

      - name: Install # optional, --immutable
        run: |
          yarn config set strict-ssl false
          yarn install --frozen-lockfile

      - name: Build
        run: |
          export NODE_OPTIONS="--max_old_space_size=4096"
          yarn build --mode staging

      - id: deploy
        name: Deploy
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CF_WORKER_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          command: deploy --name=${{ vars.CF_STAGING_WORKER_NAME }} --env staging

      - id: extract-url
        name: Extract versioned URL
        if: ${{ success() }}
        env:
          DEPLOY_OUTPUT: ${{ steps.deploy.outputs.command-output }}
        run: |
          VERSION_ID=$(echo $DEPLOY_OUTPUT | sed -n 's/.*Current Version ID: //p')
          DEPLOYMENT_URL="${{ steps.deploy.outputs.deployment-url }}"
          VERSION_PREFIX=${VERSION_ID%%-*}
          URL_HOSTNAME=${DEPLOYMENT_URL#*//}
          VERSIONED_URL="https://${VERSION_PREFIX}-${URL_HOSTNAME}"
          echo "The versioned URL is: ${VERSIONED_URL}"
          echo "versioned-url=${VERSIONED_URL}" >> $GITHUB_OUTPUT

      - name: Notify publish success
        if: ${{ success() }}
        uses: joelwmale/webhook-action@2.4.1
        with:
          url: ${{ secrets.WEBHOOK_URL }}
          body: |
            {
              "msg_type": "post",
              "content": {
                "post": {
                  "en": {
                    "title": "The preview version of ${{ github.repository }} deployed",
                    "content": [
                      [
                        {
                          "tag": "text",
                          "text": "🚢 Branch ${{ github.head_ref || github.ref_name }} has been deployed to preview. The URL is "
                        },
                        {
                          "tag": "a",
                          "text": "${{ steps.extract-url.outputs.versioned-url }}",
                          "href": "${{ steps.extract-url.outputs.versioned-url }}"
                        }
                      ]
                    ]
                  }
                }
              }
            }

      - name: Notify publish failure
        if: ${{ failure() }}
        uses: joelwmale/webhook-action@2.4.1
        with:
          url: ${{ secrets.WEBHOOK_URL }}
          body: |
            {
              "msg_type": "post",
              "content": {
                "post": {
                  "en": {
                    "title": "The preview of ${{ github.repository }} deployment failed",
                    "content": [
                      [
                        {
                          "tag": "text",
                          "text": "🚧 Branch ${{ github.head_ref || github.ref_name }} deployment failed!. Check the "
                        },
                        {
                          "tag": "a",
                          "text": "Action ${{ github.run_id }}",
                          "href": "https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                        },
                        {
                          "tag": "text",
                          "text": " for detail."
                        }
                      ]
                    ]
                  }
                }
              }
            }
