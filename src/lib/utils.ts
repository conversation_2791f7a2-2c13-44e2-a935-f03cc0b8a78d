import { BaseChainType, NetworkType } from '@/wallets/config/type';
import { BigNumber as EthersBN } from 'ethers';
import BigNumber from 'bignumber.js';
import { type ClassValue, clsx } from 'clsx';
import { extendTailwindMerge } from 'tailwind-merge';
import Decimal from 'decimal.js';
import { formatUnits as originFormatUnits } from 'viem';

const twMerge = extendTailwindMerge({
  prefix: 'bl-',
});

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const toPercent = (rate: string | undefined | number) => {
  return `${(Number(rate) * 100).toFixed(1)}%`;
};

export function formatNumberWithCommas(number: number) {
  if (number <= 1000) {
    return number?.toString();
  }
  // return new Intl.NumberFormat().format(number);
  return number?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

/** Forked from https://github.com/epoberezkin/fast-deep-equal */

export function deepEqual(a: any, b: any) {
  if (a === b) return true;

  if (a && b && typeof a === 'object' && typeof b === 'object') {
    if (a.constructor !== b.constructor) return false;

    let length: number;
    let i: number;

    if (Array.isArray(a) && Array.isArray(b)) {
      length = a.length;
      if (length !== b.length) return false;
      for (i = length; i-- !== 0; ) if (!deepEqual(a[i], b[i])) return false;
      return true;
    }

    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();
    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();

    const keys = Object.keys(a);
    length = keys.length;
    if (length !== Object.keys(b).length) return false;

    for (i = length; i-- !== 0; )
      if (!Object.prototype.hasOwnProperty.call(b, keys[i]!)) return false;

    for (i = length; i-- !== 0; ) {
      const key = keys[i];

      if (key && !deepEqual(a[key], b[key])) return false;
    }

    return true;
  }

  // true if both NaN, false otherwise
  // biome-ignore lint/suspicious/noSelfCompare: <explanation>
  return a !== a && b !== b;
}

export const removeTrailingZeroesFromDecimals = (value: string) => {
  return value.replace(/^(\d+)(?:\.0+|(\.\d*?)0+)$/, '$1$2');
};

interface FormatBalanceOptions {
  decimalPlaces?: number;
}

export const formatBalance = (
  balance?: string,
  options: FormatBalanceOptions = { decimalPlaces: 6 },
) => {
  if (!balance) return '';

  options.decimalPlaces = options.decimalPlaces || 6;
  const regex = new RegExp(`(\\.\\d{${options.decimalPlaces}})\\d+`);

  // keeps decimal places
  const cutted = balance.replace(regex, '$1');

  if (cutted === '0') return '0';

  // remove trailing zeroes after decimal
  return removeTrailingZeroesFromDecimals(cutted);
};

export const formatUsdBalance = (balance?: number | string) => {
  if (!balance) return '$0';
  const balanceBig = new BigNumber(balance);
  return balanceBig.eq(0) ? '$0' : balanceBig.lt(0.01) ? '<$0.01' : `$${+balanceBig.toFixed(2)}`;
};

export const formatScientificNumber = (price?: number) => {
  if (!price) return '0';
  const strPrice = price + '';
  const [, float] = strPrice.split('.');
  if (float) {
    const eIndex = float.indexOf('e-');
    if (eIndex > -1) {
      const res = /e-(\d+)/gi.exec(float);
      const fixedDecimal = eIndex + (res ? +res[1] : 0);
      return price.toFixed(fixedDecimal);
    }
  }
  return price;
};

export const isTest = () => {
  if (!window) return false;
  return origin.includes('test.pages.dev') || origin.includes('localhost');
};

export const delay = async (ms: number) => {
  return new Promise((resolve) => setTimeout(resolve, ms));
};

export const buildExplorerTransactionUrl = (chain: BaseChainType, hash: string) => {
  if (chain.networkType === NetworkType.tron) {
    return `${chain.chain.blockExplorers?.default.url}/#/transaction/${hash}`;
  }
  return `${chain.chain.blockExplorers?.default.url}/tx/${hash}`;
};

export interface ShortHashOptions {
  prefixLength?: number;
  suffixLength?: number;
}

export const shortHash = (hash: string, options: ShortHashOptions = {}) => {
  const { prefixLength = 9, suffixLength = 9 } = options;
  return `${hash.slice(0, prefixLength)}...${hash.slice(-suffixLength)}`;
};

export const incrBy = (origin: bigint, upPercent: number) => {
  return EthersBN.from(origin)
    .mul(Math.floor(100 * (1 + upPercent)))
    .div(100)
    .toBigInt();
};

export function truncateTo8Decimals(num: string | number | undefined): string {
  if (num === undefined) {
    return '0';
  }

  const decimalNum = new Decimal(num);

  const truncatedNum = decimalNum.toDecimalPlaces(8, Decimal.ROUND_DOWN);

  return truncatedNum.toFixed(8);
}

export function formatNumber(_number: number | string) {
  if (!_number) {
    return truncateTo8Decimals(0);
  }
  const number = Number(_number);
  // 将数字转换为字符串，并确保有八位小数
  const str = number.toFixed(9);
  const [_, decimalPart] = str.split('.');

  const decimalDigits = decimalPart?.split('').map(Number);
  if (decimalDigits[8] >= 5) {
    return truncateTo8Decimals(new Decimal(_number).toFixed(8));
  } else {
    return truncateTo8Decimals(new Decimal(_number).toFixed(8));
  }
}

export type FormatUnitsOptions = {
  keepDecimals?: number;
  trimZeros?: boolean;
};

export function formatUnits(
  amount: number | bigint,
  decimals: number,
  options: FormatUnitsOptions = {},
): string {
  const formatted = originFormatUnits(BigInt(amount), decimals);
  const { keepDecimals, trimZeros = false } = options;

  if (keepDecimals !== undefined) {
    const parts = formatted.split('.');
    if (parts.length === 1) {
      if (trimZeros) {
        return formatted;
      }
      return formatted + '.'.padEnd(keepDecimals + 1, '0');
    }

    parts[1] = parts[1].slice(0, keepDecimals);
    if (trimZeros) {
      parts[1] = parts[1].replace(/0+$/, '');
    } else {
      parts[1] = parts[1].padEnd(keepDecimals, '0');
    }
    return parts.join('.');
  }
  return formatted;
}

/**
 * Formats a number into an abbreviated string with unit suffixes (K, M, B).
 *
 * @param value - The number to format (integer or decimal)
 * @returns A formatted string with appropriate unit suffix
 *
 * @example
 * formatNumberToShortString(999) // "999"
 * formatNumberToShortString(1000) // "1K"
 * formatNumberToShortString(1500) // "1.5K"
 * formatNumberToShortString(1000000) // "1M"
 * formatNumberToShortString(2500000) // "2.5M"
 * formatNumberToShortString(1000000000) // "1B"
 * formatNumberToShortString(-1500) // "-1.5K"
 * formatNumberToShortString(0) // "0"
 */
export function formatNumberToShortString(value: number): string {
  // Handle edge cases
  if (value === 0) return '0';
  if (!isFinite(value)) return value.toString();

  const isNegative = value < 0;
  const absValue = Math.abs(value);

  // Helper function to format the result with proper decimal handling
  const formatResult = (num: number, suffix: string): string => {
    // Round to 1 decimal place and remove unnecessary trailing zeros
    const rounded = Math.round(num * 10) / 10;
    const formatted = rounded % 1 === 0 ? rounded.toString() : rounded.toFixed(1);
    return `${isNegative ? '-' : ''}${formatted}${suffix}`;
  };

  // Numbers less than 1,000: return as-is
  if (absValue < 1000) {
    return value.toString();
  }

  // 1,000 - 999,999: use "K" suffix
  if (absValue < 1000000) {
    return formatResult(absValue / 1000, 'K');
  }

  // 1,000,000 - 999,999,999: use "M" suffix
  if (absValue < 1000000000) {
    return formatResult(absValue / 1000000, 'M');
  }

  // 1,000,000,000+: use "B" suffix
  return formatResult(absValue / 1000000000, 'B');
}
