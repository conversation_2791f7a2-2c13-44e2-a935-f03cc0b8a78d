import { Client } from './client';
import { FaucetAP<PERSON>, StrAPI, GasAPI } from './api';
import { AppLoadContext } from '@remix-run/cloudflare';

class API {
  public readonly faucet: FaucetAPI;
  public readonly strapi: StrAPI;
  public readonly gasapi: Gas<PERSON><PERSON>;

  constructor(client: Client, strapiClient: Client, gasClient: Client) {
    this.faucet = new FaucetAPI(client);
    this.strapi = new StrAPI(strapiClient);
    this.gasapi = new GasAPI(gasClient);
  }
}

export function getEnvValue(context: AppLoadContext, name: string): string {
  if (context.cloudflare?.env) {
    return context.cloudflare.env[name] || '';
  }
  if (typeof process !== 'undefined') {
    return process.env[name] || '';
  }
  return '';
}

export const createAPI = async (context: AppLoadContext) => {
  const client = new Client(getEnvValue(context, 'API_TEST_NET_URL'));
  const strapiClient = new Client(getEnvValue(context, 'API_STRAPI_URL'));
  const gasClient = new Client(getEnvValue(context, 'API_GAS_URL'));

  const token = await context.cloudflare.env['API_STRAPI_TOKEN'].get();
  strapiClient.setToken(token);
  return new API(client, strapiClient, gasClient);
};

export { APIClientError, APIServerError } from './client';
