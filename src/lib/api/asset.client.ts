import { TokenType } from '@/wallets/config/type';
import { BaseResponse, Client } from './client';

export interface AssetItem {
  asset_id: string;
  asset_symbol: string;
  asset_type: number;
  token_address: string;
  decimal: number;
  icon_url: string;
  balance: string;
  price: number;
}

interface AssetsBase {
  total_value: number;
  asset_list: AssetItem[];
}

export interface NFTAsset {
  token_id: string;
  token_name: string;
  token_image: string;
  token_description: string;
  asset_type: number;
  token_address: string;
}
export interface Wrapper<T> extends BaseResponse {
  data: T;
}

export const assetTypeMap: Record<number, TokenType> = {
  0: 'native',
  1: 'brc20',
  2: 'rune',
  3: 'erc20',
  4: 'erc721',
  5: 'erc1155',
};

const testnetClient = new Client('https://dev-asset-api.bitlayerapps.org');
const mainnetClient = new Client('https://asset-api.bitlayer.org');

export class AssetsAPI {
  protected client: Client;

  constructor(client: Client) {
    client.setMode('cors');
    this.client = client;
  }
  getBTCAssets = async (address: string): Promise<Wrapper<AssetsBase>> => {
    return this.client.request('post', `/asset/api/btc`, { btc_address: address });
  };

  getERC20Assets = async (address: string, chainId: number): Promise<Wrapper<AssetsBase>> => {
    return this.client.request('post', '/asset/api/erc20', {
      btr_address: address,
      chain_id: chainId,
    });
  };

  getERC721Assets = async (address: string, chainId: number): Promise<Wrapper<NFTAsset[]>> => {
    return this.client.request('post', `/asset/api/erc721`, {
      btr_address: address,
      chain_id: chainId,
    });
  };
  getERC1155Assets = async (address: string, chainId: number): Promise<Wrapper<NFTAsset[]>> => {
    return this.client.request('post', `/asset/api/erc1155`, {
      btr_address: address,
      chain_id: chainId,
    });
  };
}

const mainnetApi = new AssetsAPI(mainnetClient);
const testnetApi = new AssetsAPI(testnetClient);

export const getAssetsAPI = ({ testnet = false }: { testnet?: boolean }) => {
  return testnet ? testnetApi : mainnetApi;
};
