import { AppLoadContext } from '@remix-run/cloudflare';
import { Client } from './client';
import { CrossChainTransaction } from './models';
import { COIN_NAME_TYPE } from '@/components/icons/coins';

export interface GasV2Config {
  request_id: string;
  encrypt_key: string;
  chains: string[];
  assets: string[];
  face_values: string[];
}

export interface BridgeStateResponse {
  status: number; // 0:stop  1:open
}

export interface EstimateAmountValue {
  face_value: string;
  target_face_value: string;
}

export interface EstimateAmountResponse {
  face_values: EstimateAmountValue[];
  fee: string;
}

export interface CreateOrderRequest {
  target: string;
  face_value: string;
  from_coin: string;
  to_coin: string;
  source: string;
}

export interface PaymentInfo {
  chain: string;
  asset_id: string;
  qrcode: string;
  to: string;
}

export interface CreateOrderResponse {
  face_value: string;
  payment_infos: PaymentInfo[];
  target: string;
  order_no: string;
  expire_at: number;
}

export interface HasBridgeResponse {
  hasBridge: boolean;
}

export interface GasTransaction extends CrossChainTransaction {
  fromAmount: string;
  toAmount: string;
  expireAt: number;
  paymentInfos?: PaymentInfo[];
  fromFaceValue: string;
  status: number;
  toAssetId: string;
}

export class GasV2API {
  protected client: Client;
  protected secret: string;

  constructor(client: Client, secret: string) {
    this.client = client;
    this.secret = secret;
  }

  private get headers() {
    return {
      'Client-Secret': this.secret,
    };
  }

  async loadConfig() {
    return await this.client.request<GasV2Config>(
      'get',
      '/order/configs/31a7e61f286ea6e4825152fd74abacb5',
      null,
      {
        ...this.headers,
      },
    );
  }

  async estimateAmount({
    amount,
    to_coin,
    from_coin,
  }: {
    amount: string[];
    to_coin: COIN_NAME_TYPE;
    from_coin: COIN_NAME_TYPE;
  }) {
    return await this.client.request<EstimateAmountResponse>(
      'post',
      '/order/estimate_amount',
      {
        amount,
        to_coin,
        from_coin,
      },
      {
        ...this.headers,
      },
    );
  }

  async isBirdge({ source, address }: { source: string; address: string }) {
    const params = new URLSearchParams({ source, address });
    return await this.client.request<HasBridgeResponse>(
      'get',
      `/has_bridge?${params.toString()}`,
      null,
      {
        ...this.headers,
      },
    );
  }

  async getBirdgeState() {
    return await this.client.request<BridgeStateResponse>('get', `/system/status`, null, {
      ...this.headers,
    });
  }

  async signBody(body: string, key: string) {
    const enc = new TextEncoder();
    const algorithm = { name: 'HMAC', hash: 'SHA-256' };
    const rawKey = await crypto.subtle.importKey('raw', enc.encode(key), algorithm, false, [
      'sign',
      'verify',
    ]);
    const signature = await crypto.subtle.sign(algorithm.name, rawKey, enc.encode(body));
    return btoa(String.fromCharCode(...new Uint8Array(signature)));
  }

  async createOrder(data: CreateOrderRequest, options: { key: string; requestId: string }) {
    const body = JSON.stringify(data);
    const digest = await this.signBody(body, options.key);

    return await this.client.request<CreateOrderResponse>('post', '/order', body, {
      'Request-Id': options.requestId,
      Sign: digest,
      'Content-Type': 'application/json',
      ...this.headers,
    });
  }

  async getOrder(orderNo: string) {
    return await this.client.request<GasTransaction>('get', `/order/${orderNo}`, null, {
      ...this.headers,
    });
  }

  async getOrderList(address: string) {
    const params = new URLSearchParams({ target_address: address, page: '1', limit: '10' });
    return await this.client.request<{ list: GasTransaction[] }>(
      'get',
      `/orders?${params.toString()}`,
      null,
      {
        ...this.headers,
      },
    );
  }

  async cancelOrder(orderNo: string, options: { key: string; requestId: string }) {
    const data = { order_no: orderNo };
    const body = JSON.stringify(data);
    const digest = await this.signBody(body, options.key);

    return await this.client.request<CreateOrderResponse>('post', '/order/cancel', body, {
      'Request-Id': options.requestId,
      Sign: digest,
      'Content-Type': 'application/json',
      ...this.headers,
    });
  }
}

export async function createAPI(context: AppLoadContext): Promise<GasV2API> {
  const baseURL = context.cloudflare?.env['API_GAS_V2_URL'] || '';
  const secret =
    typeof context.cloudflare?.env['API_GAS_V2_SECRET'] === 'string'
      ? context.cloudflare?.env['API_GAS_V2_SECRET']
      : (await context.cloudflare?.env['API_GAS_V2_SECRET'].get()) || '';
  return new GasV2API(new Client(baseURL), secret);
}
