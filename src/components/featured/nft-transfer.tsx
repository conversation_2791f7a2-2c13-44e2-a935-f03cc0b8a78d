
import { NFTAsset } from '@/lib/api/asset.client';
import 'simplebar-react/dist/simplebar.min.css';
import { Input } from '../ui/input';
import AddressLinkIcon from '../icons/coins/AddressLinkIcon';
import { Button } from '@/components/ui/button';
import { ReactNode, useState } from 'react';
import {
  Dialog,
  DialogClose,
  DialogCloseRounded,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTrigger,
} from '@/components/ui/dialog';
import { CornerMarkGroup } from '@/components/ui/corner-mark';
import { useToast } from '@/hooks/toast';
import { useMutation } from '@tanstack/react-query';
import {
  estimateFeesPerGas,
  estimateGas,
  writeContract,
  waitForTransactionReceipt,
  type Config,
} from '@wagmi/core';
import { Address, BaseError, encodeFunctionData, InsufficientFundsError, IntrinsicGasTooLowError, isAddress } from 'viem';
import { useAccount, useConfig } from 'wagmi';
import { handleException } from '@/modules/bridge/transactions/evm.client';
import { buildExplorerTransactionUrl, cn } from '@/lib/utils';
import { defaultChain } from './navigation';
import { LoaderIcon } from 'lucide-react';
import { ReadableError, UserRejectedError } from '@/modules/bridge/transactions/errors';
import { useReadContract } from 'wagmi';

export interface NftTransferProps {
  token: NFTAsset;
  children: ReactNode;
  title?: string;
  description?: string;
}

export const NftTransferDialog = ({
  token,
  disable,
  refetch,
  children,
}: {
  token: NFTAsset;
  disable: boolean;
  refetch: () => void;
  children: ReactNode;
}) => {
  const { toast } = useToast();
  const { address: accountAddress } = useAccount();
  const [address, setAddress] = useState('');
  const { mutateAsync: transferNft, isPending } = useTransferNft();
  const [resHash, setResHash] = useState('');

  const { data: owner, refetch: refetchOwner } = useReadContract({
    abi: TransferNftAbi,
    address: token.token_address as Address,
    functionName: 'ownerOf',
    args: [BigInt(token.token_id)],
    query: {
      refetchOnWindowFocus: true,
      // refetchInterval: 10000,
    }
  })

  const hasTransferred = owner && accountAddress && (owner as Address).toLowerCase() !== accountAddress.toLowerCase();

  const onChangeAddress = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAddress(e.target.value);
  };

  const onRefresh = () => {
    refetch();
  }

  const onConfirm = async () => {
    if (!address) {
      toast('Please input transfer address');
      return;
    }
    if (!isAddress(address)) {
      toast('Please input the correct address.');
      return;
    }
    try {
      const res = await transferNft({
        to: address,
        token: {
          ...token,
          token_id: token.token_id,
        },
      });
      setResHash(res?.transactionHash);
      onRefresh();
      window.setTimeout(() => {
        onRefresh;
      }, 10000)
      window.setTimeout(() => {
        onRefresh;
      }, 20000)
      window.setTimeout(() => {
        onRefresh;
      }, 30000)
    } catch (error: unknown) {
      if (error instanceof ReadableError && error.message === 'insufficientFunds') {
        toast('Insufficient BTC Balance');
        return;
      }

      if (error instanceof UserRejectedError) {
        toast('User rejected');
        return;
      }

      toast('Transfer failed');
    }
  };

  const onViewInExplorer = () => {
    const url = buildExplorerTransactionUrl(defaultChain, resHash)
    window.open(url, '_blank');
  };

  const onOpenChange = () => {
    refetchOwner();
  }

  if (disable) {
    return (
      <>{children}</>
    )
  }

  return (
    <Dialog onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="md:bl-w-[460px] bl-p-0 bl-bg-primary bl-text-black">
        <DialogHeader>
          <h4 className="bl-text-2xl bl-text-center">
            {
              hasTransferred
                ? 'NFT has been transferred'
                : resHash ? 'Transfer Successful!' : 'Transfer'}
          </h4>
          <DialogCloseRounded className="bl-absolute bl-top-0 bl-right-3" />
        </DialogHeader>
        <div className="bl-relative bl-w-full bl-flex bl-flex-col bl-items-center bl-justify-center">
          <img
            src={token.token_image}
            alt="icon"
            className="bl-w-30 md:bl-w-[155px] bl-object-scale-down bl-mt-2"
          />
          <div className="bl-w-full bl-flex bl-items-center bl-justify-center bl-gap-6 bl-py-3 bl-text-white bl-text-xl md:bl-text-2xl bl-font-[600] bl-pb-6">
            <div className="">#{token.token_id}</div>
            <div className="">{token.token_name}</div>
          </div>
          <div className={cn('bl-w-full bl-pt-5 bl-px-4 md:bl-px-9 bl-border-t-[2px] bl-border-[#D36619]', {
            'bl-hidden': !!resHash || hasTransferred,
          })}>
            <div className='bl-text-base md:bl-text-lg bl-mb-2'>Transfer Address</div>
            <div className='bl-flex bl-items-center bl-gap-2 bl-relative'>
              <Input
                value={address}
                onChange={onChangeAddress}
                placeholder="Please enter a transfer address"
                className='bl-bg-transparent bl-pl-8 focus-within:bl-bg-transparent bl-text-black focus-within:bl-text-black focus:bl-bg-transparent focus:bl-text-black focus-visible:bl-bg-transparent md:focus-visible:bl-bg-transparent focus-visible:bl-text-black md:focus-visible:bl-text-black placeholder:bl-text-divider/60 md:focus-visible:placeholder:bl-text-divider/60 bl-text-[12px] md:bl-text-base'
              />
              <AddressLinkIcon className='bl-text-[#1B1E21] bl-size-4 md:bl-size-5 bl-absolute bl-left-2 bl-z-50' />
            </div>
          </div>
        </div>
        <DialogFooter className="bl-gap-6 bl-pt-10 bl-pb-8">
          {
            hasTransferred ?
              (<DialogClose asChild>
                <Button variant="dark" className="bl-w-44" onClick={onRefresh}>
                  <span className='bl-text-base md:bl-text-2xl'>Confirm</span>
                </Button>
              </DialogClose>)
              : resHash
                ? (<DialogClose asChild>
                  <Button variant="dark" className="bl-w-44" onClick={onViewInExplorer}>
                    <span className='bl-text-base md:bl-text-2xl'>View on Explorer</span>
                  </Button>
                </DialogClose>)
                : (<Button variant="dark" className="bl-w-44" onClick={onConfirm} disabled={isPending || !address || !isAddress(address)}>
                  {isPending ? <LoaderIcon className="bl-size-6 bl-animate-spin" /> : null}
                  <span className='bl-text-base md:bl-text-2xl'>Confirm</span>
                </Button>)
          }
        </DialogFooter>
        <CornerMarkGroup variant="dark" positions={['tl', 'bl', 'br']} />
      </DialogContent>

    </Dialog>
  );
};

interface TransferNftParams {
  // from: Address;
  to: Address;
  token: { token_address: Address } & NFTAsset;
}
export const useTransferNft = () => {
  const config = useConfig() as Config;
  const { address, chain } = useAccount();
  return useMutation({
    mutationFn: async ({ to, token }: TransferNftParams) => {
      try {
        const abi = TransferNftAbi;
        const functionName = 'transferFrom';
        const args = [address, to, BigInt(token.token_id)];

        // Calculate gas fees
        const feesPerGas = await estimateFeesPerGas(config);

        // Estimate gas for the transaction
        const gas = await estimateGas(config, {
          data: encodeFunctionData({
            abi,
            functionName,
            args,
          }),
          to: token.token_address,
          account: address as Address,
          maxFeePerGas: feesPerGas.maxFeePerGas,
          maxPriorityFeePerGas: feesPerGas.maxPriorityFeePerGas,
        });

        // Execute the contract write
        const tx = await writeContract(config, {
          abi,
          address: token.token_address,
          chainId: chain.id,
          functionName,
          args,
          gas,
          maxFeePerGas: feesPerGas.maxFeePerGas,
          maxPriorityFeePerGas: feesPerGas.maxPriorityFeePerGas,
        });

        // Wait for the transaction to be mined
        const receipt = await waitForTransactionReceipt(config, {
          hash: tx,
          pollingInterval: 5_000,
          confirmations: 3,
        });

        return receipt;
      } catch (error) {
        if (error instanceof BaseError) {
          if (error?.message.includes('gas required exceeds allowance')) {
            throw new ReadableError('insufficientFunds');
          }
        }

        handleException(error);
      }
    },
  });
};

export const TransferNftAbi = [{ "inputs": [{ "internalType": "string", "name": "name", "type": "string" }, { "internalType": "string", "name": "symbol", "type": "string" }, { "internalType": "string", "name": "_baseUrl", "type": "string" }, { "internalType": "address", "name": "owner", "type": "address" }, { "internalType": "uint64", "name": "wlStartTime", "type": "uint64" }, { "internalType": "uint64", "name": "wlEndTime", "type": "uint64" }, { "internalType": "uint256", "name": "maxSupply", "type": "uint256" }], "stateMutability": "nonpayable", "type": "constructor" }, { "inputs": [], "name": "AccessControlBadConfirmation", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "account", "type": "address" }, { "internalType": "bytes32", "name": "neededRole", "type": "bytes32" }], "name": "AccessControlUnauthorizedAccount", "type": "error" }, { "inputs": [], "name": "ERC721EnumerableForbiddenBatchMint", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "sender", "type": "address" }, { "internalType": "uint256", "name": "tokenId", "type": "uint256" }, { "internalType": "address", "name": "owner", "type": "address" }], "name": "ERC721IncorrectOwner", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "operator", "type": "address" }, { "internalType": "uint256", "name": "tokenId", "type": "uint256" }], "name": "ERC721InsufficientApproval", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "approver", "type": "address" }], "name": "ERC721InvalidApprover", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "operator", "type": "address" }], "name": "ERC721InvalidOperator", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "owner", "type": "address" }], "name": "ERC721InvalidOwner", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "receiver", "type": "address" }], "name": "ERC721InvalidReceiver", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "sender", "type": "address" }], "name": "ERC721InvalidSender", "type": "error" }, { "inputs": [{ "internalType": "uint256", "name": "tokenId", "type": "uint256" }], "name": "ERC721NonexistentToken", "type": "error" }, { "inputs": [{ "internalType": "address", "name": "owner", "type": "address" }, { "internalType": "uint256", "name": "index", "type": "uint256" }], "name": "ERC721OutOfBoundsIndex", "type": "error" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "owner", "type": "address" }, { "indexed": true, "internalType": "address", "name": "approved", "type": "address" }, { "indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256" }], "name": "Approval", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "owner", "type": "address" }, { "indexed": true, "internalType": "address", "name": "operator", "type": "address" }, { "indexed": false, "internalType": "bool", "name": "approved", "type": "bool" }], "name": "ApprovalForAll", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "string", "name": "oldUrl", "type": "string" }, { "indexed": false, "internalType": "string", "name": "newUrl", "type": "string" }], "name": "BaseUrlSet", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "uint64", "name": "fmStartTime", "type": "uint64" }], "name": "FreeMintStartTimeSet", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "address", "name": "minter", "type": "address" }, { "indexed": false, "internalType": "uint256", "name": "tokenId", "type": "uint256" }], "name": "FreeMinted", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32" }, { "indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32" }, { "indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32" }], "name": "RoleAdminChanged", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32" }, { "indexed": true, "internalType": "address", "name": "account", "type": "address" }, { "indexed": true, "internalType": "address", "name": "sender", "type": "address" }], "name": "RoleGranted", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32" }, { "indexed": true, "internalType": "address", "name": "account", "type": "address" }, { "indexed": true, "internalType": "address", "name": "sender", "type": "address" }], "name": "RoleRevoked", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": true, "internalType": "address", "name": "from", "type": "address" }, { "indexed": true, "internalType": "address", "name": "to", "type": "address" }, { "indexed": true, "internalType": "uint256", "name": "tokenId", "type": "uint256" }], "name": "Transfer", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "uint64", "name": "wlEndTime", "type": "uint64" }], "name": "WhiteListEndTimeSet", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "uint64", "name": "wlStartTime", "type": "uint64" }], "name": "WhiteListStartTimeSet", "type": "event" }, { "anonymous": false, "inputs": [{ "indexed": false, "internalType": "bytes32", "name": "wl", "type": "bytes32" }, { "indexed": false, "internalType": "uint256", "name": "tokenId", "type": "uint256" }], "name": "WhitelistMinted", "type": "event" }, { "inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "MaxSupply", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "OperatorRole", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "OwnerRole", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "to", "type": "address" }, { "internalType": "uint256", "name": "tokenId", "type": "uint256" }], "name": "approve", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "owner", "type": "address" }], "name": "balanceOf", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "tokenId", "type": "uint256" }], "name": "getApproved", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "role", "type": "bytes32" }], "name": "getRoleAdmin", "outputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "role", "type": "bytes32" }, { "internalType": "uint256", "name": "index", "type": "uint256" }], "name": "getRoleMember", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "role", "type": "bytes32" }], "name": "getRoleMemberCount", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "role", "type": "bytes32" }, { "internalType": "address", "name": "account", "type": "address" }], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "role", "type": "bytes32" }, { "internalType": "address", "name": "account", "type": "address" }], "name": "hasRole", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "owner", "type": "address" }, { "internalType": "address", "name": "operator", "type": "address" }], "name": "isApprovedForAll", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "wallet", "type": "address" }], "name": "isWhitelist", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "name": "minted", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "name", "outputs": [{ "internalType": "string", "name": "", "type": "string" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "tokenId", "type": "uint256" }], "name": "ownerOf", "outputs": [{ "internalType": "address", "name": "", "type": "address" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes32[]", "name": "wls", "type": "bytes32[]" }], "name": "removeWhitelists", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "role", "type": "bytes32" }, { "internalType": "address", "name": "callerConfirmation", "type": "address" }], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "role", "type": "bytes32" }, { "internalType": "address", "name": "account", "type": "address" }], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "from", "type": "address" }, { "internalType": "address", "name": "to", "type": "address" }, { "internalType": "uint256", "name": "tokenId", "type": "uint256" }], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "from", "type": "address" }, { "internalType": "address", "name": "to", "type": "address" }, { "internalType": "uint256", "name": "tokenId", "type": "uint256" }, { "internalType": "bytes", "name": "data", "type": "bytes" }], "name": "safeTransferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "operator", "type": "address" }, { "internalType": "bool", "name": "approved", "type": "bool" }], "name": "setApprovalForAll", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "string", "name": "_baseUrl", "type": "string" }], "name": "setBaseUri", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint64", "name": "wlEndTime", "type": "uint64" }], "name": "setWhitelistEndTime", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "uint64", "name": "wlStartTime", "type": "uint64" }], "name": "setWhitelistStartTime", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes4", "name": "interfaceId", "type": "bytes4" }], "name": "supportsInterface", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "symbol", "outputs": [{ "internalType": "string", "name": "", "type": "string" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "index", "type": "uint256" }], "name": "tokenByIndex", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "owner", "type": "address" }, { "internalType": "uint256", "name": "index", "type": "uint256" }], "name": "tokenOfOwnerByIndex", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "uint256", "name": "tokenId", "type": "uint256" }], "name": "tokenURI", "outputs": [{ "internalType": "string", "name": "", "type": "string" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "totalSupply", "outputs": [{ "internalType": "uint256", "name": "", "type": "uint256" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "address", "name": "from", "type": "address" }, { "internalType": "address", "name": "to", "type": "address" }, { "internalType": "uint256", "name": "tokenId", "type": "uint256" }], "name": "transferFrom", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [{ "internalType": "bytes32[]", "name": "wls", "type": "bytes32[]" }], "name": "uploadWhitelists", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "whitelistMint", "outputs": [], "stateMutability": "nonpayable", "type": "function" }, { "inputs": [], "name": "whitelistMintEndAt", "outputs": [{ "internalType": "uint64", "name": "", "type": "uint64" }], "stateMutability": "view", "type": "function" }, { "inputs": [], "name": "whitelistMintStartAt", "outputs": [{ "internalType": "uint64", "name": "", "type": "uint64" }], "stateMutability": "view", "type": "function" }, { "inputs": [{ "internalType": "bytes32", "name": "", "type": "bytes32" }], "name": "whitelists", "outputs": [{ "internalType": "bool", "name": "", "type": "bool" }], "stateMutability": "view", "type": "function" }]