import { useAccount } from '@/hooks/wallet/account';
import { But<PERSON> } from '@/components/ui/button';
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerPortal,
  DrawerTrigger,
} from '@/components/ui/drawer';
import { AssetTypes, BaseChainType, NetworkType, Token } from '@/wallets/config/type';
import {
  WalletDriver,
  WalletDriverName,
  useChainWallets,
  walletDrivers,
} from '@/hooks/wallet/common';
import { useConnect } from '@/hooks/wallet/connect';
import { Text } from '@/components/ui/text';
import { useDialog } from '@/hooks/dialog';
import { useDisconnect } from '@/hooks/wallet/disconnect';
import { ArrowRightIcon, LoaderIcon, Power } from 'lucide-react';
import { BalanceData } from '@/hooks/wallet/balance';
import React, { ComponentProps, ReactNode, useState, forwardRef } from 'react';
import { cn, formatBalance, formatUsdBalance } from '@/lib/utils';
import Exchange2Icon from '../icons/Exchange2Icon';
import GasPumpIcon from '../icons/GasPumpIcon';
import { motion } from 'framer-motion';
import { useTranslation, Trans } from 'react-i18next';
import { Link } from '../i18n/link';
import ArrowLeftIcon from '../icons/ArrowLeftIcon';
import useCopy from '@react-hook/copy';
import CheckedIcon from '../icons/CheckedIcon';
import CopyIcon from '../icons/CopyIcon';
import BITLAYERIcon from '@/components/icons/coins/BITLAYERIcon';
import { UseEnsureOnChain } from '@/hooks/wallet/chains';
import { useAssets } from '@/hooks/wallet/assets';
import { SectionBadge } from '../ui/section-badge';
import { Slot } from '@radix-ui/react-slot';
import { NFTAsset } from '@/lib/api/asset.client';
import { TokenIcon } from '../ui/token-icon';
import { setWalletConnectState } from '@/wallets/evm/connectors/wallectConnect.client';
import 'simplebar-react/dist/simplebar.min.css';
import SimpleBar from 'simplebar-react';
import { NftTransferDialog } from './nft-transfer';
import React from 'react';
import {
  CONTRACT_ADDRESS_MAIN,
  CONTRACT_ADDRESS_TEST,
} from '@/routes/($lang)._app+/airdrop+/lucky-helmet.($network)/section.banner';

export function shortAddress(address: string) {
  if (!address) {
    return;
  }
  return `${address.slice(0, 4)}...${address.slice(-4)}`;
}

export const ChainIcon = ({
  icon: Icon,
  className,
}: {
  className?: string;
  icon?: string | React.FC<React.SVGProps<SVGSVGElement>>;
}) => {
  if (typeof Icon === 'string') {
    return <img src={Icon} alt="icon" className={className} />;
  }

  if (Icon) {
    return <Icon className={className} />;
  }
  return <BITLAYERIcon className={className} />;
};

export const SmallWalletButton = ({ className, ...props }: ComponentProps<typeof Button>) => {
  return (
    <Button
      {...props}
      variant="dark2"
      type="button"
      className={cn('btn-xs bl-h-7 bl-text-base/none bl-min-w-52 md:bl-w-52 bl-py-0', className)}
    />
  );
};

export const ConnectWalletDialog = ({
  chain,
  title,
  description,
  onSuccess,
  close,
}: {
  title?: string | React.ReactElement;
  description?: string | React.ReactElement;
  chain: BaseChainType;
  onSuccess?: () => void;
  close: () => void;
}) => {
  const { connect } = useConnect();
  const [connectName, setConnectName] = useState('');
  const drivers = useChainWallets(chain.networkType);

  const handleClickConnect = (driver: WalletDriver) => {
    const { id, wallectConnectQRName, name } = driver;
    setWalletConnectState(wallectConnectQRName || '');
    setConnectName(name);
    connect(id, chain.networkType, {
      chain,
      onSuccess: () => {
        close();
        onSuccess?.();
      },
      onSettled: () => {
        setConnectName('');
      },
    });
  };
  return (
    <div className="bl-relative bl-w-72 bl-flex bl-flex-col bl-items-center">
      <Text variant="dark" size="none" className="bl-text-2xl/9">
        {title || <Trans i18nKey={'common.connect'} />}
      </Text>
      <Text variant="secondary" size="sm" className="bl-text-center bl-text-primary-foreground">
        {description || <Trans i18nKey={'common.connectDesc'} />}
      </Text>
      <SimpleBar
        autoHide={false}
        className="dialogScrollBar bl-w-[258px] sm:bl-w-72 bl-pl-6 bl-pr-5 bl-mt-4 bl-max-h-[210px]"
      >
        {drivers.map((driver) => {
          const Icon = driver.icon;
          const name = driver.name;
          return (
            <SmallWalletButton
              key={driver.name}
              onClick={() => handleClickConnect(driver)}
              className="bl-shrink-0"
            >
              <div className="bl-flex bl-w-full bl-h-full bl-pl-1 bl-items-center bl-justify-between">
                <Icon
                  className={cn(
                    'bl-size-4 bl-rounded-full bl-shrink-0',
                    driver.name === 'MetaMask' ? 'bl-w-[14px] bl-h-5 bl-ml-[1px]' : '',
                  )}
                />
                <span className="bl-w-full bl-h-4 bl-leading-[18px] bl-ml-3 bl-text-center">
                  {name}
                </span>
                <span className="bl-w-2 bl-h-6 bl-shrink-0">
                  {connectName === driver.name && (
                    <LoaderIcon className="bl-w-[14px] bl-animate-spin" />
                  )}
                </span>
              </div>
            </SmallWalletButton>
          );
        })}
      </SimpleBar>
    </div>
  );
};

export const SwitchChainDialog = ({
  chain,
  close,
}: {
  chain: BaseChainType;
  close: () => void;
}) => {
  const { ensure } = UseEnsureOnChain();
  const handleSwitchChain = async () => {
    await ensure({ chain });
    close();
  };
  return (
    <div className="bl-relative bl-w-72 bl-flex bl-flex-col bl-items-center">
      <Text variant="dark" size="none" className="bl-text-2xl/9">
        <Trans i18nKey="common.switchChain" values={{ chainName: chain?.name }} />
      </Text>
      <Text variant="secondary" size="sm" className="bl-text-center bl-text-primary-foreground">
        <Trans
          i18nKey={'pages.bridge.switchChainDesc'}
          values={{
            chainName: chain.name,
            networkType: chain.networkType,
          }}
        />
      </Text>
      <Button
        size="lg"
        variant="dark2"
        // overlayVariant="secondary"
        className="bl-h-10 bl-mt-8 bl-mb-8 hover:bl-border-primary"
        outlineClassName="group-hover/button:bl-border-primary"
        type="button"
        onClick={handleSwitchChain}
      >
        <div>
          <Trans i18nKey="common.switchChain" values={{ chainName: chain?.name }} />
        </div>
      </Button>
    </div>
  );
};

export interface WalletConnectorProps {
  chain?: BaseChainType;
  children: ReactNode;
  title?: string;
  description?: string;
}

export const WalletConnector = ({
  chain,
  children,
  title = 'Connect Wallet',
  description = 'Connect your wallet to transfer tokens',
}: WalletConnectorProps) => {
  const { open: openDialog } = useDialog();
  const handleClickConnect = () => {
    if (!chain) return;
    openDialog({
      content: ({ close }) => (
        <ConnectWalletDialog title={title} description={description} chain={chain} close={close} />
      ),
    });
  };

  return <Slot onClick={handleClickConnect}>{children}</Slot>;
};

const WalletSection = ({ className, children }: { className?: string; children?: ReactNode }) => {
  return (
    <div
      className={cn(
        'bl-flex bl-w-full bl-py-7 bl-border-b-2 bl-border-card-border last:bl-border-b-0',
        className,
      )}
    >
      {children}
    </div>
  );
};

interface LinkData {
  name: string;
  link: string;
  icon: React.FC<React.SVGProps<SVGSVGElement>>;
}

interface LinkButtonProps {
  link: LinkData;
  className?: string;
}

const LinkButton = ({ link, className }: LinkButtonProps) => {
  const Icon = link.icon;
  return (
    <DrawerClose asChild>
      <Button
        variant="outline"
        overlayVariant="secondary"
        className="bl-h-20 bl-w-full bl-duration-200 hover:bl-border-primary hover:bl-text-black"
        outlineClassName="bl-duration-200 group-hover/button:bl-border-primary"
        asChild
      >
        <Link to={link.link}>
          <div className="bl-space-y-2">
            <Icon className="bl-size-6" />
            <div className="bl-text-sm sm:bl-text-base">{link.name}</div>
          </div>
        </Link>
      </Button>
    </DrawerClose>
  );
};

const TokenBadge = ({ token }: { token: Pick<Token, 'name' | 'type'> }) => {
  const label = token.type === 'native' ? token.name : token.type;
  return (
    <div className="bl-w-fit bl-text-primary bl-bg-primary/20 bl-text-sm/[18px] bl-px-2 bl-uppercase group-data-[highlighted]:bl-text-white">
      {label}
    </div>
  );
};

const TokenBalance = ({
  balance,
  usdBalance,
  token,
}: {
  balance?: BalanceData;
  usdBalance?: number | string;
  token: Token;
}) => {
  return (
    <div className="bl-flex bl-items-center bl-justify-between bl-w-full bl-border-b bl-border-card-border bl-text-lg/none bl-py-5">
      <div className="bl-flex bl-items-center bl-gap-3.5">
        <TokenIcon token={token} className="bl-size-8" />
        <div className="bl-space-y-1">
          <div>{token.name}</div>
          <TokenBadge token={token} />
        </div>
      </div>
      <div className="bl-text-right">
        <div>{formatBalance(balance?.formatted)}</div>
        <div className="bl-text-base bl-text-[#515151]">{formatUsdBalance(usdBalance)}</div>
      </div>
    </div>
  );
};

const NFTItem = ({ token, refetch }: { token: NFTAsset; refetch: () => void }) => {
  const whiteListContractAddress = [
    CONTRACT_ADDRESS_TEST.toLowerCase(),
    CONTRACT_ADDRESS_MAIN.toLowerCase(),
  ];
  const canTransfer = whiteListContractAddress.includes(token.token_address.toLowerCase());

  return (
    <NftTransferDialog token={token} disable={!canTransfer} refetch={refetch}>
      <div className="bl-w-1/2 bl-bg-black bl-h-[270px] bl-relative bl-group">
        <div className="bl-w-full bl-h-48 bl-border bl-border-l-0 bl-border-card-border">
          <img
            src={token.token_image}
            alt="icon"
            className="bl-w-full bl-h-full bl-object-scale-down"
          />
        </div>
        <div className="bl-py-3 bl-border-r bl-border-card-border bl-h-[62px]">
          <div className="bl-h-5 bl-pl-3">#{token.token_id}</div>
          <div className="bl-pl-3 bl-text-sm/5 bl-text-secondary">{token.token_name}</div>
        </div>
        {!canTransfer ? null : (
          <div className="bl-bg-[#F5F5F5] bl-h-[62px] bl-absolute bl-top-48 bl-left-0 bl-right-0 bl-hidden md:group-hover:bl-flex bl-transition-all bl-justify-center bl-items-center bl-text-black bl-text-base hover:bl-text-primary bl-cursor-pointer">
            Transfer
          </div>
        )}
      </div>
    </NftTransferDialog>
  );
};

const AssetsList = ({
  address,
  type,
  chain,
}: {
  address: string;
  type: AssetTypes;
  chain: BaseChainType;
}) => {
  const {
    data: assetsList,
    isLoading,
    refetch,
  } = useAssets({
    address,
    type,
    chain,
  });
  if (isLoading) {
    return (
      <div className="bl-flex bl-justify-center bl-items-center bl-h-15 bl-mt-20">
        <LoaderIcon className="bl-w-10 bl-animate-spin" />
      </div>
    );
  }
  if (assetsList && assetsList.list) {
    if (type === AssetTypes.NFT) {
      if (assetsList.list.length) {
        return (
          <div className="bl-relative bl-mt-10">
            <div className="bl-absolute bl-w-[calc(100%+56px)] bl-overflow-y-auto bl-h-[calc(100vh-428px)] sm:bl-h-[calc(100vh-364px)] -bl-translate-x-7 bl-flex bl-flex-wrap">
              {assetsList.list.map((item: any) => (
                <NFTItem key={item.token_id} token={item} refetch={refetch} />
              ))}
            </div>
          </div>
        );
      } else {
        return (
          <SectionBadge className="bl-w-56 bl-h-12 bl-mt-28 bl-mx-auto">
            <Trans i18nKey={'navigation.walletDrawer.noNFT'} />
          </SectionBadge>
        );
      }
    } else {
      return (
        <div className={cn(type === AssetTypes.BTC ? '' : 'bl-mt-5')}>
          {assetsList.list.map((item: any) => (
            <TokenBalance
              key={item.asset_id}
              usdBalance={item.usdBalance}
              balance={item.balance}
              token={item}
            />
          ))}
        </div>
      );
    }
  }
  return '';
};
const WalletAssets = ({ address, chain }: { address: string; chain: BaseChainType }) => {
  const [active, setActive] = useState('Token');
  const isEvm = chain.networkType === NetworkType.evm;
  return (
    <WalletSection className="bl-flex-col">
      {isEvm && (
        <div className="bl-flex bl-justify-between bl-gap-6">
          <Button
            variant="outline"
            overlayVariant="secondary"
            onClick={() => setActive('Token')}
            className={cn(
              'bl-h-10 bl-w-full bl-duration-200 hover:bl-border-primary',
              active === 'Token' ? 'bl-bg-white bl-text-black' : '',
            )}
            outlineClassName="bl-duration-200 group-hover/button:bl-border-primary"
          >
            <span>Token</span>
          </Button>
          <Button
            variant="outline"
            overlayVariant="secondary"
            onClick={() => setActive('NFT')}
            className={cn(
              'bl-h-10 bl-w-full bl-duration-200 hover:bl-border-primary',
              active === 'NFT' ? 'bl-bg-white bl-text-black' : '',
            )}
            outlineClassName="bl-duration-200 group-hover/button:bl-border-primary"
          >
            <span>NFT</span>
          </Button>
        </div>
      )}
      <AssetsList
        address={address}
        type={isEvm ? (active === 'NFT' ? AssetTypes.NFT : AssetTypes.ERC20) : AssetTypes.BTC}
        chain={chain}
      />
    </WalletSection>
  );
};

const MobileCloseButton = () => {
  return (
    <>
      <div className="bl-bg-background bl-fixed bl-w-full bl-z-50 bl-top-0 bl-left-0 bl-h-16 bl-pl-7 bl-flex bl-items-center sm:bl-hidden">
        <DrawerClose className="bl-flex bl-w-full bl-items-center bl-gap-2 bl-text-secondary bl-text-lg">
          <ArrowRightIcon className="bl-size-3.5" strokeWidth={2} />
          Return
        </DrawerClose>
      </div>
      <div className="bl-h-16 sm:bl-hidden"></div>
    </>
  );
};

const DesktopCloseButton = () => {
  return (
    <DrawerClose className="bl-h-full bl-w-8 bl-border-x bl-border-card-border bl-justify-center bl-items-center bl-text-primary hover:bl-text-white hover:bl-bg-primary bl-duration-200 bl-hidden sm:bl-flex">
      <ArrowLeftIcon className="bl-size-3 bl-rotate-180" />
    </DrawerClose>
  );
};

const HeadSection = ({
  address,
  driver,
  chain,
  shouldSwitch = true,
}: {
  address: string;
  driver: WalletDriverName;
  chain: BaseChainType;
  shouldSwitch?: boolean;
}) => {
  const { copied, copy, reset } = useCopy(address);
  const { chainId } = useAccount({ network: chain.networkType });
  const { disconnect } = useDisconnect();
  const { ensure } = UseEnsureOnChain();
  const handleClickDisconnect = () => {
    disconnect(chain.networkType);
  };

  const isWrongNet = shouldSwitch && chain.chain.id !== chainId;
  const handleCopy = () => {
    copy();
    setTimeout(reset, 2000);
  };
  const handleSwitch = () => {
    ensure({ chain });
  };
  const explorerUrl = `${chain.chain.blockExplorers?.default.url}/address/${address}`;
  const Icon = walletDrivers[driver]?.icon;

  return (
    <WalletSection className="bl-justify-between">
      <div className="bl-flex bl-items-center bl-gap-2">
        <div className="bl-relative bl-size-[50px] bl-shrink-0">
          <ChainIcon icon={chain.icon} className="bl-size-[50px]" />
          <div className="bl-size-7 bl-flex bl-items-center bl-justify-center bl-absolute -bl-bottom-2 -bl-right-[10px] bl-bg-primary bl-rounded-full">
            {Icon && <Icon className="bl-size-5" />}
          </div>
        </div>
        <div className="bl-pl-3 bl-mt-2 bl-flex bl-flex-col bl-items-start bl-justify-between">
          <div className="bl-flex bl-items-center bl-justify-center">
            <a
              href={explorerUrl}
              target="_blank"
              rel="noreferer noreferrer"
              className=" bl-text-xl/none hover:bl-text-primary"
            >
              {shortAddress(address)}
            </a>
            <button className="hover:bl-text-white" onClick={handleCopy}>
              {copied ? (
                <CheckedIcon className="bl-size-4.5" />
              ) : (
                <CopyIcon className="bl-size-4.5" />
              )}
            </button>
          </div>
          <button
            className={cn('bl-text-indicator bl-cursor-pointer bl-text-left bl-leading-4 bl-mt-1', {
              'bl-text-button-error': isWrongNet,
            })}
            onClick={isWrongNet ? handleSwitch : undefined}
          >
            {isWrongNet ? (
              <Trans
                i18nKey={'navigation.walletDrawer.wrongNet'}
                values={{
                  chainName: chain.name,
                }}
              />
            ) : (
              chain.name
            )}
          </button>
        </div>
      </div>
      <Button
        className="bl-size-12 bl-rounded-full bl-border bl-border-primary bl-text-primary bl-text-2xl/none hover:bl-text-black bl-p-0 bl-bg-background"
        overlayVariant="secondary"
        style={{ WebkitMask: 'none' }}
        onClick={handleClickDisconnect}
      >
        <Power className="bl-size-5" />
      </Button>
    </WalletSection>
  );
};

const BalanceSection = ({ chain, address }: { chain: BaseChainType; address: string }) => {
  const { t } = useTranslation();
  const links = [
    {
      name: t('common.bridge'),
      link: '/bridge',
      icon: Exchange2Icon,
    },
    {
      name: t('common.getGas'),
      link: '/flash-bridge',
      icon: GasPumpIcon,
    },
  ];

  return (
    <WalletSection className="bl-flex-col bl-justify-between bl-gap-3.5">
      <div className="bl-text-[50px] bl-hidden"></div>
      <div className="bl-grid bl-grid-cols-2 bl-gap-6">
        {links.map((link, index) => (
          <LinkButton key={index} className="bl-text-black bl-w-full" link={link} />
        ))}
      </div>
    </WalletSection>
  );
};

export function HeadWalletButton({ chain }: { chain: BaseChainType }) {
  const network = chain.networkType;
  const { t } = useTranslation();

  const { address, driver } = useAccount({ network });
  const { open: openDialog } = useDialog();

  if (!address) {
    const handleClickConnect = () => {
      openDialog({
        content: ({ close }) => (
          <ConnectWalletDialog
            title={t('common.connect')}
            description={t('common.connectDesc')}
            chain={chain}
            close={close}
          />
        ),
      });
    };
    return (
      <Button
        variant="ghost"
        className="bl-px-2 md:bl-px-3 bl-text-base/none bl-w-36 md:bl-w-44 md:bl-text-xl/none bl-h-7 md:bl-h-9"
        onClick={handleClickConnect}
      >
        {t('common.connect')}
      </Button>
    );
  }

  const Icon = walletDrivers[driver]?.icon;

  return (
    <WalletDrawer address={address} chain={chain}>
      <Button variant="ghost" className="bl-w-36 md:bl-w-44 bl-h-7 md:bl-h-9">
        <div className="bl-flex bl-items-center bl-gap-2 md:bl-gap-3.5">
          <div className="bl-relative">
            <div className="bl-size-6 bl-rounded-full">
              <ChainIcon icon={chain.icon} className="bl-size-6" />
            </div>
            <div className="bl-absolute -bl-bottom-1 -bl-right-1 bl-size-3  bl-bg-primary bl-rounded-full">
              {Icon && <Icon className="bl-size-3" />}
            </div>
          </div>
          <span className="bl-text-base/none md:bl-text-xl/none">{shortAddress(address)}</span>
        </div>
      </Button>
    </WalletDrawer>
  );
}

export function WalletDrawer({
  address,
  chain,
  children,
  shouldSwitch,
}: {
  address: string;
  chain: BaseChainType;
  children: ReactNode;
  shouldSwitch?: boolean;
}) {
  const network = chain.networkType;
  const { driver } = useAccount({ network });

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      className="bl-flex"
    >
      <Drawer direction="right" shouldScaleBackground={false}>
        <DrawerTrigger asChild>{children}</DrawerTrigger>
        <DrawerPortal>
          <DrawerContent className="bl-fixed bl-bottom-0 bl-right-0 bl-h-dvh bl-w-[100vw] sm:bl-w-[26rem] bl-bg-background bl-border-l-card-border bl-flex-row">
            <DesktopCloseButton />
            <div className="bl-grow bl-flex-1 bl-text-white bl-px-7 bl-relative bl-overflow-y-auto">
              <MobileCloseButton />
              <HeadSection
                address={address}
                driver={driver!}
                chain={chain}
                shouldSwitch={shouldSwitch}
              />
              <BalanceSection address={address} chain={chain} />
              <WalletAssets address={address} chain={chain} />
            </div>
          </DrawerContent>
        </DrawerPortal>
      </Drawer>
    </motion.div>
  );
}

export interface SwitchChainButtonProps extends ComponentProps<typeof Button> {
  chain: BaseChainType;
}

export const SwitchChainButton = forwardRef<HTMLButtonElement, SwitchChainButtonProps>(
  ({ chain, onClick, children, ...props }, ref) => {
    const { ensure, isPending } = UseEnsureOnChain();

    const handleClick = async (event: React.MouseEvent<HTMLButtonElement>) => {
      if (onClick) {
        onClick(event);
      } else {
        await ensure({ chain });
      }
    };

    return (
      <Button ref={ref} onClick={handleClick} {...props} disabled={isPending}>
        {children || <Trans i18nKey="common.switchChain" values={{ chainName: chain?.name }} />}
      </Button>
    );
  },
);

SwitchChainButton.displayName = 'SwitchChainButton';
