import { SVGProps } from 'react';

const AddressLinkIcon = (props: SVGProps<SVGSVGElement>) => {
  return (<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" {...props}>
    <path d="M18.3643 15.5353L16.9501 14.1211L18.3643 12.7069C20.3169 10.7543 20.3169 7.58848 18.3643 5.63586C16.4117 3.68323 13.2458 3.68323 11.2932 5.63586L9.87898 7.05007L8.46477 5.63586L9.87898 4.22164C12.6127 1.48797 17.0448 1.48797 19.7785 4.22164C22.5122 6.95531 22.5122 11.3874 19.7785 14.1211L18.3643 15.5353ZM15.5358 18.3638L14.1216 19.778C11.388 22.5116 6.9558 22.5116 4.22213 19.778C1.48846 17.0443 1.48846 12.6121 4.22213 9.8785L5.63634 8.46428L7.05056 9.8785L5.63634 11.2927C3.68372 13.2453 3.68372 16.4111 5.63634 18.3638C7.58896 20.3164 10.7548 20.3164 12.7074 18.3638L14.1216 16.9495L15.5358 18.3638ZM14.8287 7.75718L16.243 9.17139L9.17188 16.2424L7.75766 14.8282L14.8287 7.75718Z" fill="currentColor" />
  </svg>)
}

export default AddressLinkIcon;