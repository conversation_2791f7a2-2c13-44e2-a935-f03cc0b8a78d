import { SVGProps } from 'react';

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={200}
    height={200}
    viewBox="0 0 200 200"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={100} cy={100} r={100} fill="#4DA2FF" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M128.446 89.98a36.073 36.073 0 0 1 7.856 22.52 36.344 36.344 0 0 1-8.074 22.795l-.437.537-.115-.682a33.03 33.03 0 0 0-.347-1.752c-2.528-11.128-10.76-20.669-24.311-28.398-9.152-5.206-14.39-11.467-15.766-18.59-.889-4.603-.228-9.227 1.048-13.188 1.276-3.957 3.173-7.277 4.787-9.273l5.273-6.46a2.308 2.308 0 0 1 3.58 0l26.511 32.49h-.005Zm8.337-6.456-35.334-43.3a1.686 1.686 0 0 0-2.612 0l-35.33 43.3-.114.144A47.015 47.015 0 0 0 53 113.222c0 26.054 21.108 47.177 47.143 47.177 26.034 0 47.142-21.123 47.142-47.177a46.99 46.99 0 0 0-10.393-29.549l-.114-.144.005-.005Zm-64.82 6.316 3.158-3.877.095.716c.074.568.169 1.135.278 1.707 2.046 10.756 9.35 19.719 21.56 26.662 10.616 6.057 16.798 13.02 18.576 20.655.745 3.185.874 6.32.551 9.063l-.02.169-.154.075a36.02 36.02 0 0 1-15.869 3.663c-19.967 0-36.154-16.195-36.154-36.178 0-8.58 2.984-16.459 7.97-22.665l.01.01Z"
      fill="#fff"
    />
  </svg>
);

export const SUIShape = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={202}
    height={259}
    viewBox="0 0 202 259"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M161.667 107.632c10.504 13.198 16.834 29.964 16.834 48.144s-6.464 35.417-17.305 48.749l-.943 1.145-.269-1.482c-.202-1.212-.471-2.491-.741-3.77-5.386-23.769-23.028-44.171-52.116-60.735-19.594-11.11-30.838-24.509-33.801-39.727-1.885-9.83-.471-19.728 2.222-28.212 2.76-8.484 6.8-15.554 10.235-19.796l11.312-13.804c1.952-2.424 5.723-2.424 7.676 0l56.896 69.488Zm17.844-13.803L103.828 1.313c-1.414-1.75-4.175-1.75-5.589 0L22.49 93.829l-.269.337C8.35 111.47 0 133.42 0 157.324 0 213.009 45.248 258.19 101 258.19s101-45.181 101-100.866c0-23.903-8.349-45.854-22.287-63.158l-.202-.337ZM40.602 107.363l6.733-8.282.202 1.549c.135 1.212.337 2.424.606 3.636 4.377 22.96 20.066 42.15 46.191 56.964 22.759 12.928 35.956 27.808 39.794 44.17 1.616 6.801 1.885 13.534 1.212 19.392l-.067.337-.337.135a77.632 77.632 0 0 1-34.003 7.81c-42.757 0-77.434-34.609-77.434-77.298 0-18.315 6.397-35.216 17.103-48.413Z"
      fill="currentColor"
    />
  </svg>
);

export default SvgComponent;
