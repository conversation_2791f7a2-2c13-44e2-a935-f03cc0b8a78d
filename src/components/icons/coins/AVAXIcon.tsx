import { SVGProps } from 'react';

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={559}
    height={558}
    viewBox="0 0 559 558"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={279.601} cy={279.114} r={212.777} fill="#fff" />
    <path
      d="M279.603.467c153.893 0 278.649 124.755 278.649 278.649s-124.756 278.65-278.649 278.65C125.709 557.766.953 433.01.953 279.116.953 125.222 125.71.466 279.603.466ZM386.927 277.94c-5.005-8.669-17.518-8.669-22.523 0l-53.439 92.56c-5.005 8.669 1.251 19.505 11.262 19.505h106.877c10.011 0 16.267-10.836 11.262-19.505l-53.439-92.56Zm-96.059-166.385c-5.005-8.669-17.517-8.669-22.522 0L118.844 370.498c-5.005 8.669 1.251 19.506 11.261 19.506h89.292a27.863 27.863 0 0 0 24.132-13.933l91.986-159.322a27.868 27.868 0 0 0 0-27.865l-44.647-77.329Z"
      fill="#FF394A"
    />
  </svg>
);

export const AVAXShape = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={194}
    height={194}
    viewBox="0 0 194 194"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M194 97c0 53.572-43.428 97-97 97S0 150.572 0 97 43.428 0 97 0s97 43.428 97 97ZM69.513 135.601H50.688c-3.956 0-5.91 0-7.101-.762a4.783 4.783 0 0 1-2.169-3.742c-.071-1.405.906-3.121 2.86-6.553l46.481-81.93c1.978-3.478 2.979-5.218 4.242-5.861a4.796 4.796 0 0 1 4.337 0c1.263.643 2.263 2.383 4.241 5.862l9.556 16.68.048.085c2.137 3.733 3.22 5.625 3.693 7.612a14.155 14.155 0 0 1 0 6.624c-.477 2.002-1.549 3.908-3.717 7.697l-24.416 43.16-.063.11c-2.15 3.763-3.24 5.671-4.75 7.11a14.206 14.206 0 0 1-5.79 3.36c-1.979.548-4.195.548-8.627.548Zm47.539 0h26.974c3.98 0 5.982 0 7.174-.785a4.77 4.77 0 0 0 2.168-3.765c.069-1.36-.887-3.01-2.76-6.242l-.195-.336-13.511-23.114-.154-.26c-1.899-3.21-2.857-4.832-4.088-5.459a4.745 4.745 0 0 0-4.313 0c-1.239.644-2.24 2.335-4.217 5.743l-13.464 23.114-.046.08c-1.971 3.402-2.956 5.102-2.885 6.497.095 1.525.882 2.931 2.168 3.765 1.168.762 3.17.762 7.149.762Z"
      fill="currentColor"
    />
  </svg>
);

export default SvgComponent;
