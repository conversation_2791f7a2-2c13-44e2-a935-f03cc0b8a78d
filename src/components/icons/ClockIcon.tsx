import { SVGProps } from 'react';

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={15}
    height={15}
    viewBox="0 0 15 15"
    fill="currentColor"
    {...props}
  >
    <rect width={15} height={15} rx={7.5} fill="currentColor" />
    <path
      d="M6 5v4h4"
      stroke="#fff"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default SvgComponent;


export const ClockOutlineIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    viewBox="0 0 24 24" 
    fill="none" 
    {...props}
  >
    <path fillRule="evenodd" clipRule="evenodd" d="M12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3ZM1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12Z" fill="#1B1E21"/>
    <path fillRule="evenodd" clipRule="evenodd" d="M13 6V11.382L17.3416 13.5528L16.4472 15.3416L11.5528 12.8944C11.214 12.725 11 12.3788 11 12V6H13Z" fill="currentColor"/>
  </svg>
);
