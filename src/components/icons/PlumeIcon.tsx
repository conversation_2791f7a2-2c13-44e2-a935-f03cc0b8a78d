import { SVGProps } from 'react';

const SvgComponent = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={64}
    height={64}
    viewBox="0 0 64 64"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect width={64} height={64} rx={32} fill="#FF3D00" />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16.272 16.486v12.387l4.422 4.427V20.913l12.326.042-4.42-4.43-12.325-.04-.003.001Zm22.425 9.908v11.184l-4.422-4.427V21.967l4.422 4.427ZM26.37 38.742h11.166l-4.42-4.43H21.949l4.42 4.43Zm18.612 5.075V32.688l-4.422-4.427v12.387l-12.325-.041 4.42 4.429 11.146.037 2.35 2.355 1.506.215-.287-1.436-2.388-2.392Z"
      fill="#fff"
    />
  </svg>
);

export default SvgComponent;
