import { cn } from '@/lib/utils';
import {
  ControllerP<PERSON>,
  ControllerRenderProps,
  FieldPath,
  FieldValues,
  useController,
} from 'react-hook-form';
import { Checkbox } from './checkbox';
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from './form';
import { Input } from './input';
import {
  ButtonSelectTrigger,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from './select';
import { Textarea } from './textarea';
import { ChangeEvent, useEffect, useRef } from 'react';
import { Uploader } from './uploader';

interface RowLayoutProps {
  children?: React.ReactNode;
  className?: string;
}

export const RowLayout = ({ children, className }: RowLayoutProps) => {
  return (
    <div className={cn('bl-grid bl-grid-cols-1 md:bl-grid-cols-2 bl-gap-8', className)}>
      {children}
    </div>
  );
};

interface CommonFieldProps {
  label?: string | React.ReactNode;
  description?: string | React.ReactNode;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  required?: boolean;
}

type FieldActions<T> = React.ReactNode | ((data: { field: T }) => React.ReactNode);

interface SingleCheckboxFieldProps extends CommonFieldProps {}

export const SingleCheckboxField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  label,
  description,
  disabled,
  className,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, 'render'> &
  SingleCheckboxFieldProps & {
    actions?: FieldActions<ControllerRenderProps<TFieldValues, TName>>;
  }) => {
  return (
    <FormField
      {...props}
      render={({ field }) => (
        <FormItem
          className={cn('bl-flex bl-flex-row bl-items-start bl-space-x-3 bl-space-y-0', className)}
        >
          <FormControl>
            <Checkbox checked={field.value} onCheckedChange={field.onChange} disabled={disabled} />
          </FormControl>
          <div className="bl-space-y-1 bl-leading-none">
            {label && <FormLabel>{label}</FormLabel>}
            {description && <FormDescription>{description}</FormDescription>}
          </div>
        </FormItem>
      )}
    />
  );
};

interface TextFieldProps extends CommonFieldProps {
  inputClassName?: string;
  type?: 'text' | 'number' | 'password' | 'email' | 'file';
  autoComplete?: string;
  spellCheck?: boolean;
  pattern?: string;
}

export const TextField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  label,
  type = 'text',
  description,
  placeholder,
  autoComplete,
  spellCheck,
  disabled,
  className,
  required,
  inputClassName,
  pattern,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, 'render'> &
  TextFieldProps & {
    actions?: FieldActions<ControllerRenderProps<TFieldValues, TName>>;
  }) => {
  const input = useRef<HTMLInputElement>(null);
  useEffect(() => {
    const element = input.current;
    if (!element) {
      return;
    }
    if (type !== 'number') {
      return;
    }
    const handler = (e: WheelEvent) => {
      if (document.activeElement === element) {
        e.preventDefault();
      }
    };
    element.addEventListener('wheel', handler, { passive: false });
    return () => {
      element.removeEventListener('wheel', handler);
    };
  }, [type]);

  const { field } = useController(props);
  const re = pattern ? new RegExp(pattern) : undefined;
  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (re) {
      if (re.test(e.target.value)) {
        field.onChange(e);
      } else {
        e.preventDefault();
      }
    } else {
      field.onChange(e);
    }
  };

  return (
    <FormField
      {...props}
      render={({ field }) => (
        <FormItem className={className}>
          {label && (
            <FormLabel>
              {label} {required && <span className="bl-font-mono">(*)</span>}
            </FormLabel>
          )}
          <FormControl>
            <Input
              {...field}
              onChange={handleChange}
              disabled={disabled}
              type={type}
              placeholder={placeholder}
              autoComplete={autoComplete}
              spellCheck={spellCheck}
              className={inputClassName}
              ref={input}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

interface TextAreaFieldProps extends TextFieldProps {
  rows?: number;
  autoResize?: boolean;
}

export const TextAreaField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  label,
  description,
  placeholder,
  autoComplete,
  spellCheck,
  disabled,
  className,
  inputClassName,
  rows,
  autoResize,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, 'render'> &
  TextAreaFieldProps & {
    actions?: FieldActions<ControllerRenderProps<TFieldValues, TName>>;
  }) => {
  const ref = useRef<HTMLTextAreaElement>(null);
  const handleInput = (e: ChangeEvent<HTMLTextAreaElement>) => {
    if (autoResize && ref.current) {
      ref.current.style.height = 'auto';
      ref.current.style.height = `${e.target.scrollHeight}px`;
    }
  };
  return (
    <FormField
      {...props}
      render={({ field }) => (
        <FormItem className={className}>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <Textarea
              {...field}
              disabled={disabled}
              placeholder={placeholder}
              autoComplete={autoComplete}
              spellCheck={spellCheck}
              className={inputClassName}
              rows={rows}
              ref={ref}
              onInput={handleInput}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export interface SelectOption {
  label: string;
  value: string;
}

interface SelectFieldProps<TOption extends SelectOption> extends CommonFieldProps {
  options: TOption[];
  item?: (option: TOption) => React.ReactNode;
}

export const SelectField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
  TOption extends SelectOption = SelectOption,
>({
  label,
  description,
  placeholder,
  options,
  item,
  className,
  triggerVariant = 'default',
  buttonSize = 'lg',
  buttonClassName,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, 'render'> &
  SelectFieldProps<TOption> & {
    triggerVariant?: 'button' | 'default';
    buttonSize?: 'sm' | 'md' | 'lg';
    buttonClassName?: string;
  }) => {
  return (
    <FormField
      {...props}
      render={({ field }) => (
        <FormItem className={className}>
          {label && <FormLabel>{label}</FormLabel>}
          <Select onValueChange={field.onChange} value={field.value}>
            <FormControl>
              {triggerVariant === 'button' ? (
                <ButtonSelectTrigger
                  buttonSize={buttonSize}
                  buttonClassName={buttonClassName}
                  disabled={field.disabled}
                >
                  <SelectValue placeholder={placeholder} />
                </ButtonSelectTrigger>
              ) : (
                <SelectTrigger disabled={field.disabled}>
                  <SelectValue placeholder={placeholder} />
                </SelectTrigger>
              )}
            </FormControl>
            <SelectContent>
              {options.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {item ? item(option) : option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

interface UploadFieldProps extends CommonFieldProps {
  accept?: string;
}

export const UploadField = <
  TFieldValues extends FieldValues = FieldValues,
  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,
>({
  label,
  description,
  placeholder,
  className,
  accept,
  ...props
}: Omit<ControllerProps<TFieldValues, TName>, 'render'> & UploadFieldProps) => {
  return (
    <FormField
      {...props}
      render={({ field }) => (
        <FormItem className={className}>
          {label && <FormLabel>{label}</FormLabel>}
          <FormControl>
            <Uploader
              accept={accept}
              placeholder={placeholder}
              value={field.value}
              onChange={field.onChange}
              disabled={field.disabled}
            />
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export type AmountInputProps = {
  className?: string;
  value: string;
  placeholder?: string;
  onChange?: (value: string) => void;
  validator: (value: string) => boolean;
};

export function AmountInput({
  className,
  value,
  onChange,
  placeholder,
  validator,
}: AmountInputProps) {
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    if (value === '.') {
      value = '0.';
    }
    const valid = validator(value);
    if (!valid) return false;
    onChange?.(value);
  };

  return (
    <input
      value={value}
      className={cn(
        'bl-text-4xl bl-bg-transparent bl-text-white placeholder:bl-text-indicator focus-visible:bl-outline-none',
        className,
      )}
      onChange={handleInputChange}
      spellCheck={false}
      placeholder={placeholder}
      autoComplete="off"
    />
  );
}
AmountInput.displayName = 'AmountInput';
