import React from 'react';

export function DynamicIcon({
  icon,
  className,
}: {
  icon?:
    | React.ComponentType<React.SVGProps<SVGSVGElement>>
    | React.ComponentType<React.ImgHTMLAttributes<HTMLImageElement>>
    | string;
  className?: string;
}) {
  if (!icon) return null;
  if (icon instanceof Function) {
    const Comp = icon;
    return <Comp className={className} />;
  }
  return <img src={icon} alt="icon" className={className} />;
}
