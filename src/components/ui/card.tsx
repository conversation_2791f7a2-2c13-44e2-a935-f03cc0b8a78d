import * as React from 'react';

import { cn } from '@/lib/utils';

const Card = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        'bl-border bl-border-card-border bl-bg-card-background bl-text-white',
        className,
      )}
      {...props}
    />
  ),
);
Card.displayName = 'Card';

const CutCornerCard = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    containerClassName?: string;
  }
>(({ className, children, containerClassName, ...props }, ref) => {
  return (
    <div className={cn('bl-relative bl-w-fit', containerClassName)}>
      <div
        ref={ref}
        className={cn(
          'bl-bg-[#111] bl-corner-cutout bl-corner-cutout-tl-4 bl-corner-cutout-br-4 bl-relative bl-z-10',
          className,
        )}
        {...props}
      >
        {children}
      </div>
      <div className="bl-absolute bl-w-[calc(100%+2px)] bl-h-[calc(100%+2px)] bl-bg-card-border bl-text-white bl-corner-cutout bl-corner-cutout-tl-4 bl-corner-cutout-br-4 -bl-top-px -bl-left-px"></div>
    </div>
  );
});
CutCornerCard.displayName = 'CutCornerCard';

const CardHeader = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('bl-flex bl-flex-col bl-space-y-1.5 bl-p-9', className)}
      {...props}
    />
  ),
);
CardHeader.displayName = 'CardHeader';

const CardTitle = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('bl-text-2xl bl-font-semibold bl-leading-none bl-tracking-tight', className)}
      {...props}
    />
  ),
);
CardTitle.displayName = 'CardTitle';

const CardDescription = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('bl-text-sm bl-text-muted-foreground', className)} {...props} />
  ),
);
CardDescription.displayName = 'CardDescription';

const CardContent = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('bl-p-9 bl-pt-0', className)} {...props} />
  ),
);
CardContent.displayName = 'CardContent';

const CardFooter = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div ref={ref} className={cn('bl-flex bl-items-center bl-p-6 bl-pt-0', className)} {...props} />
  ),
);
CardFooter.displayName = 'CardFooter';

export { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent, CutCornerCard };
