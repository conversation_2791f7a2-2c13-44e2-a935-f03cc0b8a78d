import { providers } from 'ethers';
import { BTCWallet, BinanceBitcoinProvider } from '@/wallets/bitcoin/types';
import type { AppLoadContext as OriginalAppLoadContext } from '@remix-run/cloudflare';
import 'react';

type ExternalProvider = providers.ExternalProvider;
declare module 'react' {
  interface CSSProperties {
    [key: `--${string}`]: string | number;
  }
}

interface TelegramWebviewProxy {
  postEvent: (eventName: string, data?: any) => void;
  close: () => void;
  ready: () => void;
  expand: () => void;
  setHeaderColor: (color: string) => void;
}

declare global {
  interface OKXWallet extends ExternalProvider {
    bitcoin: BTCWallet;
  }
  interface BitgetWallet extends ExternalProvider {
    unisat: BTCWallet;
  }
  interface BybitWallet extends ExternalProvider {
    bitcoin: BTCWallet;
  }
  interface TokenPocketWallet extends ExternalProvider {
    ethereum?: ExternalProvider;
    bitcoin?: BTCWallet;
  }
  interface XverseWallet extends ExternalProvider {
    BitcoinProvider?: BTCWallet;
  }

  interface GateWallet extends ExternalProvider {
    bitcoin: BTCWallet;
    ethereum: ExternalProvider;
  }
  interface FoxWallet extends ExternalProvider {
    bitcoin: BTCWallet;
    ethereum: ExternalProvider;
  }
  interface BinanceWallet extends ExternalProvider {
    bitcoin: BinanceBitcoinProvider;
  }
  interface SafepalProvider extends ExternalProvider {
    isSafePal: boolean;
  }

  interface Window {
    ethereum?: ExternalProvider & {
      isSafePal?: boolean;
    };
    unisat?: BTCWallet;
    okxwallet?: OKXWallet;
    bitkeep?: BitgetWallet;
    bybitWallet?: BybitWallet;
    binancew3w?: BinanceWallet;
    tokenpocket?: TokenPocketWallet;
    gatewallet?: GateWallet;
    foxwallet?: FoxWallet;
    TelegramWebviewProxy?: TelegramWebviewProxy;
    XverseProviders?: XverseWallet;
    safepalProvider: SafepalProvider;
    safepalBRC20Provider: BTCWallet;
    gtag: (option: string, gaTrackingId: string, options: Record<string, unknown>) => void;
  }
}

// declare module '@remix-run/cloudflare' {
//   interface AppLoadContext extends OriginalAppLoadContext {
//     cloudflare: {
//       env: Record<string, string>;
//     };
//   }
// }
