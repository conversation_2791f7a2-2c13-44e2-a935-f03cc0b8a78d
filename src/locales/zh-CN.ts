export default {
  title: 'Bitlayer',
  meta: {
    description: '首个基于BitVM范式的Bitcoin二层网络',
  },
  common: {
    readMore: '更多信息',
    learnMore: '了解更多',
    upload: '上传',
    submit: '提交',
    return: '返回',
    send: '发送',
    back: '后退',
    coming: '即将上线',
    connect: '连接钱包',
    disconnect: '断开连接',
    switchChain: '切换至 {{chainName}}',
    insufficientBalance: '余额不足',
    search: '搜索',
    toTop: '返回顶部',
    getGas: '获取Bitlayer Gas',
    bridge: '跨链 & 赚取收益',
    connectDesc: '连接您的钱包以转移代币',
    MesonDes: '安全、无成本 & 即时的稳定币跨链',
    OwltoDes: 'Owlto Finance 是一个以意图为中心的互操作性协议',
    OrbiterDes: 'Orbiter Finance 是一个去中心化的跨 Rollup 桥',
    BoolBridgeDes: 'Bool Bridge 是比特币和2层网络的去中心化跨链桥。',
    OmnityBridgeDes: 'Omnity 搭建稳固桥梁，操作便捷，帮助比特币持有者顺利完成链上交易。',
    miniBridgeDes:
      'Minibridge 是一个 “0费率” 的跨链工具，并且具有BTC/EVM/非EVM等多链支持和快速到账等优点。',
    login: '登录',
    noData: '无数据',
    confirm: '确认',
    Ok: '确认',
    cancel: '取消',
    reject: '拒绝',
    website: 'Website',
    share: 'Share',
    more: '更多',
    less: '更少',
    flash: '闪电跨入',
    getIt: '获取',
    congratulations: '恭喜！',
    error: {
      gasLess: 'BTC 余额不足',
      transactionFailed: '交易失败',
    },
  },
  resources: {
    github: 'https://github.com/bitlayer-org',
    discord: 'https://discord.gg/bitlayer',
    x: 'https://twitter.com/BitlayerCN',
    linkedin: 'https://bit.ly/42B6v15',
    telegram: 'https://t.me/bitlayerCN',
    medium: 'https://medium.com/@Bitlayer',
    rankRule: 'https://medium.com/@Bitlayer/d157b77106c1',
  },
  navigation: {
    links: {
      opvm: 'OpVM',
      developers: '开发者',
      users: '用户',
      userCenter: '用户中心',
      resources: '资源',
      contact: '联系我们',
      mainnet: '主网',
      bridgeEarn: 'Bridge & Earn',
      usdcChange: 'USDC 变更',
      gas: '获取Gas',
      startBuilding: '开始构建',
      btrScan: 'Bitlayer(BTR) 浏览器',
      addMainnet: '添加 Bitlayer 主网',
      testnet: '测试网',
      faucet: '水龙头',
      testnetBridge: '测试网跨链桥',
      testnetBridgeHint: 'Bitlayer Testnet 跨链桥',
      testnetScan: '测试网浏览器',
      addTestnet: '添加 Bitlayer 测试网',
      language: '语言',
      getGas: '获取 Bitlayer Gas',
      readyPlayerOne: '头号玩家',
      luckyHelmet: '幸运头盔',
      dAppsLeaderboard: 'Dapps 排行榜',
      hint1: '价值 5000 万美元的代币空投',
      hint2: '成为赛车手，在 Bitlayer 赚取收益',
      hint3: '为您喜爱的项目投票',
      brandKit: '品牌套件',
      whitepaper: '白皮书 2.0',
      leaderboard: '榜单竞赛',
      dappCenter: '生态应用中心',
      dappCenterHint: 'Bitlayer DApp 排行榜',
      miningGala: 'Mining Gala',
      bridge: '跨链桥',
      flash: '闪电跨入',
      developersSection: {
        hint: '开始在Bitlayer Chain构建',
        documentation: '文档',
        documentationHint: '面向开发人员的支持文档',
        tools: {
          title: '开发者工具',
          hint: '项目开发工具',
          mainnetScan: '主网浏览器',
          mainnetScanHint: 'Bitlayer(BTR)主网浏览器',
          testnetScan: '测试网浏览器',
          testnetScanHint: 'Bitlayer(BTR)测试网浏览器',
          faucet: '水龙头',
          faucetHint: 'Bitlayer Chain上获取试点代币',
          theGraph: 'The Graph',
          theGraphHint: '在Bitlayer上索引和搜索数据',
          multisigWallet: '多签钱包',
          multisigWalletHint: '安全易用的私钥存储',
        },
        security: {
          title: '安全',
          hint: '安全支持',
          dappSecurityManual: 'Dapp安全手册',
          dappSecurityManualHint: '构建安全Dapp的教程',
          auditAlliance: '审计联盟',
          auditAllianceHint: '审计联盟',
          securityNetwork: '安全网络',
          securityNetworkHint: '安全网络',
        },
        developerSupport: {
          title: '运营支持',
          hint: '资助、资金、激励和支持',
          readyPlayerOne: 'Ready player I',
          readyPlayerOneHint: '交易赚取高达$100,000的奖金池',
          luckyHelmet: '幸运头盔',
          luckyHelmetHint: '为早期开发者解锁$50,000,000',
          miningGala: '挖矿盛会',
          miningGalaHint: '赢取Bitlayer奖励和dapp空投',
          leaderboard: '排行榜',
          leaderboardHint: 'Bitlayer排行榜竞赛第一期',
        },
        hackathon: '黑客松',
        hackathonHint: '开发人员支持文档',
        github: 'Github',
        githubHint: '探索Bitlayer Chain技术资源库',
        devCommunities: '开发者社区',
        devCommunitiesHint: '加入开发者聊天',
        telegram: '电报',
        opvm: 'OpVM',
        opvmHint: '让比特币拥有验证能力',
      },
    },
    footer: {
      Participate: '参与',
      ReadyPlayerOne: '头号玩家',
      Build: '构建',
      BuildingBitlayer: '在 Bitlayer 上构建',
      Faucet: '水龙头',
      SupportedWallets: '支持的钱包',
      TheGraph: 'The Graph',
      MainScan: 'Bitlayer(BTR) 浏览器',
      TestScan: '测试网浏览器',
      About: '关于',
      Solutions: '解决方案',
      Roadmap: '路线图',
      EventNews: '活动与新闻',
      Jobs: '工作机会',
      Community: '社区',
      blog: '博客',
      Podcast: '播客',
      Operation: '操作',
    },
    walletDrawer: {
      wrongNet: '请切换至{{chainName}}',
      noNFT: '暂无 NFT',
    },
  },
  pages: {
    home: {
      title: {
        notice: '头号玩家现已上线，立即投票，赢取积分&白名单！',
        slogon1: 'MAKE',
        slogon2: 'BITCOIN',
        slogon3: 'VERIFIABLE',
        powering: '赋能比特币去中心化金融',
        explore: '探索 BitVM 桥',
        btrBridge: 'Bitlayer 桥',
        extra: '跨链到 Bitlayer 网络',
        the: 'THE',
        first: 'FIRST',
        bitvm: 'BITVM',
        startBuilding: '开启构建',
        bridgeEarn: '跨链 & 赚取收益',
        bridge: '跨链桥',
        competition: 'Bitlayer 应用中心',
        dapp100:
          "探索<span class='bl-text-xs md:bl-text-2xl bl-text-primary bl-font-[600]'>100+</span> 生态应用，赢取奖励！",
        gasFee: '平均Gas费：',
        whitePaper: 'Bitlayer 白皮书',
        leaderboard:
          "立即查看你的<span class='bl-text-xs md:bl-text-2xl bl-text-white bl-font-[600] bl-px-1'> 宝石空投 </span> 资格！",
      },
      spotlight: {
        spotlight: '焦点信息',
        readMore: '查看更多',
        slideshow: {
          item1: {
            type: 'Blog',
            title: 'BitVM 桥',
            content: '比特币 DeFi 的门户。',
          },
          item2: {
            type: '博客',
            title: 'Bitlayer V2',
            content: '基于BitVM范式的Bitcoin二层网络。',
          },
          item3: {
            type: '空投活动',
            title: '幸运头盔福利',
            content: '幸运头盔用户专属 $BTR 奖励。',
          },
          item4: {
            type: '空投活动',
            title: '幸运赛车抽卡',
            content: '抽卡组装赛车，空投收益回报率最高可达200%。',
          },
          item5: {
            type: '空投活动',
            title: 'Bitlayer活动中心',
            content: '庆祝 Bitlayer 活动中心上线，独家空投高达 100,000,000 Bitlayer 积分。',
          },
          item6: {
            type: '空投活动',
            title: 'Bitlayer 用户中心',
            content: 'Bitlayer 用户中心现已上线！探索任务和小游戏，赢取 Bitlayer 宝石和积分！',
          },
        },
      },
      liveData: {
        liveData: '实时数据',
        explore: '探索',
        'the community': 'Bitlayer',
        'of bitlayer': '社区',
      },
      solutions: {
        solutions: '解决方案',
        version: '愿景',
        infrastructure: '终极比特币DEFI基础设施',
        subtitle: '未来比特币生态系统的关键支柱性方案',
        items: [
          {
            title: '继承比特币安全性',
            text: '通过 BitVM 继承比特币安全性',
          },
          {
            title: '零信任桥',
            text: '结合 DLC 和 BitVM，带来超越传统多重签名的创新模型',
          },
          {
            title: '实时VMs',
            text: '支持多 VMs，实现 100% EVM 兼容环境',
          },
        ],
        whitepaper: {
          title: '下载白皮书',
          button: '白皮书',
        },
      },
      bitvm: {
        bitvm: 'BitVM',
        what: {
          title: '什么是 BitVM',
          describe: 'BitVM 是一种革命性范式，采用乐观验证方案，在比特币上实现图灵完备的合约。',
        },
        how: {
          title: 'BitVM 的工作原理是什么',
          describe:
            'BitVM 利用预签名交易图在比特币上模拟智能合约，通过乐观方式验证零知识证明，实现对链下计算的验证。',
        },
      },
      bitvmBridge: {
        bitvmBridge: 'BitVM 桥',
        title: '比特币 DeFi 的门户通道',
        items: [
          {
            title: '安全',
            subtitle: '- 信任最小化 -',
            text: '通过系统中角色的“存在性诚实性”，可以实现最佳的信任假设（即 N 个参与者中只需 1 个诚实者）。',
          },
          {
            title: '完全可编程性',
            subtitle: '-  Peg-BTC (YBTC) -',
            text: 'Peg-BTC (YBTC)  是一种完全可编程资产，借助 BitVM Bridge 的多链支持，可无缝集成到各种可编程环境中。',
          },
          {
            title: '产生收益',
            subtitle: '- 可持续收益 -',
            text: '通过原生或集成的收益产品，为资产持有者提供更多增值渠道。',
          },
        ],
      },
      bitlayerNetwork: {
        bitlayerNetwork: 'Bitlayer Network',
        title: '比特币 DeFi 引擎',
        items: [
          {
            title: '比特币同等安全保障',
            subtitle: '- 比特币 Rollup -',
            text: '结合 BitVM 智能合约与零知识证明，在比特币一层上实现最终确定性。',
          },
          {
            title: '最小化信任桥',
            subtitle: '- BitVM Bridge -',
            text: 'BitVM Bridge 实现了最优的信任假设（1-of-N）。',
          },
          {
            title: '实时EVM',
            subtitle: '- RtEVM -',
            text: '提供几乎实时的EVM性能，配备前沿的并行执行引擎和区块链专用数据库。',
          },
        ],
      },
      innovations: {
        innovations: '创新',
        items: [
          {
            title: '继承比特币安全性',
            subtitle: '- Bitcoin Rollup -',
            text: '基于BitVM智能合约和零知识证明, 以实现Bitcoin Finality',
          },
          {
            title: '信任最小化桥',
            subtitle: '- BitVM跨链桥 -',
            text: '由BitVM技术驱动的信任最小化BTC桥，作为现代BTCFi的基石',
            imageDesc:
              '结合原子交换，BTC可以在比特币一层和二层之间无信任的跨链转移，取代陈旧的多签方式',
          },
          {
            title: '实时EVM',
            subtitle: '- RtEVM -',
            text: '提供几乎实时的EVM性能，配备前沿的并行执行引擎和区块链专用数据库',
            imageDesc1: '调度器将交易乐观调度到多个EVM执行器，充分利用多线程',
            imageDesc2:
              '将多个KV存储分片挂接到一个可扩展的MPT下，实现几乎无限的存储容量和极快的状态访问',
          },
        ],
      },
      roadmap: {
        roadmap: '路线图',
        title: '比特币的旅程',
        title2: 'Bitlayer 的旅程',
        achieved: '（已完成）',
        staging: '（准备中）',
        bridge: {
          points: [
            {
              title: 'BitVM Bridge 测试网发布',
              description: 'Mint功能',
              date: 'DEC.2024',
            },
            {
              title: '新功能发布',
              description: 'Unmint铸造、Reclaim流程',
              date: 'FEB.2025',
            },
            {
              title: '主网发布',
              date: 'May 2025',
            },
            {
              title: '支持更多链的接入',
              date: '2025 H2',
            },
          ],
        },
        network: {
          points: [
            {
              title: 'Bitlayer PoS，奠定基础',
              description: '安全模型结合 PoS 与多签机制<br/>实现跨链功能<br/>兼容 EVM',
              date: 'April 2024',
            },
            {
              title: 'Bitlayer Rollup，基于 BitVM',
              description: '链上 STF 验证<br/>最小信任化的 BTC 桥<br/>无缝迁移',
              date: 'Q1/Q2 2025',
            },
            {
              title: 'Bitlayer Rollup，性能全面强化',
              description: '无与伦比的性能<br/>闪电般的确认速度',
              date: 'Q4 2025',
            },
          ],
        },
        journeys: [
          {
            title: 'BITLAYER MAINNET V1',
            texts: ['Bitlayer PoS'],
            subtitle: 'Apr, 2024',
          },
          {
            title: 'BITLAYER MAINNET V2',
            texts: ['Bitlayer Rollup,BitVM based', 'BitVM Bridge', 'Bitcoin Rollup'],
            subtitle: 'Q1/Q2, 2025',
          },
          {
            title: 'BITLAYER MAINNET V3',
            texts: ['Bitlayer Rollup,Super Charged', 'Realtime EVM'],
            subtitle: 'Q4, 2025',
          },
        ],
      },
      events: {
        tag: '活动与新闻',
        title: 'Bitlayer生态活动与新闻',
        subtitle: '关注我们最新进展',
        items: [
          'Bitcoin 2024 Nashville',
          'Korea Blockchain Week',
          'Token 2049',
          'Bitlayer Night Korea',
        ],
      },
      jobs: {
        tag: '工作机会',
        title: '职位空缺',
        subtitle: '让我们一起创造比特币历史！',
        positions: [
          {
            title: '开发者关系负责人',
            skills: [
              '负责拓展和维护公链生态的开发者关系',
              '处理与全球开发者技术社区、生态中的技术交流和开发者教育',
            ],
          },
          {
            title: '协议研究员',
            skills: ['基于行业痛点进行区块链前沿研究', '撰写高质量的研究报告和论文'],
          },
        ],
        more: '探索职位空缺',
      },
      community: {
        tag: '社区',
        title: '加入社区',
        subtitle: '与 Bitlayer 互动',
        items: [
          {
            title: '加入 Bitlayer 开发者队伍',
            description: '与我们一同构建',
          },
          {
            title: '加入 Discord',
            description: '参与讨论与交流',
          },
          {
            title: '关注官方推特',
            description: '时刻获取最新进展',
          },
          {
            title: '在领英上联系我们',
            description: '与我们联系',
          },
          {
            title: '加入我们的电报群',
            description: '获取帮助和资讯',
          },
          {
            title: '关注Medium更新',
            description: '获取更多信息和指南',
          },
        ],
      },
      investors: {
        investors: '投资者',
        'Co-lead': '领投',
      },
      ecosystem: {
        ecosystem: '生态系统',
        top: 'Bitlayer链上',
        Dapps: '的应用',
        on: '在',
        Bitlayer: 'Bitlayer',
        'Dapp Center': '生态应用中心',
        lending: '借贷',
        dex: '交易所',
        staking: '质押',
        gaming: '链游',
        'stable coins': '稳定币',
      },
    },
    developers: {
      title: {
        titleLine1: '欢迎来到',
        titleLine2: 'Bitlayer 文档',
        subtitle: '由构建者为加入 Bitlayer 生态的构建者编写的手册',
        buildNow: '立即构建',
        blog: '博客',
      },
      faq: {
        title: '常见问题',
        name1: 'Bitlayer 是什么？',
        desc1:
          'Bitlayer 作为比特币的第二层解决方案，具有100%的 EVM 和以太坊工具链兼容性，BTC 作为原生代币（Gas 代币）。Bitlayer 允许应用和开发者以低成本从现有的以太坊生态系统迁移到 Bitlayer，无需进行大量修改或重写。',
        name2: 'Bitlayer与现有的比特币钱包兼容吗？',
        desc2:
          '是的，Bitlayer与 MetaMask、Unisat Wallet 或其他比特币/以太坊兼容钱包兼容，允许用户在Bitlayer网络上无缝地与他们的资金和资产互动。',
        name3: '开发者可以将现有项目迁移到Bitlayer吗？',
        desc3:
          '是的，Bitlayer通过提供EVM兼容性，支持现有开发者以低成本无缝迁移和部署现有项目。开发者可以轻松迁移用 Solidity、Vyper 或其他直接编译到 Bitlayer 的EVM字节码的语言编写的智能合约，支持使用您熟悉的工具链：以太坊JSON-RPC、Hardhat等。',
        name4: '我如何支持 Bitlayer？',
        desc4: `支持 Bitlayer 有几种方式。您可以积极参与社区讨论，提供反馈和建议，为平台上的应用程序或工具开发做出贡献，或向可能受益于其服务的他人推广 Bitlayer。此外，您可以探索 Bitlayer 可能设立的任何特定支持计划或项目。点击了解更多关于 <span data-link-index='0' class='bl-underline bl-cursor-pointer'>头号玩家</span> 的信息,这是一个为早期建设者和贡献者解锁5000万美元代币激励的计划。`,
      },
      feature: {
        title: '我们的优势',
        title1: '图灵完备性',
        desc1: '支持多 VMs，实现100% EVM 兼容',
        title2: '免信任的双向锚定',
        desc2: '综合 DLC 和 BitVM，带来超越传统多重签名的创新模型',
        title3: '比特币一层验证',
        desc3: '通过BitVM继承比特币安全',
      },
      quickstart: {
        title: '快速开始',
        subtitle: '由构建者为构建者量身打造的指南，解锁 Bitlayer 生态',
        title1: '将您的钱包连接到 Bitlayer 测试网',
        skill1: '将 Bitlayer 测试网配置添加到您的钱包，并与测试网上的 Dapps 互动',
        title2: '编译、运行和部署',
        skill2: '本指南将指导您完成 Bitlayer 的编译、运行和部署',
        title3: '新观点与新动态',
        skill3: '敬请关注我们博客中关于技术和市场洞察的更新',
        readMore: '了解更多',
      },
      intro: {
        title: 'TRACK PACK',
        subtitle: '加入Bitlayer生态系统的开发者指南：由开发者为开发者创建。',
        buildNow: '立即构建',
      },
      tools: {
        title: '开发者工具',
        subtitle: '利用Bitlayer生态系统中的工具套件，最大限度地发挥您在Bitlayer Chain上的潜力',
        name1: '水龙头',
        desc1: '每24小时获取您的Bitlayer测试网代币用于开发。',
        name2: '主网浏览器',
        desc2: `探索和分析Bitlayer主网上的区块链数据的必要工具。`,
        name3: '多签钱包',
        desc3: '通过去中心化的多签名者保护您的资产安全。',
        name4: 'The Graph',
        desc4: '在Bitlayer上索引和访问实时区块链数据。',
        website: '网站',
        more: '探索更多工具',
      },
      security: {
        title: '安全',
        subtitle: '使用安全套件快速构建安全可靠的应用程序',
        name1: 'Dapp安全手册',
        desc1: `通过遵循安全手册，提高应用程序安全模块的开发效率。`,
        buttonLabel1: '阅读文档',
        // name2: 'Audit alliance',
        name2: '安全网络',
        desc2: `快速与知名安全服务提供商对接，保障并提升您应用的价值。`,
        buttonLabel2: '阅读文档',
        name3: '开源工具',
        desc3: '使用开源安全工具快速进行自检。',
        buttonLabel3: '探索工具',
      },
      operationalSupport: {
        title: '运营支持',
        subtitle: '资助、资金、激励和支持',
        name1: 'Ready Player Grant',
        desc1: '解锁 Bitlayer 的潜力：寻找创新项目和卓越团队推动 BTC 生态系统发展。',
        name2: '激励计划',
        desc2: `Ready Player I——为早期开发者和贡献者提供 50,000,000 美元的激励。`,
        name3: '运营和市场资源',
        desc3: '官方资源支持 & 全球市场资源支持。排行榜、DApp 中心、Racer 中心。',
        name4: '生态增长活动',
        desc4: '挖矿盛会、Bitlayer 之声、全球加密货币会议。',
        participate: '参与',
        more: '更多支持',
      },
      cards: {
        name1: '快速入门',
        desc1:
          'Bitlayer是第一个为比特币提供的Layer 2解决方案，提供与比特币自身相当的安全性和图灵完备性。了解如何在其上构建dApps。',
        name2: 'Bitlayer架构',
        desc2: '开始使用Bitlayer网络并了解其独特特性。了解网络架构以及它如何与其他区块链不同。',
        name3: 'Bitlayer路线图',
        desc3: `Bitlayer的愿景将通过分阶段推出主网来实现，每个阶段都旨在增强用户体验。`,
        readDoc: '阅读文档',
      },
      connect: {
        title: '联系我们',
        subtitle: '了解 Bitlayer 社区的新闻和最新进展',
      },
    },
    bridge: {
      // transfer
      bridgeTitle: '跨链 & 赚取收益',
      gasTitle: '获取 Bitlayer Gas',
      gasPromotion: 'Quick Start - Get Bitlayer Gas in 1 Minute!',
      newToBridge: '新用户？',
      from: '从',
      to: '到',
      amount: '数量',
      balance: '余额: {{balance}}',
      available: '可用: {{balance}}',
      transferable: '可转余额',
      max: '最大',
      recipientAddress: '接收地址',
      recipientTip: '连接钱包接收代币',
      est: '预计: {{time}}',
      fee: '费用:',
      total: '总计:',
      receivepPlaceholder: '请输入 {{chainName}} 地址',
      transfer: '转账',
      transferProgress: '转账正在进行中',
      approve: '授权',
      approveInProgress: '等待您的确认',
      checkingAllowance: '正在查询授权额度',
      minLimit: '每笔交易最低 {{amount}} {{symbol}}',
      maxLimit: '每笔交易最高 {{amount}} {{symbol}}',
      invalidAmount: '金额无效',
      invalidAddress: '无效地址',
      switchChainDesc: '切换到{{chainName}}网络需要使用{{networkType}}钱包',
      transferFailed: '转账失败',
      amountExceed: '数额超限制',
      connectDesc: '连接您的钱包以转移代币',
      bridgeTip: '桥接到Bitlayer，持有Bitlayer幸运头盔，共享400K BWB积分',
      historyTab: '历史记录',
      getGas: '快速获取Gas',
      swap: '兑换',
      gasLimit: `余额≥{{maxBalance}}无法兑换`,
      dayLimit: `今日剩余的BTC为{{remainSwapAmount}}`,
      // history
      loadingData: '加载数据中',
      noData: '无数据',
      sender: '发送者',
      receiver: '接收者',
      transactionHash: '交易哈希',
      Pay: '您支付',
      gasPay: '付款地址',
      gasGet: '收款地址',
      Balance: '余额:',
      Max: '最大',
      SwitchToBitlayer: '切换 Bitlayer',
      Maximum: '每笔交易最多 {{maxSwapAmount}} BTC',
      History: '历史记录',
      Receive: '您收到',
      Fee: '费用:',
      Total: '总计:',
      Slippage: '滑点:',
      ThirdPartyBridge: '第三方跨链桥',
      ConfirmSwap: '确认兑换',
      ApproveToken: '批准代币',
      ApproveGas: '批准签名，无需支付 Gas',
      ConfirmSwapRadio: '确认兑换比例',
      ConfirmGas: '确认兑换比例，无需支付 Gas',
      Processing: '处理中...',
      Completed: '已完成',
      From: '从',
      To: '到',
      Return: '返回',
      thirdParty: {
        tipTitle: '温馨提示',
        tipContent: '目前官方EVM桥是Beta版本，小额跨链请先使用三方桥',
      },
      btcHint: '仅支持识别 BTC、Ordinal 和 Runes 协议资产',
      btcCaution: {
        title: '确认交易',
        message: '本次交易将使用以下 Inputs。请确认这些 Inputs 未携带其他资产。',
      },
      inscribeTransfer: '铭刻 可转余额',
      inscribedTitle: '交易已提交',
      inscribedTip: '铭刻完成后即可发起转账。',
      viewOnExplorer: '在浏览器中查看',
      refresh: '刷新',
      pending: '待确认',
      selectAll: '全选',
      maintenanceTip: '维护详情',
      underMaintenance: '系统维护中，敬请期待',
      usdc: {
        change: 'USDC 变更',
        changeLabel: '您的令牌变更：',
        changeDesc: '此令牌已根据 Circle 的原生智能合约更新。USDC 新合约地址：',
        confirm: '确认并领取',
        learnMore: '查看更多详细信息',
        bonusForYou: '获取 Bitlayer 奖励积分！',
        tips: '温馨提示',
        lendingDesc: '我们检测到您在借贷平台上有 USDC 贷款。请先偿还贷款。',
        borrowingTitle: 'USDC.e 借款情况',
        viewOn: '在 {{ platform }} 上查看',
        insufficentBalance: 'USDC 余额不足。请联系我们。',
      },
      errors: {
        invalidRequest: '无效的请求。',
        insufficentBtc:
          'BTC 余额不足或没有可用的 UTXO。如果您有未确认的交易，请等待交易确认后重试。',
        insufficentRune:
          'Rune 余额不足或没有可用的 UTXO。如果您有未确认的交易，请等待交易确认后重试。',
        insufficentBrc20:
          'BRC20 余额不足或没有可用的 UTXO。如果您有未确认的交易，请等待交易确认后重试。',
        buildTxFailed: '创建交易失败。',
        transferError: '转账失败。',
        internalError: '内部服务错误。',
        swapError: '兑换失败。',
        insufficientFunds: '余额不足：当前余额不足以支付链上Gas+转账数量',
        withdrawLimitReached:
          '最大跨出 {{ limit }} {{ token }}，当前剩余可跨 {{ available}} {{ token }}，每秒恢复 {{ recovery }} {{ token }}',
      },
    },
    faucet: {
      title: '测试网水龙头',
      description:
        '获取 Bitlayer 测试网代币进行开发，每24小时更新。测试网代币没有价值，无法以实际价格交易',
      selectField: {
        label: '选择 token',
        placeholder: '选择一个 token',
      },
      textFiled: {
        label: '钱包地址',
        placeholder: '请输入您的 Bitlayer 测试网地址',
      },
      result: {
        gotTip: '你得到了 {{tokens}}!',
        gotAnother: '您可以在24小时后再次请求 {{token}} 。',
        sending: '发送中...',
      },
      error: {
        testAddress: '请输入一个有效的 Bitlayer 测试网地址',
        verifyCaptcha: '请完成验证码验证',
        verifyFailed: '验证码验证失败',
        exceededLimit: '您已在最近24小时内请求过代币，请等待 {{h}}{{m}}{{s}} 后再试。',
        invalidAddress: '请输入有效的 Bitlayer 测试网地址',
        unexpectedError: '发生了意外的服务器错误，请稍后再试。',
      },
    },
    readyplayone: {
      title: '头号玩家',
      description: '为早期构建者和贡献者解锁5000万美元代币激励。',
      register: '报名',
      time: '2024年3月29日 - 2024年5月10日',
      airdrop: '单一项目最高可获100万美金激励！',
      Volume: '体积:',
      rules: {
        title: '规则',
        ruleContents: [
          {
            title: '注册',
            texts: ['确保在5月10日之前完成注册。'],
            date: '3月-5月',
          },
          {
            title: '上线',
            texts: ['在主网部署并为即将到来的竞赛做好准备。'],
            date: '4月-5月',
          },
          {
            title: '空投',
            texts: ['加入排行榜竞赛，赢取价值超过5000万美元的代币空投。'],
            date: '6月-7月',
          },
        ],
      },
      colead: '联合领投',
      investors: '投资者',
      partners: '合作伙伴',
      setp: {
        register: {
          schemaName: '请输入项目名称',
          schemaWebsite: '请输入项目网站',
          schemaProjectDemo: '请提供项目 Demo',
          schemaContact: '请输入联系人姓名',
          schemaEmail: '请输入电子邮件',
          schemaTelegram: '请输入项目电报',
          schemaAgree: '请同意条款和条件',
          newBuilderType: '初创团队',
          experiencedBuilderType: '成熟团队',
          formValidError:
            '我们在您的表单中发现了一些问题，请在重新提交前查看突出显示的字段并进行更正。',
          apiClientError: '我们在您的表单中发现了一些问题，请查看字段并尝试再次提交。({{code}})',
          apiServerError: '我们这边似乎暂时出现了故障，请刷新页面或几分钟后再试。({{code}})',
          formOne: {
            title: '项目信息',
            name: {
              label: '项目名称',
              placeholder: '请输入名称',
            },
            builderType: {
              label: '团队类型',
              placeholder: '请选择团队类型',
            },
            fundInfo: {
              label: '项目融资信息：（描述项目的融资状况。500个字符或提供详细链接。）',
              placeholder: '请输入融资情况',
            },
            projectDemo: {
              label: '项目Demo',
              placeholder: '请输入demo演示地址',
            },
            pitchDeck: {
              label: '项目融资演示文稿(仅支持PDF)',
              placeholder: '选择PDF文件上传',
            },
            twitter: {
              label: '项目X账号',
              placeholder: '请输入 ID',
            },
            website: {
              label: '项目网站',
              placeholder: '请输入 URL',
            },
            logo: {
              label: '项目 Logo <span>(建议使用1:1比例)</span>',
              placeholder: '选择文件上传',
            },
            description: {
              label:
                '项目描述/Deck：在500个字符内概述您的项目（目的、解决的问题、工作原理、项目规划）或提供 Deck 链接。',
              placeholder: '请输入描述',
            },
            category: {
              label: '您的项目属于哪个类别？',
              placeholder: '请选择',
              selects: [
                'Infra',
                'Defi',
                'Social',
                'Inscriptions',
                'NFT',
                'MeMe Token',
                'DAO',
                'Tooling',
                'Wallet',
                'Cross-chain',
                'Privacy computering',
                'Privacy Token',
                'Prediction makert',
                'Storage',
                'Gamefi',
                'Entertainment',
                'Metaverse',
                'Others',
              ],
            },
            stage: {
              label: '项目阶段',
              placeholder: '请选择',
              selects: ['Idea phase', 'In development', 'Prototype available', 'Live/Beta'],
            },
            coin: {
              label: '项目在Bitlayer部署的代币合约地址 <span>（如无请填N/A）</span>',
              placeholder: '请输入代币合约地址',
            },
            lockUp: {
              label: '项目在Bitlayer上的锁仓地址 <span>（如无请填N/A）</span>',
              placeholder: '请输入锁仓地址',
            },
            contract: {
              label: '项目在Bitlayer部署的交互合约地址 <span>（如无请填N/A）</span>',
              placeholder: '请输入交互合约地址',
            },
            defilama: {
              label: '项目Defillama链接 <span>（如无请填N/A）</span>',
              placeholder: '请输入Defillama链接',
            },
          },
          formTwo: {
            title: '团队信息',
            team: {
              label: '团队人数有多少？请列出主要团队成员的姓名、角色和背景。',
              placeholder: '请输入描述',
            },
          },
          formThree: {
            title: '联系人',
            name: {
              label: '名称',
              placeholder: '请输入名称',
            },
            email: {
              label: '电子邮件地址',
              placeholder: '请输入电子邮件地址',
            },
            telegram: {
              label: '电报',
              placeholder: '请输入 URL',
            },
            twitter: {
              label: 'X 个人资料',
              placeholder: '请输入 URL',
            },
          },
          consent: {
            title: '同意',
            label: '我同意本次黑客松的条款和条件、隐私政策和行为准则。',
          },
        },
        launch: {
          title: '恭喜您报名成功',
          documents: '开发者文档',
          deploy: '现在，您可以在Bitlayer上部署项目了',
          scanCode: '扫码加入社区',
          next: '下一步',
        },
        airdrop: {
          deploymentTip: '如果您的项目已经完成部署，恭喜您将可以参加初选！',
          description:
            '最终获胜者将获得1万至30万美元的资助额度，额外激励还包括3,000至5,000美元的荣誉奖励、以及100万美元的活动专属奖金池！',
        },
      },
      gems: {
        title: 'Bitlayer宝石空投',
        gems: '总宝石',
        tips: '即将分配',
        button: '检查空投资格',
        claimGems: {
          button: '领取宝石',
          label1: '您已经获得',
          label2: '宝石空投资格',
          'failed-title': '继续加油！',
          'label-failed': '您暂时没有宝石空投的领取资格，敬请关注后续活动！',
          'button-failed': '探索应用，赢取奖励',
        },
      },
      subTitle: 'Bitlayer开发者加速计划',
      descriptionV4: '公平透明，逐月审核，单个项目最高可获<text> 100万 </text> 美金激励！',
      incentives: {
        tag: '激励',
        'New Builder': {
          title: '初创团队',
          bonus: '特殊荣誉称号、<span>{{amount}}</span>称号奖励',
        },
        'Experienced builder': {
          title: '成熟团队',
          bonus: '<span>{{amount}}</span>专属活动奖池',
        },
        grants: '资助额度',
        bonus: '额外激励',
      },
      rulesV4: {
        tag: '规则',
        ruleContents: [
          {
            title: '报名',
            texts: [
              '每月对当月30日之前报名的项目进行评估',
              '30日之后报名的项目将进入次月评估序列',
              '请确保在报名时提供充分的项目信息',
            ],
            date: '逐月进行',
          },
          {
            title: '初选',
            texts: [
              'Bitlayer评审会将会根据报名信息滚动审核',
              '进一步审查将包括技术检查、市场研究和产品分析',
            ],
            date: '预计7天',
          },
          {
            title: '终选',
            texts: [
              'Bitlayer基金会将与特约的投资机构评委一起对通过初选的项目进行评估',
              '每个项目将进行10-15分钟的演讲',
              '获胜项目将获得10,000至300,000美元的资助额度',
            ],
            date: '预计14天',
          },
        ],
      },
      currentShortlist: {
        tag: '当期入围名单',
        primaryElection: '初选结果',
        finalElection: '终选结果',
        like: '点赞',
        grant: '资助额度',
        grantWinner: '获胜者',
      },
      updates: {
        tag: '近期更新',
      },
    },
    luckyhelmet: {
      title: '幸运头盔',
      description: '成为赛车手，在 Bitlayer 赚取收益。',
      toGet: '如何获取？',
      miningBtn: '铸造开放日期：2024年5月8日',
      assetTitle: '比特币一层的资产',
      assetDesc: '比特币一层上的原生 Ordinals 资产',
      mintTitle: '在比特币 L2 铸造',
      mintDesc: '与 L1 相比，更低的Gas费用和更好的流通性',
      desc1: '无缝的 L1/L2 体验',
      desc2: 'Bitlayer 的金铲子特权',
      minting: '铸造中',
      mint: '铸造',
      checkWhitelist: '查看白名单',
      minted: '已铸造',
      mintingSuccess: '铸造成功',
      congratulations: '祝贺你!',
      success: '您在白名单中',
      sorry: '很抱歉',
      failure: '您不在白名单中',
      finished: '已完成',
      whitelistOnly: '仅限白名单用户',
      trade: 'Trade Lucky Helmet',
    },
    rank: {
      head: {
        head1: '奖励池',
        head2: 'Dapps',
        head3: '总投票数',
        head4: '每日投票',
      },
      banner: {
        tag: '筹备阶段',
        title: '头号玩家',
        subtitle: '给构建者的代币空投',
        link: '了解规则？',
        all: '所有类别',
        switch: '切换至',
        register: '注册 Dapp',
      },
      list: {
        votes: '投票数',
        vote: '投票',
        out: '票数用尽',
        invite: '邀请获取分数',
      },
      carouse: {
        title: '我的 Ready Player One 积分',
        note: '请注意：头号玩家积分不等同于 Bitlayer 积分，它们未来会按照一定比例进行兑换。请关注我们获取最新信息',
        accepted: '邀请被接受',
        taskCenter: '任务中心',
        now: '立即关注',
        vote: '每次投票',
        invite: '每次邀请',
        follow: '在 X 上关注',
        hint1: '每天最多3次',
        hint2: '一次性任务',
      },
      dialog: {
        band: {
          title: '邀请朋友助力',
          sorry: '抱歉！您的X账户已与不同地址绑定，请使用另一个 X 账户',
        },
        invite: {
          need: '您需要先绑定一个 X 账户。成功邀请一个用户可获得500积分',
          auth: '在X上授权',
          attention: '注意：一个 X 账户只能绑定一个地址，以避免错误',
        },
        note: {
          please: '请注意：',
          note: '头号玩家积分不等同于 Bitlayer 积分，它们未来会按照一定比例进行兑换。请关注我们获取最新信息',
        },
        rules: {
          rules1: '每天完成3次投票可获取300积分',
          rules2: '关注官方 X 可获取500积分',
          rules3: '每成功邀请一个新用户可额外获取500积分',
          rules4: '随机抽取100个用户分发 Bitlayer 幸运头盔，积分排名越高，中奖机会越大',
          vote: '为您喜爱的项目投票',
          note: '参与 Bitlayer 人气榜投票，支持您喜爱的项目！活动时间为2024年4月23日至2024年5月10日。登录排行榜页面，每人每天有3次投票机会；通过关注官方X账户和邀请新用户可获取额外积分。积极参与者可累积头号玩家活动积分，并有机会获得Bitlayer幸运头盔白名单。',
          rewards: '奖励：',
          boost: `提升项目人气，影响榜单排名，积极参与有机会获得官方NFT奖励`,
          gotIt: '已了解',
        },
        share: {
          copy: '复制链接',
          share: '分享到',
          invite: '邀请投票',
          more: '分享好友获取更多投票',
          connect: '链接 X（推特）',
        },
        vote: {
          vote: '投票',
          voteFor: '投票给',
          daily: '每天可投3次',
          votes: '票数',
        },
        expire: {
          late: '抱歉，您来晚了!',
          expired: '投票活动现已结束',
          tuned: '未来我们将在官方推特上分享更多活动，敬请期待！',
          thx: '非常感谢！',
          follow: '一键关注',
        },
      },
    },
    getGas: {
      recipient: '接收地址',
      placeholder: '请输入一个 Bitlayer 地址',
      invalid: '无效地址',
      purchase: '支付',
      maintain: '系统升级中，请稍后重试',
      maintainMobile: '系统升级中，请稍后重试',
      announcement: '安全升级中，预计4小时，请前往官桥',
      time: '预计时间：支付完成后约 1 分钟',
      getGas: '获取 Gas 极速版',
      getRegular: '获取 Gas 常规版',
      after: '完成转账后，你将可以查看订单',
      history: '历史记录',
      payment: '支付地址',
      timeout: '二维码超时',
      warning: '请不要给过期的地址转账，否则您有丢失代币的风险',
      amount: '支付数量:',
      cancelWarning: '如果取消订单，请不要将任何代币发送到支付地址，因为这可能会导致您的资金损失',
      ok: '好的',
      cancel: '取消订单',
      Complete: '完成',
      getBitlayerGas: '您可以通过从支持 Bitlayer 的钱包/交易所向支付地址发送代币的方式获取 Gas',
      promotionNote1: 'Promotion: only',
      fee: ' $1 ',
      promotionNote2: 'for the Get Gas Express fee, right now!',
      addressNotion:
        'Confirm the blockchain, token, and amount accurately. Each payment address can only be used once. Failure to do so may result in the loss of your deposited funds.',
      promotion: '手续费优惠:',
      estimated: '预计时间: 约10-30分钟',
      ensure: '  请确认以下信息',
      content: `核对转账的 <strong>链, 代币, 金额</strong> 都准确无误
        每个支付地址只能 <strong>收款一次</strong>
                <div> 否则你的资产有可能丢失且无法找回</div>`,
      aboutFlash: '关于闪电跨入',
      aboutInfo: `a.Flash Bridge-In的所有资金均是提前从官桥跨入。<br />b.主要是为了让用户更快、更便宜地跨入Bitlayer。`,
      get: '收到',
      right: '请仔细核对！',
      otherwise: '否则，',
      lose: '你可能损失资产',
      Blockchain: '公链',
      Amount: '数量',
      token: '币种',
      pay: '1个地址只能收款一次',
      kind: '温馨提示',
      hasBridge:
        '您即将访问第三方链接，该链接不属于Bitlayer，您在第三方链接的行为适用于第三方链接的《隐私政策》和《用户协议》，由第三方单独向您承担责任，请您了解相关情况和后果',
      notBridge: '这是PEPE活动专属跨链链接，完成一笔跨链后即可前往领取奖励',
      commonRisk: '常见风险或骗局',
      phishing: '钓鱼网站',
      authorization: '授权风险',
      highYield: '高收益骗局',
      ponzischeme: '庞氏骗局',
      freeAirdrop: '“免费”空投',
      contractLoophole: '合约漏洞',
      aware: '我清楚相关风险',
      addressExpirationNote: '此二维码和地址仅在 15 分钟内有效。请勿在过期后向该地址转账。',
    },
    leaderBoard: {
      title: 'Bitlayer 榜单竞赛 第一期',
      support: '上所支持',
      period: '数据统计周期：',
      incentives: '空投激励',
      liquidity: '为生态伙伴',
      forPartners: '提供流动性支持',
      rules: '了解活动规则？',
      topTvl: 'TVL巅峰榜',
      topTransation24: '24h内交易数巅峰榜',
      topTransaction: '交易数排行榜',
      topPopularity: '人气巅峰榜',
      viewAll: '查看全部',
      tryNow: '立即体验',
      countdown: '本阶段倒计时',
      countEnded: '活动结束',
      stage3: '阶段3：竞赛期',
      stage2: '阶段2：准备期',
      gemsMinted: '已发放宝石总数',
      locked: '总锁定价值',
      transactionCount: '交易数',
      totalLikes: '总赞数',
      tvlRanking: 'Bitlayer生态TVL 排行榜',
      discoverTvl: '探索更多高TVL的 Bitlayer 生态应用',
      txnRanking: 'Bitlayer 生态交易数排行榜',
      discoverTransactions: '探索更多交易活跃的 Bitlayer 生态应用',
      rankingList: 'Bitlayer 生态人气排行榜',
      discoverPopular: '探索 Bitlayer 生态最受欢迎的应用产品',
      top10: 'Top10巅峰榜',
      tvl: 'TVL',
      transactions: '交易数',
      likes: '赞',
      valueUpdate: 'Bitlayer 主网上 DApp 协议中锁定的有效资产总价值，每日更新。',
      numberUpdate: '用户调用Dapp合约的有效交易数，每日更新。',
      likesUpdate: '比赛期间项目累计点赞数，实时更新。',
      name: '名称',
      category: '类别',
      gemMinted: '已收集宝石量',
      gemsPoints:
        '宝石数量根据项目TVL、有效交易笔数、有效用户数、项目人气值、项目产品力综合计算。数据每天00:00（UTC）更新',
      boost: '为我助力',
      bindX: '绑定推特',
      bindNeed: '您需要先绑定一個 X 账户。',
      start: '5月23日开始',
      airdrop: '空投活动进行中！',
      reward: '奖励',
      rank: '榜单竞赛',
      dappCenter: '生态应用中心',
      more: '更多',
      introduce: '活动介绍',
      gems: '宝石100%',
      distributed: '向用户分发!',
      rewards: '奖励',
      completed: '已完成',
      winnerList: '获奖名单',
      slots: '份',
      win: '赢取',
      startAt: '开始于',
      eventCountdown: '活动倒计时',
      ended: '已结束',
      verify: '验证',
      verified: '已验证',
      tutorial: '参与教程',
      participated: '参与',
      tasks: '任务',
      congratulation: '恭喜！',
      foru: '获得200 Bitlayer 积分！',
      export: '探索Bitlayer生态应用中心获取更多奖励！',
      phase: '第{{num}}期',
      'Free Drop': '免费空投',
      '$1 Sweeptake': '$1夺宝',
    },
    dappDetail: {
      reward: '奖励：',
      activity: '活动',
      join: '立即加入！',
      introduce: '活动介绍',
      whatIs: '什么是{{name}} ?',
      overView: '概览',
      dappIntro: '介绍我们的Dapp！',
      event: `如何参与空投：`,
      team: '团队',
      twitterText: '现在和我一起体验Bitlayer上的{{name}} ，获得#Bitlayer生态100+项目的空投奖励！',
      shareTitle: '分享给好友',
      shareText: '分享并和朋友一起体验你喜欢的项目！成为获得潜在空投的Bitlayer挖矿先锋',
    },
    miningGala: {
      meta: {
        title: 'Win Bitlayer rewards & dapp airdrops',
      },
      head: {
        time: 'May 27th, 2024 13PM UTC - June 10th, 2024 13PM UTC',
        doubleGain: 'Double Gain',
      },
      miningPolls: {
        title: 'Mining Pools',
        claim: 'Claim',
        minted: 'Minted',
        taskTitle: 'Complete Tasks to Mint a Pioneer Badge',
        taskTip: 'Complete at least one transaction with {{project}}',
        shareText: 'Share with your firends',
        or: 'Or',
        hasHelmet: 'Hold Lucky Helmet',
        auditReport: 'audit report',
        miningGuide: 'mining guide',
        airdrop: 'Airdrop',
        detailRules: 'Detail Rules',
        claimSuccess: 'Claim Success!',
        claimFailed: 'Claim failed, Please try again later.',
        bitlayerTask: 'Participate the Bitlayer Mining Gala',
        lorenze: {
          desc: 'The premier platform for Bitcoin liquid restaking token issuance, trading, and settlement through Babylon.',
          tasks: [
            'Stake BTC to the Babylon Pre-launch Staking and get stBTC.',
            'Bridge the stBTC received to Bitlayer Mainnet.',
            'Participate in the Bitlayer DeFi ecosystem with your stBTC.',
          ],
        },
        bitsmiley: {
          desc: 'BTC-Native stablecoin protocol, Initially funded by OKX Ventures & ABCDELabs.',
          tasks: [
            'Mint bitUSD on bitsmiley.',
            'Add liquidity to bitUSD-USDT / bitUSD-WBTC on bitCow.',
            'Stake bitUSD on Defi protocols.',
          ],
        },
        avalon: {
          desc: 'Avalon Finance strives to become the best decentralized lending protocol on BTC layer 2.',
          tasks: [
            'Supply assets on Bitlayer, earn at least 1000 supply points.',
            'Borrow assets on Bitlayer, earn at least 500 borrow points.',
            'Loop your supply and borrowing on Avalon Finance.',
          ],
        },
        bitcow: {
          desc: 'BTC-Native stable and concentrated liquidity AMM.',
          tasks: [
            'Add liquidity to bitUSD-WBTC / bitUSD-USDT trading pairs.',
            'Participate in trading bitUSD-USDT or other pairs.',
            'Create new trading pairs and increase the trading volume.',
          ],
        },
        pell: {
          desc: 'Pell is a decentralized trust marketplace that diversifies BTC and LSD yield, aiming to enhance security for BTC L2 networks.',
          tasks: [
            'Connect your wallet to sign in.',
            'Stake over 0.001 BTC and keep it locked for 7 days.',
            'Lucky draw: 1.5x permanent points card x 1000, 100 USDT x 10.',
          ],
        },
        enzo: {
          desc: 'Enzo Finance is a best-decentralized lending protocol on the BitLayer chain with its cutting-edge algorithmic.',
          tasks: [
            'Deposit / Stake your Bitlayer assets on Enzo Finance.',
            'Borrow Bitlayer assets on Enzo Finance.',
            'Daily 1BTC Giveaway (Requirements: total deposits / loans > $100 USDT).',
          ],
        },
        bitparty: {
          desc: 'BitParty is the first "asset gamified community network" in the BTC ecosystem!',
          tasks: [
            'Obtain Bitlayer assets through the cross-chain bridge.',
            'Stake your Bitlayer assets in Bitparty and earn points to get $BTPX.',
            'Join the groups and seize territory with the others!',
          ],
        },
      },
      shareDialog: {
        title: 'Share with friends',
        desc: 'Share with your friends, participate in Bitlayer Mining Gala, and collect exclusive Bitlayer Mining Gala Pioneer Badges Now!',
      },
      tipDialog: {
        title: 'Kind Tips',
        desc: 'insufficient BTC banlance，you can replenish BTC in the following ways.',
        depositBtn: 'Lightning deposit',
        bridgeBtn: 'Bridge',
      },
      twitterShare: {
        projectShare: `Join with me, participate in {{name}} mining pool on @BitlayerLabs #MiningGala now! {{money}} airdrop waiting for you to share!\n`,
        badgeShare:
          'I just minted my @BitlayerLabs #MiningGala Pioneer Badge here! Enter the page to free mint yours! Join with me to share $24,350,000 airdrops!\n',
      },
      bottomTip: {
        title: 'Useful Tutorials for Bitlayer Mining Gala!',
        desc: 'Welcome to Bitlayer Mining Gala! Check our guidance below and enjoy your mining trip here!',
        tips: [
          'Get Bitlayer Gas in 1 minute.',
          'Tutorial for bridging assets to Bitlayer.',
          'The best way to join Bitlayer Mining Gala.',
          'Bitlayer Mining Gala Pioneer minting guidance.',
        ],
      },
    },
    userCenter: {
      title: '用户中心',
      dailyTasks: '每日任务',
      bitlayerPoints: 'Bitlayer积分',
      bitlayerGems: 'Bitlayer宝石',
      tasks: '任务',
      badges: '勋章',
      bitlayerDays: '已加入Bitlayer <span>{{ days }}</span> 天',
      txn: '交易数',
      bridged: '累计跨入',
      unlocked: 'Lv{{ level }} 已解锁',
      locked: 'Lv{{ level }} 待解锁',
      unlocking: 'Lv{{ level }} 正在解锁 <span>{{ progress }}%</span>',
      tabs: {
        newRacerTasks: '新手任务',
        advancedTasks: '进阶任务',
        ecoTasks: 'Eco 任务',
        myBadges: '我的勋章',
      },
      task: {
        complete: '立即参与',
        claim: '领取奖励',
        pointsAcquired: '已获得 <span>{{ points }}</span> Bitlayer积分。',
        check: '查看',
      },
      claimGems: {
        title: '恭喜，请立即领取您的宝石！',
        action: '立即领取',
      },
      badge: {
        coming: '即将上线',
        inProgress: '进行中',
        finished: '已结束',
        claim: '立即领取',
        join: '参与活动',
        owned: '已拥有勋章',
        notOwned: '未拥有勋章',
        rewardCanClaim: '支持领取',
        rewardClaimed: '已领取',
      },
      reward: {
        points: '积分',
        gems: '宝石',
        btr: 'BTR',
      },
      errors: {
        twitter: {
          accountBinded: '此Twitter已绑定其他地址，请使用其他Twitter帐号。',
        },
        unexpectedError: '未知错误，请稍后再试。',
      },
      rankTitle: '我的积分排名',
      topRacers: '高排名赛手',
      racer: '用户',
      gains: '收益',
      invite: {
        discover: '了解 Bitlayer',
        and: '并获取',
        invite: '邀请好友',
        refer: '推荐给好友',
        shareText:
          '将您的推荐代码/链接分享给好友。当他们注册 bitlayer.org 并在 Bitlayer 上多次发送令牌时，您即可获得奖励。（每 12 小时更新一次）',
        my: '我的推荐',
      },
      twitter: {
        bindTitle: '绑定您的 X (Twitter) 帐户',
        bindDesc: '您需要先绑定一个 X (Twitter) 帐户！',
        bindAction: '在X上授权',
        tips: '注意：一个 Twitter 账户只能绑定一个地址，以避免错误。',
      },
      draw: {
        history: {
          history: '抽奖历史',
          time: '抽奖时间',
          reward: '奖励',
        },
        error: '您有 0 次抽奖机会，邀请好友即可获得更多机会',
        up: '奖励最高可达 1000 美元',
        unit: '抽奖',
        invite: '邀请好友获得更多抽奖机会，提升幸运等级！',
        card: '抽卡',
        wait: '正在等待链上确认，大约需要 30 秒',
        warning: '警告',
        sorry: '抱歉！每次抽奖需要至少 1000 积分。您的积分不足。',
        ok: '确认',
      },
    },
    activities: {
      guessingGame: {
        title: '有奖竞猜',
        subtitle: '币价预言家',
        olympics2024: {
          title: '有奖竞猜',
          subtitle: '2024巴黎奥运会',
        },
        status: {
          inProgress: '进行中',
          coming: '即将上线',
          waiting: '等待开奖',
          finished: '已结束',
          releasing: '结果24小时之内揭晓',
          endIn: '倒计时',
        },
        placeBet: '参与竞猜',
        bet: '竞猜',
        betUnit: '单次{{ step }}个{{ rewardType }}',
        betFor: '选择{{ text }} <icon /> <span>{{ amount }}</span>',
        wonRewardFor: '成功竞猜{{ text }}并赢得 <icon /> <span>{{ amount }}</span>',
        myBets: '竞猜记录',
        won: '获胜',
        return: '回报率',
        pool: '累计奖池',
        reward: {
          claim: '领奖',
          claimed: '已领奖',
          claimAll: '领取所有奖励',
          congratulations: '恭喜！',
          claimedAllPoints: '您已从用户中心赚取 <point /> <span>{{ points }}</span> 积分。',
          claimedAllGems: '您已从用户中心赚取 <gem /> <span>{{ gems }}</span> 积分。',
          claimedAllBoth:
            '您已从用户中心赚取 <point /> <span>{{ points }}</span> 和 <gem /> <span>{{ gems }}</span> 积分。',
        },
        messages: {
          placedBet: '您已成功竞猜，祝您好运！',
          failedToPlaceBet: '竞猜失败，请稍后再试。',
          claimedReward: '您已成功领取奖励！',
          failedToClaim: '取奖励失败，请稍后再试。',
          insufficient: '您至少需要{{amount}}个{{rewardType}}才能参与竞猜。',
          alreadyClaimedAll: '您已领取所有奖励。',
          failedToClaimAll: '无法领取所有奖励。请稍后再试。',
        },
      },
    },
    whiteList: {
      reward: '我的奖励',
      address: '地址',
      xp: 'Bitlayer 积分',
      lucky: '奖励',
      points: '积分',
      winnerList: '获奖名单',
      description: `注意：在活动结束后 7 天内完成和验证所有任务的用户将获得 Bitlayer 积分奖励。 项目奖励将根据该项目的官方公告进行发放。`,
    },
    bitvm: {
      title: {
        slogon1: 'BitVM Stack',
        slogon2: '让比特币拥有验证能力',
        slogon3: '可证实',
        description: '比特币扩容体系中缺失的一环',
        whitePaper: '白皮书',
        earlyAccess: '申请使用',
        comingSoon: '敬请期待',
      },
      feature: {
        badge: '功能特色',
        title1: '继承了比特币的安全性',
        description1: '极低的工程成本',
        title2: '模块化，可组合',
        description2: '开源、基于 API 的服务、即插即用',
        title3: '架构可适应未来需求',
        description3: '防欺诈 + 有效性证据',
        title4: '可自定义，可升级',
        description4: '开源，可审计',
      },
      advantages: {
        badge: '优点',
        num1: '可加快开发进度',
        num2: '通过聚合机制降低矿工费',
        num3: '比肩比特币的 1 级安全性',
      },
      usecase: {
        badge: '用例',
        case1: 'Bitcoin Layer2',
        case2: '正在包装 BTC',
        case3: '比特币链桥',
        case4: '比特币质押协议',
        start: '立即开始使用！',
        early: '抢先体验',
      },
    },
    raffle: {
      draw: '抽卡',
      rate: '回报率高达 {{rate}}% ',
      getFreeDraw:
        '获得 <span className="bl-text-primary group-hover/title:bl-text-white">免费</span> 抽卡次数',
      getFreeDrawTip: '每邀请1位好友来抽卡, 可获得1次免费抽卡次数',
      freeDraw: '免费抽卡',
      oneDraw: '1 抽',
      tenDraw: '10 抽',
      hundredDraw: '100 抽',
      '10%off': '10% 优惠',
      guaranteedComponent: '至少出1个<span class="bl-text-primary">4星</span>装备',
      skip: '跳过',
      superCard: {
        title: '超级跑车抽卡',
        more: '更多',
        myEstimatedTotalAirdrop: '我的预期空投价值：',
      },
      learnMore: {
        label: '收益秘籍',
        title: '如何获得更多空投',
        tip: '请仔细阅读秘籍与游戏规则, 里面有财密码!',
        description:
          "官方补贴<span class='bl-text-primary'>100%</span> ($BTR). 用户的所有抽卡费用都会放到空投池里, 最终空缺$BTR总金额为控投池的 <span class='bl-text-primary'>200%</span> . 无需担心被反撸. ",
        section1: {
          title: '为什么要参与',
          description1:
            "在游戏中组装出跑车, 将会在TGE时获得 <span class='bl-text-primary'>解锁的 $BTR token</span> 空投. 空投池包含所有抽卡费用和 <span class='bl-text-primary'>100%</span> Bitlayer官方补贴.",
          description2:
            "拥有不同星级的跑车, 代表不同的空投回报率, 最高可达 <span class='bl-text-primary'>200%</span>.",
        },
        section2: {
          title: '如何参与',
          description1:
            "跑车有3种星级 （<img class='bl-inline bl-h-2 md:bl-h-[17px]' src='/images/user-center/raffle/star-3.png' alt='three stars'>、<img  class='bl-inline bl-h-2 md:bl-h-[17px]' src='/images/user-center/raffle/star-4.png' alt='four stars'> 、<img class='bl-inline bl-h-2 md:bl-h-[17px]' src='/images/user-center/raffle/star-5.png' alt='five stars'>）, 分别由对应星级的8种装备组装而成. 收集8个不同部位的装备, 可组装成一辆跑车.",
          description2: '进行10抽, 有更大的机会抽出高级的装备, 且减少费用',
          description3: '10次10抽(或100抽), 至少会出1个5星装备',
          description4: '拥有更高星级的跑车, 才能获得更高的回报率',
        },
      },
      collection: {
        myEstimatedTotalAirdrop: "我的<span class='bl-text-primary'>预期 </span>空投价值",
        valueTips: {
          tip1: '预期空投价值是指你收到的$BTR token的总价值(基于TGE价格)',
          tip2: '全部跑车分享整个空投价值, 因此预期空投价值会随时变化',
          tip3: '预期空投价值不等于你最终获得的价值',
        },
        title: '我的<span class="bl-text-primary">空投</span>收集',
        ready: '可以组装',
        collection: '收集',
        stars: '星',
        estimated: '预期',
        cars: {
          engine: '引擎',
          wheels: '车轮',
          steeringwheel: '方向盘',
          discbrake: '刹车',
          chassisrigged: '底盘',
          truckhitch: '挂接',
          seat: '座位',
          gascylinder: '增压器',
        },
      },
      drawAd: {
        title: '再来一次10抽, 即可组装跑车',
        myStarCollection: '我的 {{ star }}-星收集',
        '3Star-title': "<span class='bl-text-primary'>1至2次</span> 10抽",
        '3Star-description1': '组装一辆新的跑车',
        '4Star-title': "<span class='bl-text-primary'>非常</span> 接近",
        '4Star-description1': '组装成高价值的4星跑车',
        '5Star-title': "<span class='bl-text-primary'>非常</span> 接近",
        '5Star-description1': '组装顶级价值的5星跑车',
        '5Star-description2': "获得高达<span class='bl-text-primary'>200%</span> 的回报",
      },
      oopsSomethingWrong: 'oops, 貌似出了点小状况',
      insufficientUSDTBalance: 'USDT余额不足',
      insufficientBTCGas: 'BTC gas不足',
      needSomeUSDTOrGas: '需要更多USDT或BTC Gas?',
    },
  },
};
