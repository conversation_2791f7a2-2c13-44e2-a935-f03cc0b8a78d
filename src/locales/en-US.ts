export default {
  title: 'Bitlayer',
  meta: {
    description: 'The first Bitcoin security-equivalent Layer 2 based on the BitVM paradigm.',
  },
  common: {
    readMore: 'Read More',
    learnMore: 'Learn More',
    upload: 'Upload',
    submit: 'Submit',
    return: 'Return',
    send: 'Send',
    back: 'Back',
    coming: 'Coming soon',
    connect: 'Connect Wallet',
    disconnect: 'Disconnect',
    switchChain: 'Switch to {{chainName}}',
    insufficientBalance: 'Insufficient balance',
    search: 'search',
    toTop: 'Return to Top',
    getGas: 'Get Bitlayer Gas',
    bridge: 'Bridge & Earn',
    connectDesc: 'Connect your wallet to transfer tokens',
    MesonDes: 'Safe, Costless & Instant Cross-chain for Stablecoins',
    OwltoDes: 'Owlto Finance is an intent-centric interoperability protocol',
    OrbiterDes: 'Orbiter Finance is a decentralized cross-rollup bridge',
    login: 'Login',
    noData: 'No Data',
    confirm: 'Confirm',
    website: 'Website',
    share: 'Share',
  },
  resources: {
    github: 'https://github.com/bitlayer-org',
    discord: 'https://discord.gg/bitlayer',
    x: 'https://twitter.com/BitLayerLabs',
    linkedin: 'https://bit.ly/42B6v15',
    telegram: 'https://t.me/bitlayerofficial',
    medium: 'https://medium.com/@Bitlayer',
    rankRule:
      'https://medium.com/@Bitlayer/bitlayer-provides-20m-airdrop-for-dapp-leaderboard-competition-rewarding-ecosystem-projects-and-87ed3dc76b94',
  },
  navigation: {
    links: {
      developers: 'Developers',
      users: 'Users',
      resources: 'Resources',
      contact: 'Contact Us',
      mainnet: 'Mainnet',
      bridge: 'Bridge & Earn',
      gas: 'Get Gas',
      startBuilding: 'Start Building',
      btrScan: 'Bitlayer(BTR) Scan',
      addMainnet: 'Add Bitlayer Mainnet',
      testnet: 'Testnet',
      faucet: 'Faucet',
      testnetBridge: 'Testnet Bridge',
      testnetBridgeHint: 'Bitlayer Testnet Bridge',
      testnetScan: 'Testnet Scan',
      addTestnet: 'Add Bitlayer Testnet',
      language: 'Language',
      getGas: 'Get Bitlayer Gas',
      readyPlayerOne: 'Ready Player One',
      luckyHelmet: 'Lucky Helmet',
      dAppsLeaderboard: 'DApps Leaderboard',
      hint1: '$50,000,000 Airdrop',
      hint2: 'Be a driver. Earn from bitlayer',
      hint3: 'Vote for Your Favorite Project',
      brandKit: 'Brand Kit',
      whitepaper: 'Whitepaper',
      leaderboard: 'Leaderboard',
      dappCenter: 'DApp Center',
      dappCenterHint: 'Bitlayer DApp Ranking List',
      miningGala: 'Mining Gala',
    },
    footer: {
      Participate: 'Participate',
      ReadyPlayerOne: 'Ready Player One',
      Build: 'Build',
      BuildingBitlayer: 'Building Bitlayer',
      Faucet: 'Faucet',
      SupportedWallets: 'Supported Wallets',
      TheGraph: 'The Graph',
      MainScan: 'Bitlayer(BTR) Scan',
      TestScan: 'Testnet Scan',
      About: 'About',
      Solutions: 'Solutions',
      Roadmap: 'Roadmap',
      EventNews: 'Event & News',
      Jobs: 'Jobs',
      Community: 'Community',
      Podcast: 'Podcast',
    },
    walletDrawer: {
      wrongNet: 'Please switch to {{chainName}}',
      noNFT: 'No Collection',
    },
  },
  pages: {
    home: {
      title: {
        notice: 'Bitlayer Ready Player I is Now Live! Vote For Points & WLs Now!',
        slogon1: 'Extend',
        slogon2: 'possibilities',
        slogon3: 'Of Bitcoin',
        startBuilding: 'Start Building',
        bridgeEarn: 'Bridge & Earn',
        competition: 'Bitlayer Ready Player I Competition Stage',
        dapp100:
          "Earn Rewards from <span class='bl-text-xs md:bl-text-2xl bl-text-primary bl-font-[600]'>100+</span> Dapps!",
        gasFee: 'Median Gas Fee:',
        leaderboard:
          "Check your eligibility for <span class='bl-text-xs md:bl-text-2xl bl-text-white bl-font-[600] bl-px-1'> Gems airdrop </span> now!",
      },
      solutions: {
        solutions: 'Solutions',
        title: "What Bitcoin Layer 2's Future Should Be",
        subtitle: 'The key pillar solutions for the future of Bitcoin ecosystem',
        items: [
          {
            title: 'Layer 1 Verification',
            text: 'Inherits Bitcoin security via BitVM',
          },
          {
            title: 'Trustless 2-Way Peg',
            text: 'Combining DLC & BitVM, bringing groundbreaking models that supersede traditional multisig',
          },
          {
            title: 'Turing-Completeness',
            text: 'Supports multiple VMs, enables a 100% EVM-Compatible Environment',
          },
        ],
      },
      roadmap: {
        roadmap: 'Roadmap',
        title: 'The Journey of Bitcoin',
        title2: 'The Journey of Bitlayer',
        points: [
          {
            title: 'Colored Coins',
            description:
              'Colored Coins tag blockchain tokens to represent real-world assets for secure and transparent management.',
            date: '2012',
          },
          {
            title: 'Counterparty',
            description:
              'Counterparty adds custom tokens and smart contracts to Bitcoin for building and trading digital assets.',
            date: '2014',
          },
          {
            title: 'Spells of Genesis',
            description:
              'Blockchain-based mobile game Spells of Genesis blends trading cards with arcade elements in a fantasy setting.',
            date: '2015',
          },
          {
            title: 'Rare PEPE',
            description:
              'Digital trading cards featuring PEPE the Frog are created and exchanged on the Bitcoin blockchain, symbolizing unique, collectible crypto art.',
            date: '2016',
          },
          {
            title: 'Segwit update',
            description:
              'Segregated Witness protocol update increases block capacity and improves scalability by separating signature data from transaction data.',
            date: '2017',
          },
          {
            title: 'Taproot update',
            description:
              'Taproot Bitcoin protocol update enhances privacy, efficiency, and smart contract capabilities through a new signature scheme called Schnorr Signatures.',
            date: '2021',
          },
          {
            title: 'Ordinals',
            description:
              'Ordinals protocol inscribes unique digital artifacts directly onto individual Satoshis on the Bitcoin blockchain, enabling NFT-like functionality.',
            date: '2023',
          },
        ],
        journeys: [
          {
            title: 'Mainnet V1',
            texts: [
              'Best security model to enroll developers and users: PoS sidechain + MPC-TSS/Native Multisig',
            ],
            subtitle: '04/2024',
          },
          {
            title: 'Mainnet V2',
            texts: [
              'Transform to a rollup-equivalent model: ground breaking security model for asset bridging, L1 verification challenging game without BitVM',
            ],
            subtitle: '09/2024',
          },
          {
            title: 'Mainnet V3',
            texts: [
              'Full picture: L1 verification challenging game with BitVM paradigm, enabling Bitcoin security-equivalence',
            ],
            subtitle: '06/2025',
          },
        ],
      },
      events: {
        tag: 'Event & News',
        title: 'Bitlayer Ecosystem Event & News',
        subtitle: 'Follow up our updates',
        items: ['Bitcoin Singapore', 'Ethdenver', 'Bitcoin meets DeFi', 'Ready Player One'],
      },
      jobs: {
        tag: 'Jobs',
        title: 'Job Openings',
        subtitle: "Let's Make Bitcoin History Together!",
        positions: [
          {
            title: 'Head of Developer Relations_',
            skills: [
              'Responsible for expanding and maintaining developer relations within the public blockchain ecosystem',
              'Handle technical communication and developer education with various global developer technical communities and ecosystems.',
            ],
          },
          {
            title: 'Protocol Researcher_',
            skills: [
              'Conduct cutting-edge research on blockchain based on industry pain points.',
              'Write high-quality research reports and papers.',
            ],
          },
        ],
        more: 'Explore open positions',
      },
      community: {
        tag: 'Community',
        title: 'Join the community of the future',
        subtitle: 'Stay active with Bitlayer',
        items: [
          {
            title: 'Contribute to Bitlayer',
            description: 'Build with us',
          },
          {
            title: 'Join the Discord',
            description: 'Talk with us',
          },
          {
            title: 'Follow on X',
            description: 'Engage with us',
          },
          {
            title: 'Connect on LinkedIn',
            description: 'Connect with us',
          },
          {
            title: 'Join our Telegram',
            description: 'Chat with us',
          },
          {
            title: 'Stay up to date in Medium',
            description: 'Learn with us',
          },
        ],
      },
    },
    developers: {
      title: {
        titleLine1: 'Welcome to',
        titleLine2: 'Bitlayer Docs',
        subtitle: 'A manual for joining the Bitlayer ecosystem By builders for builders',
        buildNow: 'Build Now',
        blog: 'Blog',
      },
      faq: {
        title: 'FAQ',
        name1: 'What is Bitlayer?',
        desc1:
          'Bitlayer acts as a Layer 2 solution for Bitcoin, boasting 100% EVM and Ethereum toolchain compatibility, with BTC as native token(gas token). Bitlayer can enable applications and developers from the existing Ethereum ecosystem to migrate to Bitlayer at low cost, eliminating the need for substantial modifications or rewrites.',
        name2: 'Is Bitlayer compatible with existing Bitcoin wallets?',
        desc2:
          'Yes, Bitlayer is compatible with existing wallets such as Metamask, Unisat, or other Bitcoin/Ethereum-compatible wallets, allowing users to seamlessly interact with their funds and assets on the Bitlayer network.',
        name3: 'Can developers migrate their existing projects to Bitlayer?',
        desc3:
          'Yes, Bitlayer supports existing developers by offering EVM compatibility, allowing for seamless migration and deployment of existing projects at low costs. Developers can have the ease of migrating smart contracts written in Solidity, Vyper, or any other language that compiles EVM bytecode directly to Bitlayer, using the toolchain you are familiar with: Ethereum JSON-RPC, Hardhat, etc.',
        name4: 'How can I help support Bitlayer?',
        desc4: `There are several ways to support Bitlayer. You can actively participate in community discussions, provide feedback and suggestions, contribute to the development of applications or tools on the platform, or promote Bitlayer to others who may benefit from its services. Additionally, you can explore any specific support initiatives or programs that Bitlayer may have in place. Click to know more about <span data-link-index='0' class='bl-underline bl-cursor-pointer'>Ready Player One</span>, a program unlocks $50,000,000 in incentives for early builders and contributors.`,
      },
      feature: {
        title: 'Our Strength',
        title1: 'Turing-Completeness',
        desc1: 'Supports multiple VMs, enables a 100% EVM-Compatible Environment',
        title2: 'Trustless 2-Way Peg',
        desc2:
          'Combining DLC & BitVM, bringing groundbreaking models that supersede traditional multisig',
        title3: 'Layer 1 Verification',
        desc3: 'Inherits Bitcoin security via BitVM',
      },
      quickstart: {
        title: 'Developer Quickstart',
        subtitle: 'Unlock the Bitlayer ecosystem with a guide crafted by builders, for builders',
        title1: 'Connect Your Wallet to Bitlayer Testnet',
        skill1:
          'Add the Bitlayer Testnet configurations to your wallet and interact with the Dapps on Bitlayer Testnet',
        title2: 'Compile, Run and Deploy',
        skill2: 'This guide walks you through compiling and running Bitlayer to deployment',
        title3: 'Fresh Perspectives and Latest Updates Await',
        skill3: 'Stay tuned for detailed updates on technology and market insights in our blog',
        readMore: 'READ MORE',
      },
      tools: {
        title: 'Developer Tools',
        subtitle:
          'Leverage the suite of tools within the Bitlayer ecosystem to maximize your potential on Bitlayer',
        name1: 'Faucet',
        desc1: 'Obtain your Bitlayer Testnet tokens every 24 hours for development here',
        name2: 'Mainnet Scan',
        desc2: `A essential tool for exploring and analyzing blockchain data on Bitlayer Mainnet. You can dive deep into the transactions, blocks, and addresses.`,
        name3: 'Testnet Scan',
        desc3:
          "A essential tool for exploring and analyzing blockchain data on the testnet. You can dive deep into the testnet's transactions, blocks, and addresses",
        website: 'Website',
      },
      connect: {
        title: 'Connect with Us',
        subtitle:
          'Stay informed about the most recent news and advancements within the Bitlayer Community',
      },
    },
    bridge: {
      // transfer
      bridgeTitle: 'Bridge & Earn',
      gasTitle: 'Get Bitlayer Gas',
      gasPromotion: 'Quick Start - Get Bitlayer Gas in 1 Minute!',
      newToBridge: 'New to bridge?',
      from: 'From',
      to: 'To',
      amount: 'Amount',
      balance: 'Balance: {{balance}}',
      max: 'Max',
      recipientAddress: 'Recipient Address',
      recipientTip: 'Connect wallet to receive tokens',
      est: 'Est: {{time}}',
      fee: 'Fee:',
      total: 'Total:',
      receivepPlaceholder: 'Please enter the {{chainName}} address',
      transfer: 'Transfer',
      transferProgress: 'Transfer is in progress',
      approve: 'Approve',
      approveInProgress: 'Waiting for your approval',
      checkingAllowance: 'Checking allowance',
      minLimit: 'Minimum {{amount}} {{symbol}} per transaction',
      maxLimit: 'Maximum {{amount}} {{symbol}} per transaction',
      invalidAmount: 'Invalid amount',
      invalidAddress: 'Invalid address',
      switchChainDesc:
        'Switching to the {{chainName}} network requires the use of {{networkType}} wallet.',
      transferFailed: 'Transfer Failed',
      connectDesc: 'Connect your wallet to transfer tokens',
      bridgeTip: 'Bridge to Bitlayer, Hold Bitlayer Lucky Helmet, Share 400K BWB Points!',
      historyTab: 'History',
      getGas: 'Get Gas Express',
      swap: 'Swap',
      gasLimit: `Balance ≥{{maxBalance}} can't be swaped`,
      dayLimit: `Today's remaining BTC is {{remainSwapAmount}}`,
      // history
      loadingData: 'Loading Data',
      noData: 'No Data',
      sender: 'sender',
      receiver: 'receiver',
      transactionHash: 'Transaction Hash',
      Pay: 'You pay',
      gasPay: 'Pay',
      gasGet: 'Get',
      Balance: 'Balance:',
      Max: 'Max',
      SwitchToBitlayer: 'Switch To Bitlayer',
      Maximum: 'Maximum {{maxSwapAmount}} BTC per transaction',
      History: 'History',
      Receive: 'You receive',
      Fee: 'Fee:',
      Total: 'Total:',
      Slippage: 'Slippage:',
      ThirdPartyBridge: 'Third Party Bridge',
      ConfirmSwap: 'Confirm Swap',
      ApproveToken: 'Approve token',
      ApproveGas: 'Approve signature without paying gas',
      ConfirmSwapRadio: 'Confirm swap radio',
      ConfirmGas: 'Confirm swap radio without paying gas',
      Processing: 'Processing...',
      Completed: 'Completed',
      From: 'From',
      To: 'To',
      Return: 'Return',
      thirdParty: {
        tipTitle: 'Kindly Reminder',
        tipContent:
          'The current Bitlayer EVM bridge is a beta version. Please use third-party bridge service for small amount bridge.',
      },
      btcHint: 'Only supports BTC and Ordinal protocol assets',
    },
    faucet: {
      title: 'testnet faucet',
      description:
        'Obtain Bitlayer Testnet tokens every 24 hours for development. Testnet token has no financial value and cannot be traded at a real price.',
      selectField: {
        label: 'Select Token',
        placeholder: 'Select a token',
      },
      textFiled: {
        label: 'Wallet Address',
        placeholder: 'Enter your Bitlayer Testnet address',
      },
      result: {
        gotTip: 'You got {{tokens}}!',
        gotAnother: 'You can request for another {{token}} in 24 hours.',
        sending: 'Sending...',
      },
      error: {
        testAddress: 'Please enter your bitlayer testnet address',
        verifyCaptcha: 'Please complete the captcha verification',
        verifyFailed: 'Failed to verify captcha',
        exceededLimit:
          'You have requested tokens in the last 24 hours. Please wait {{h}}{{m}}{{s}} before trying again.',
        invalidAddress: 'Please enter a valid bitlayer testnet address',
        unexpectedError: 'An unexpected server error occurred. Please try again later.',
      },
    },
    readyplayone: {
      title: 'Ready Player One',
      description:
        'Unlock <text>$50,000,000</text> in incentives for early builders and contributors.',
      register: 'Register',
      time: 'Mar. 29, 2024 - May. 10, 2024',
      airdrop: 'Up to $1M Incentives For Each Project!',
      Volume: 'Volume:',
      rules: {
        title: 'Rules',
        ruleContents: [
          {
            title: 'Register',
            texts: ['Ensure registration is completed by May 10th.'],
            date: 'Mar-Apr',
          },
          {
            title: 'Launch',
            texts: ['Initiate launch on the mainnet and gear up for the impending competition.'],
            date: 'Apr-May',
          },
          {
            title: 'Airdrop',
            texts: [
              'Join the leaderboard competition to win a token airdrop worth over $50 million.',
            ],
            date: 'Jun-Jul',
          },
        ],
      },
      colead: 'Co-Leads',
      investors: 'Investors',
      partners: 'Partners',
      setp: {
        register: {
          schemaName: 'Please provide the project name',
          schemaWebsite: 'Please provide the project website',
          schemaContact: 'Please provide the contact name',
          schemaEmail: 'Please provide the project email',
          schemaTelegram: 'Please provide the project telegram',
          schemaAgree: 'Please agree to the terms and conditions',
          formValidError:
            'We found some issues in your form. Please review the highlighted fields and make corrections before resubmitting.',
          apiClientError:
            'We found some issues in your form. Please review the fields and try to submit again. ({{code}})',
          apiServerError:
            'There seems to be a temporary glitch on our end. Please refresh the page or try again in a few minutes. ({{code}})',
          formOne: {
            title: 'Project Information',
            name: {
              label: 'Project name',
              placeholder: 'Please enter the name',
            },
            twitter: {
              label: 'Project twitter',
              placeholder: 'Please enter the id',
            },
            website: {
              label: 'Project website',
              placeholder: 'Please enter the URL',
            },
            logo: {
              label: 'Project logo <span>(1 by 1 ratio recommended)</span>',
              placeholder: 'Select file to upload',
            },
            description: {
              label:
                'Project Description/Deck: Summarize your project (purpose, problem solved, how it works) in 500 characters or provide a deck link.',
              placeholder: 'Please enter the description',
            },
            category: {
              label: 'What is the category of your project?',
              placeholder: 'Please select',
              selects: [
                'Infra',
                'Defi',
                'Social',
                'Inscriptions',
                'NFT',
                'MeMe Token',
                'DAO',
                'Tooling',
                'Wallet',
                'Cross-chain',
                'Privacy computering',
                'Privacy Token',
                'Prediction makert',
                'Storage',
                'Gamefi',
                'Entertainment',
                'Metaverse',
                'Others',
              ],
            },
            stage: {
              label: 'Project stage',
              placeholder: 'Please select',
              selects: ['Idea phase', 'In development', 'Prototype available', 'Live/Beta'],
            },
            coin: {
              label:
                'Token addresses on Bitlayer <span>(if not available, please fill in N/A)</span>',
              placeholder: 'Please enter the token addresses',
            },
            lockUp: {
              label:
                'Lock-up addresses on Bitlayer <span>(if not available, please fill in N/A)</span>',
              placeholder: 'Please enter lock up addresses',
            },
            contract: {
              label:
                'Dapp contract addresses on Bitlayer <span>(if not available, please fill in N/A)</span>',
              placeholder: 'Please enter the contract addresses',
            },
            defilama: {
              label: 'Project Defillama link <span>(if not available, please fill in N/A)</span>',
              placeholder: 'Please enter the defillama link',
            },
          },
          formTwo: {
            title: 'Team Information',
            team: {
              label:
                'How many are there? List the names, roles and  backgrounds of main team members.',
              placeholder: 'Please enter the description',
            },
          },
          formThree: {
            title: 'Contact Person',
            name: {
              label: 'Name',
              placeholder: 'Please enter the name',
            },
            email: {
              label: 'Email address',
              placeholder: 'Please enter the email address',
            },
            telegram: {
              label: 'Telegram',
              placeholder: 'Please enter the url',
            },
            twitter: {
              label: 'Twitter profile',
              placeholder: 'Please enter the url',
            },
          },
          consent: {
            title: 'Consent',
            label:
              "I agree with the hackathon's terms and conditions, privacy policy, and code of conduct.",
          },
        },
        launch: {
          title: 'Congratulations on successfully registering!',
          documents: 'Developers documents',
          deploy: 'Now you can deploy your project on bitlayer!',
          scanCode: 'Scan to join the community',
          next: 'Next',
        },
        airdrop: {
          deploymentTip: 'If your project deployment completes, well done! ',
          description:
            'You will be able to join the primary election! Final winners will receive grants between $10K to $300K. Additional bonuses include honorary rewards of $3K to $5K and access to a $1M prize pool for running events!',
        },
      },
      gems: {
        title: 'Bitlayer Gems Airdrop',
        gems: 'Gems',
        tips: ' will be distributed soon!',
        button: 'Check my eligibility',
        claimGems: {
          button: 'Claim Gems',
          label1: 'You are eligible for your ',
          label2: 'Gems airdrop!',
          'failed-title': 'Keep Going!',
          'label-failed':
            'You are not eligible for gems airdrop in this round. Stay tuned for more events later!',
          'button-failed': 'Earn Rewards from Dapps',
        },
      },
    },
    luckyhelmet: {
      title: 'Lucky Helmet',
      description: 'Be a driver. Earn from Bitlayer.',
      toGet: 'How to get?',
      miningBtn: 'Minting Opens 12:00pm UTC on May 8th',
      assetTitle: 'Asset on Layer 1',
      assetDesc: 'Native Bitcoin Ordinals Asset on Bitcoin Layer 1',
      mintTitle: 'Mint on Layer 2',
      mintDesc: 'Lower gas fees and better efficiency when compared to layer 1',
      desc1: 'Seamless L1/L2 Experience',
      desc2: 'golden shovel bitlayer perks',
      minting: 'Minting',
      mint: 'Mint',
      checkWhitelist: 'Check Whitelist',
      minted: 'Minted',
      mintingSuccess: 'Minting Success',
      congratulations: 'Congratulations!',
      success: 'You are on the whitelist.',
      sorry: 'Sorry',
      failure: 'You are not on the whitelist.',
      finished: 'Finished',
      whitelistOnly: 'Whitelist Only',
      trade: 'Trade Lucky Helmet',
    },
    rank: {
      head: {
        head1: 'Award Pool',
        head2: 'Dapps',
        head3: 'Total Votes',
        head4: 'Daily Votes',
      },
      banner: {
        tag: 'Preparation Stage',
        title: 'READY PLAYER ONE',
        subtitle: 'token for builders',
        link: ' Get To Know Our Rules?',
        all: 'All categories',
        switch: 'Switch to',
        register: ' Register Dapp',
      },
      list: {
        votes: 'VOTES',
        vote: 'Vote',
        out: 'Out of votes',
        invite: 'Invite for Points',
      },
      carouse: {
        title: 'My Ready Player I Points',
        note: 'Please note: Ready Player I Points are not equivalent to Bitlayer Points. They can be exchanged at a certain rate in the future. Follow us for updates!',
        accepted: 'Invite Accepted',
        taskCenter: 'Task Center',
        now: 'Follow Now',
        vote: 'Every Vote',
        invite: 'Every Invite',
        follow: 'Follow on X',
        hint1: 'Up To 3 Times A Day',
        hint2: 'One Time Task',
      },
      dialog: {
        band: {
          title: 'Invite Friends to Boost',
          sorry:
            'Sorry! Your Twitter is already linked to a different address. Please use another Twitter account',
        },
        invite: {
          need: 'You need to bind an X (Twitter) account first! Earn 500 Points for each successful invite now!',
          auth: 'Authorize on X',
          attention:
            'Attention: One Twitter account can only be linked to one address to avoid errors',
        },
        note: {
          please: 'Please note:',
          note: 'Ready Player I Points are not equivalent to Bitlayer Points. They can be exchanged at a certain rate in the future. Follow us for updates!',
        },
        rules: {
          rules1: 'Earn 300 points daily by completing three votes.',
          rules2: 'Follow the official Twitter to receive 500 points.',
          rules3:
            'Each new user invited to vote successfully adds an extra 500 points to your tally.',
          rules4:
            'A random draw will award 100 Bitlayer Lucky Helmets, with higher rankings increasing your chances.',
          vote: 'Voting for Your Favorite Projects',
          note: 'Join the Bitlayer Popularity List voting to support your favorite projects! Event period: April 23, 2024, to May 10, 2024. Log in daily to the leaderboard page, with each person having three voting opportunities per day. Earn extra points by following the official Twitter and inviting new users. Active participants will accumulate Ready Player I event points and have the chance to be whitelisted for the Bitlayer Lucky Helmet.',
          rewards: 'Rewards:',
          boost: `Boost your project's popularity, influence the final leaderboard rankings, and actively participate to win official NFT rewards!`,
          gotIt: 'Got It',
        },
        share: {
          copy: 'Copy Link',
          share: 'Share On',
          invite: 'Invite to vote',
          more: 'Get more votes from friends',
          connect: 'Connect X (Twitter)',
        },
        vote: {
          vote: 'Vote',
          voteFor: 'Vote for',
          daily: '3 votes daily',
          votes: 'Votes',
        },
        expire: {
          late: 'Sorry you are late!',
          expired: 'The voting has ended.',
          tuned: 'More details will be shared on our official twitter, please stay tuned!',
          thx: 'Thank You!',
          follow: 'Follow Us Now',
        },
      },
    },
    getGas: {
      recipient: 'Recipient Address',
      placeholder: 'Please enter a Bitlayer address',
      invalid: 'Invalid address',
      purchase: 'Purchase',
      time: 'Estimated time: About 1 minute after payment completion',
      getGas: 'Get Bitlayer Gas Express',
      getRegular: 'Get Gas Regular',
      after: 'After transferring，you will be able to view the orders',
      history: 'History',
      payment: 'Payment Address',
      timeout: 'QR Code Timeout',
      warning: 'Don&apos;t transfer to an expired address, or you may risk losing tokens',
      amount: 'Payment Amount:',
      cancelWarning:
        'If the order is cancelled, please do not transfer any tokens to the payment address, as it may result in a loss of your funds.',
      ok: 'OK',
      cancel: 'Cancel Order',
      Complete: 'Complete',
      getBitlayerGas:
        'You could get Bitlayer Gas by transferring tokens to the payment address from any supported wallet/exchange.',
      promotionNote1: 'Promotion: only',
      fee: ' $1 ',
      promotionNote2: 'for the Get Gas Express fee, right now!',
      addressNotion:
        'Confirm the blockchain, token, and amount accurately. Each payment address can only be used once. Failure to do so may result in the loss of your deposited funds.',
      promotion: 'Fee Promotion:',
      estimated: 'Estimated time: 10-30 mins',
      ensure: 'Please ensure the following',
      content: `Confirm The <strong>Blockchain, Token, And Amount</strong> Accurately. Each Payment
                Address Can Only Be <strong>Used Once.</strong>
                <div>Failure To Do So May Result In The Loss Of Your Deposited Funds.</div>`,
    },
    leaderBoard: {
      title: 'Bitlayer Leaderboard Competition Epoch 1',
      support: 'Listing Support',
      incentives: 'Incentives',
      liquidity: 'Liquidity Assistance',
      forPartners: 'For Eco-Partners',
      rules: 'Get To Know Our Rules?',
      topTvl: 'Top TVL',
      topTransation24: 'Top Transactions',
      topTransaction: 'Top Transaction',
      topPopularity: 'Top Popularity',
      viewAll: 'View All',
      tryNow: 'Try Now',
      countdown: 'Epoch Countdown',
      stage3: 'Stage 3 Competiton Epoch',
      stage2: 'Stage 2 Preparation Stage',
      gemsMinted: 'Total Gems Minted',
      locked: 'Total Value Locked',
      transactionCount: 'Transaction Count',
      totalLikes: 'Total Likes',
      tvlRanking: 'TVL Ranking List On Bitlayer Mainnet Ecosystem',
      discoverTvl: 'Discover The DApps Built On Bitlayer Mainnet With The Top TVL',
      txnRanking: 'TXN Ranking List On Bitlayer Mainnet Ecosystem',
      discoverTransactions:
        'Discover the DApps Built On Bitlayer Mainnet With The Most Transactions',
      rankingList: 'Popularity Ranking List On Bitlayer Mainnet Ecosystem',
      discoverPopular: 'Discover The Most Popular DApps Built On Bitlayer Mainnet',
      top10: 'Top10 Leaderboard',
      tvl: 'TVL',
      transactions: 'Transactions',
      likes: 'Likes',
      valueUpdate:
        'The total value of effective assets locked in the DApp protocols on the Bitlayer Mainnet, daily update.',
      numberUpdate:
        'The number of effective transactions made by users calling the Dapp contract, daily update.',
      likesUpdate:
        'The accumulated likes of the project during the competition period, real-time update.',
      name: 'Name',
      category: 'Category',
      gemMinted: 'Gems Minted',
      gemsPoints:
        'Gems are calculated based on Total Value Locked (TVL), number of effective transactions, active users amount, Bitlayer popularity leaderboard performance, and product strength. The data will be updated at 00:00 (UTC) every day.',
      boost: 'Boost',
      bindX: 'Bind Your X (Twitter)',
      bindNeed: 'You need to bind an X (Twitter) account first!',
      start: 'Start in May 23rd',
      airdrop: 'Airdrop In Progress!',
      reward: 'Reward',
      rank: 'DApp Ranking',
      dappCenter: 'DApp Center',
      more: 'More',
      introduce: 'Activity Introduction',
      gems: '100% Of Gems',
      distributed: 'Will Be Distributed To Users!',
    },
    dappDetail: {
      reward: 'Reward:',
      activity: 'Activity',
      join: 'Join Now！',
      introduce: 'Activity Introduction',
      whatIs: 'What is {{name}} ?',
      overView: 'Over View',
      dappIntro: 'Introducing Our DApp!',
      event: `Here's how you can join the Airdrop Event:`,
      team: 'Team',
      twitterText:
        'Join with me and try {{name}} on @BitlayerLabs now! Seize the rewards from 100+ #Bitlayer ecosystem dapps!',
      shareTitle: 'Share with friends',
      shareText:
        'Share & Try your favourite projects with your friends now! Become the early birds to seize potential rewards!',
    },
    miningGala: {
      meta: {
        title: 'Win Bitlayer rewards & dapp airdrops',
      },
      head: {
        time: 'May 27th, 2024 13PM UTC - June 10th, 2024 13PM UTC',
        doubleGain: 'Double Gain',
      },
      miningPolls: {
        title: 'Mining Pools',
        claim: 'Claim',
        minted: 'Minted',
        taskTitle: 'Complete Tasks to Mint a Pioneer Badge',
        taskTip: 'Complete at least one transaction with {{project}}',
        shareText: 'Share with your firends',
        or: 'Or',
        hasHelmet: 'Hold Lucky Helmet',
        auditReport: 'audit report',
        miningGuide: 'mining guide',
        airdrop: 'Airdrop',
        detailRules: 'Detail Rules',
        claimSuccess: 'Claim Success!',
        claimFailed: 'Claim failed, Please try again later.',
        bitlayerTask: 'Participate the Bitlayer Mining Gala',
        lorenze: {
          desc: 'The premier platform for Bitcoin liquid restaking token issuance, trading, and settlement through Babylon.',
          tasks: [
            'Stake BTC to the Babylon Pre-launch Staking and get stBTC.',
            'Bridge the stBTC received to Bitlayer Mainnet.',
            'Participate in the Bitlayer DeFi ecosystem with your stBTC.',
          ],
        },
        bitsmiley: {
          desc: 'BTC-Native stablecoin protocol, Initially funded by OKX Ventures & ABCDELabs.',
          tasks: [
            'Mint bitUSD on bitsmiley.',
            'Add liquidity to bitUSD-USDT / bitUSD-WBTC on bitCow.',
            'Stake bitUSD on Defi protocols.',
          ],
        },
        avalon: {
          desc: 'Avalon Finance strives to become the best decentralized lending protocol on BTC layer 2.',
          tasks: [
            'Supply assets on Bitlayer, earn at least 1000 supply points.',
            'Borrow assets on Bitlayer, earn at least 500 borrow points.',
            'Loop your supply and borrowing on Avalon Finance.',
          ],
        },
        bitcow: {
          desc: 'BTC-Native stable and concentrated liquidity AMM.',
          tasks: [
            'Add liquidity to bitUSD-WBTC / bitUSD-USDT trading pairs.',
            'Participate in trading bitUSD-USDT or other pairs.',
            'Create new trading pairs and increase the trading volume.',
          ],
        },
        pell: {
          desc: 'Pell is a decentralized trust marketplace that diversifies BTC and LSD yield, aiming to enhance security for BTC L2 networks.',
          tasks: [
            'Connect your wallet to sign in.',
            'Stake over 0.001 BTC and keep it locked for 7 days.',
            'Lucky draw: 1.5x permanent points card x 1000, 100 USDT x 10.',
          ],
        },
        enzo: {
          desc: 'Enzo Finance is a best-decentralized lending protocol on the BitLayer chain with its cutting-edge algorithmic.',
          tasks: [
            'Deposit / Stake your Bitlayer assets on Enzo Finance.',
            'Borrow Bitlayer assets on Enzo Finance.',
            'Daily 1BTC Giveaway (Requirements: total deposits / loans > $100 USDT).',
          ],
        },
        bitparty: {
          desc: 'BitParty is the first "asset gamified community network" in the BTC ecosystem!',
          tasks: [
            'Obtain Bitlayer assets through the cross-chain bridge.',
            'Stake your Bitlayer assets in Bitparty and earn points to get $BTPX.',
            'Join the groups and seize territory with the others!',
          ],
        },
      },
      shareDialog: {
        title: 'Share with friends',
        desc: 'Share with your friends, participate in Bitlayer Mining Gala, and collect exclusive Bitlayer Mining Gala Pioneer Badges Now!',
      },
      tipDialog: {
        title: 'Kind Tips',
        desc: 'insufficient BTC banlance，you can replenish BTC in the following ways.',
        depositBtn: 'Lightning deposit',
        bridgeBtn: 'Bridge',
      },
      twitterShare: {
        projectShare: `Join with me, participate in {{name}} mining pool on @BitlayerLabs #MiningGala now! {{money}} airdrop waiting for you to share!\n`,
        badgeShare:
          'I just minted my @BitlayerLabs #MiningGala Pioneer Badge here! Enter the page to free mint yours! Join with me to share $24,350,000 airdrops!\n',
      },
      bottomTip: {
        title: 'Useful Tutorials for Bitlayer Mining Gala!',
        desc: 'Welcome to Bitlayer Mining Gala! Check our guidance below and enjoy your mining trip here!',
        tips: [
          'Get Bitlayer Gas in 1 minute.',
          'Tutorial for bridging assets to Bitlayer.',
          'The best way to join Bitlayer Mining Gala.',
          'Bitlayer Mining Gala Pioneer minting guidance.',
        ],
      },
    },
  },
};
