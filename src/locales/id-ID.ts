export default {
  title: 'Bitlayer',
  meta: {
    description: 'Bitcoin security pertama setara layer 2 yang berdasarkan paradigma BitVM.',
  },
  common: {
    readMore: 'Baca Selengka<PERSON>',
    learnMore: 'Info Lengkap',
    upload: 'Unggah',
    submit: '<PERSON><PERSON>',
    return: '<PERSON><PERSON><PERSON>',
    send: '<PERSON><PERSON>',
    back: '<PERSON><PERSON><PERSON>',
    coming: 'Segera hadir',
    connect: 'Hubungkan Wallet',
    disconnect: 'Putuskan',
    switchChain: '<PERSON><PERSON><PERSON> ke {{chainName}}',
    insufficientBalance: 'Saldo tidak mencukupi',
    search: 'cari',
    toTop: 'Ke<PERSON><PERSON> ke Atas',
    getGas: 'Dapatkan Bitlayer Gas',
    bridge: 'Bridge & Hasilkan',
    connectDesc: 'Hubungkan wallet Anda untuk mentransfer token',
    MesonDes: 'Cross-chain yang Aman, Tanpa Biaya & Instan untuk Stablecoin',
    OwltoDes: 'Owlto Finance adalah protokol interoperabilitas yang berpusat pada tujuan',
    OrbiterDes: 'Orbiter Finance adalah bridge cross-rollup yang terdesentralisasi',
    BoolBridgeDes:
      'Bool Bridge adalah bridge terdesentralisasi sebagi penghubung Bitcoin dan Layer 2.',
    OmnityBridgeDes:
      'Omnity adalah bridge yang ada dalam on-chain dan mudah digunakan bagi pemegang token Bitcoin.',
    login: 'Masuk',
    noData: 'Tidak Ada Data',
    confirm: 'Konfirmasi',
    Ok: 'OKE',
    cancel: 'Batal',
    reject: 'Tolak',
    website: 'Situs web',
    share: 'Bagikan',
    more: 'Lebih Banyak',
    less: 'Lebih Sedikit',
    flash: 'Flash Bridge-In',
    getIt: 'Paham',
    congratulations: 'Selamat!',
    error: {
      gasLess: 'Dana gas tidak cukup untuk transaksi ini',
      transactionFailed: 'Transaksi blockchain gagal',
    },
  },
  resources: {
    github: 'https://github.com/bitlayer-org',
    discord: 'https://discord.gg/bitlayer',
    x: 'https://twitter.com/BitLayerLabs',
    linkedin: 'https://bit.ly/42B6v15',
    telegram: 'https://t.me/bitlayerofficial',
    medium: 'https://medium.com/@Bitlayer',
    rankRule:
      'https://medium.com/@Bitlayer/bitlayer-provides-20m-airdrop-for-dapp-leaderboard-competition-rewarding-ecosystem-projects-and-87ed3dc76b94',
  },
  navigation: {
    links: {
      opvm: 'OpVM',
      developers: 'Pengembang',
      users: 'Pggna',
      userCenter: 'Pusat Pembalap',
      resources: 'Sumber Daya',
      contact: 'Hubungi Kami',
      mainnet: 'Jaringan Utama',
      bridgeEarn: 'Bridge & Hasilkan',
      usdcChange: 'Perubahan USDC',
      gas: 'Ambil Gas',
      startBuilding: 'Mulai Membangun',
      btrScan: 'Scan Bitlayer(BTR)',
      addMainnet: 'Tambahkan Mainnet Bitlayer',
      testnet: 'Testnet',
      faucet: 'Faucet',
      testnetBridge: 'Bridge Testnet',
      testnetBridgeHint: 'Bridge Testnet Bitlayer',
      testnetScan: 'Scan Testnet',
      addTestnet: 'Tambahkan Bitlayer Testnet',
      language: 'Bahasa',
      getGas: 'Dapatkan Bitlayer Gas',
      readyPlayerOne: 'Ready Player One',
      luckyHelmet: 'Helm Keberuntungan',
      dAppsLeaderboard: 'Papan Peringkat DApps',
      hint1: '$50.000.000 Airdrop',
      hint2: 'Jadilah penggerak. Earn dari bitlayer',
      hint3: 'Beri suara proyek favoritmu',
      brandKit: 'Kit Merek',
      whitepaper: 'Dokumen Resmi 2.0',
      leaderboard: 'Ppan Prngkt',
      dappCenter: 'Pusat DApp',
      dappCenterHint: 'Daftar Peringkat DApp Bitlayer',
      miningGala: 'Gala Mining',
      bridge: 'Bridge & Earn',
      flash: 'Flash Bridge-In',
      developersSection: {
        hint: 'Mulai membangun di rantai Bitlayer',
        documentation: 'Dokumentasi',
        documentationHint: 'Dokumen pendukung untuk pengembang',
        tools: {
          title: 'Alat Peningkatan',
          hint: 'Alat pengembang untuk proyek Anda',
          mainnetScan: 'Scan Jaringan Utama',
          mainnetScanHint: 'Scan Jaringan Utama Bitlayer(BTR)',
          testnetScan: 'Scan Testnet',
          testnetScanHint: 'Scan Testnet Bitlayer(BTR)',
          faucet: 'Faucet',
          faucetHint: 'Token pilot pada rantai Bitlayer',
          theGraph: 'Grafik',
          theGraphHint: 'Indeks dan cari data Anda di Bitlayer',
          multisigWallet: 'Wallet Multisig',
          multisigWalletHint: 'Simpan kunci pribadi Anda, aman dan mudah',
        },
        security: {
          title: 'Keamanan',
          hint: 'Dukungan keamanan',
          dappSecurityManual: 'Panduan Keamanan Dapp',
          dappSecurityManualHint: 'Tutorial membangun Dapp keamanan',
          auditAlliance: 'Aliansi Audit',
          auditAllianceHint: 'Aliansi Audit',
          securityNetwork: 'Jaringan Keamanan',
          securityNetworkHint: 'Jaringan Keamanan',
        },
        developerSupport: {
          title: 'Dukungan Operasi',
          hint: 'Hibah, pendanaan, insentif dan dukungan',
          readyPlayerOne: 'Ready player I',
          readyPlayerOneHint: 'Dagang dan dapatkan kolam hadiah hingga $100.000',
          luckyHelmet: 'Helm Keberuntungan',
          luckyHelmetHint: 'Buka $50.000.000 untuk builder awal',
          miningGala: 'Gala Mining',
          miningGalaHint: 'Menangkan hadiah Bitlayer & airdrop dapp',
          leaderboard: 'Ppan Prngkt',
          leaderboardHint: 'Papan Pringkt Bitlayer kompetisi Epoch 1',
        },
        hackathon: 'Hackathon',
        hackathonHint: 'Dokumen pendukung untuk pengembang',
        github: 'Github',
        githubHint: 'Jelajahi repositori teknologi Bitlayer',
        devCommunities: 'Komunitas Pengembang',
        devCommunitiesHint: 'Gabung obrolan pengembang',
        telegram: 'Telegram',
        opvm: 'OpVM',
        opvmHint: 'Buat Bitcoin dapat diverifikasi',
      },
    },
    footer: {
      Participate: 'Partisipasi',
      ReadyPlayerOne: 'Ready Player One',
      Build: 'Bangun',
      BuildingBitlayer: 'Membangun Bitlayer',
      Faucet: 'Faucet',
      SupportedWallets: 'Wallet yang Didukung',
      TheGraph: 'Grafik',
      MainScan: 'Scan Bitlayer(BTR)',
      TestScan: 'Scan Testnet',
      About: 'Tentang',
      Solutions: 'Solusi',
      Roadmap: 'Peta jalan',
      EventNews: 'Event & Berita',
      Jobs: 'Pekerjaan',
      Community: 'Komunitas',
      blog: 'Blog',
      Podcast: 'Podcast',
      Operation: 'Operasi',
    },
    walletDrawer: {
      wrongNet: 'Silakan beralih ke {{chainName}}',
      noNFT: 'Tidak Ada Koleksi',
    },
  },
  pages: {
    home: {
      title: {
        notice: 'Bitlayer Ready Player I Sekarang Live! Vote Untuk Point & WL Sekarang!',
        slogon1: 'Perpanjang',
        slogon2: 'Kemungkinan',
        slogon3: 'dari Bitcoin',
        startBuilding: 'Mulai Membangun',
        bridgeEarn: 'Bridge & Hasilkan',
        bridge: 'Bridge',
        competition: 'Pusat DApp Bitlayer',
        dapp100:
          "Dapatkan Hadiah dari <span class='bl-text-xs md:bl-text-2xl bl-text-primary bl-font-[600]'>100+</span> Dapps!",
        gasFee: 'Biaya Gas Rata-Rata:',
        whitePaper: 'Bitlayer White Paper',
        leaderboard:
          "Periksa kelayakan Anda untuk mendapatkan <span class='bl-text-xs md:bl-text-2xl bl-text-white bl-font-[600] bl-px-1'> airdrop Gem </span> sekarang!",
      },
      spotlight: {
        spotlight: 'Sorotan',
        readMore: 'Baca Selengkapnya',
        carousel: {
          item1: {
            type: 'Blog',
            title: 'OpVM',
            content: 'Layer verifikasi Bitcoin bukti masa depan',
          },
          item2: {
            type: 'Event Offline',
            title: 'Bitcoin Bar',
            content:
              'Bersiaplah mendalami dunia mata uang kripto menarik di Bitcoin BAR, diselenggarakan oleh Bitlayer dan Pizza Ninjas! ',
          },
          item3: {
            type: 'Event Offline',
            title: 'Dinner VIP Bitcoin Berikutnya',
            content:
              'Pesta dinner eksklusif yang diselenggarakan oleh Bitlayer selama pekan KBW, selamat mendaftar!',
          },
          item4: {
            type: 'Event Offline',
            title: 'Formula Bitlayer Night',
            content:
              'Bitlayer menyelenggarakan Bitlayer Night selama pekan Token 2049 di Singapura, selamat mendaftar!',
          },
          item5: {
            type: 'Event Airdrop',
            title: 'Pusat DApp Bitlayer',
            content:
              'Rayakan peluncuran Pusat DApp Bitlayerdengan airdrop eksklusif hingga 100.000.000 Poin Bitlayer. ',
          },
          item6: {
            type: 'Event Airdrop',
            title: 'Pusat Pembalap Bitlayer',
            content:
              'Pusat pembalap Bitlayer kini aktif! Jelajahi tugas dan game mini untuk mendapatkan gem dan poin Bitlayer!',
          },
        },
      },
      liveData: {
        liveData: 'Data Langsung',
        explore: 'jelajahi',
        'the community': 'komunitas',
        'of bitlayer': 'dari bitlayer',
      },
      solutions: {
        solutions: 'Solusi',
        title: 'Bagaimana Seharusnya Masa Depan Bitcoin Layer 2',
        subtitle: 'Solusi Pilar Utama untuk masa depan Ekosistem Bitcoin',
        items: [
          {
            title: 'Verifikasi Layer 1',
            text: 'Mewarisi keamanan Bitcoin melalui BitVM',
          },
          {
            title: 'TRUST-MINIMIZED BRIDGE',
            text: 'Menggabungkan DLC & BitVM, menghadirkan model inovatif yang menggantikan multisig tradisional',
          },
          {
            title: 'Kelengkapan Turing',
            text: 'Mendukung banyak VM, memungkinkan Lingkungan yang 100% Kompatibel dengan EVM',
          },
        ],
        whitepaper: {
          title: 'UNDUH LAPORAN RESMI',
          button: 'Laporan Resmi',
        },
      },
      innovations: {
        innovations: 'Inovasi',
        items: [
          {
            title: 'KEAMANAN BITCOIN',
            subtitle: '- OpVM -',
            text: 'OpVM adalah layanan finalitas tanpa trust yang membantu penyelesaian aplikasi blockchain di Bitcoin',
          },
          {
            title: 'BRIDGE TANPA TRUST',
            subtitle: '- Bridge Finalitas -',
            text: 'Bridge finalitas adalah bridge Bitcoin dengan mode trust yang diminimalkan dan front-and-reimberse, didukung oleh layanan OpVM',
            imageDesc:
              'Dengan menggabungkan pertukaran atomik, BTC dapat dipindahkan antara Bitcoin dan L2 tanpa perlu trust, menggantikan cara multisig lama',
          },
          {
            title: 'EVM REALTIME',
            subtitle: '- RtEVM -',
            text: 'Dengan eksekusi paralel dan akses status cepat, RtEVM mencapai hasil super tinggi dan konfirmasi transaksi hampir real-time',
            imageDesc1:
              'Penjadwal mengirim transaksi secara optimis ke beberapa pelaksana EVM, memanfaatkan penuh multi-utas',
            imageDesc2:
              'Menyertakan penyimpanan KV yang terbagi ke trie MPT yang terukur, menyediakan kapasitas tak terbatas dan akses status yang sangat cepat',
          },
        ],
      },
      roadmap: {
        roadmap: 'Peta jalan',
        title: 'Perjalanan Bitcoin',
        title2: 'Perjalanan Bitlayer',
        points: [
          {
            title: 'Koin Berwarna',
            description:
              'Koin Berwarna menandai token blockchain untuk mewakili aset dunia nyata untuk pengelolaan yang aman dan transparan.',
            date: '2012',
          },
          {
            title: 'Pihak Lawan',
            description:
              'Counterparty menambahkan token khusus dan kontrak pintar ke Bitcoin untuk membangun dan memperdagangkan aset digital.',
            date: '2014',
          },
          {
            title: 'Spells of Genesis',
            description:
              'Game seluler berbasis Blockchain Spells of Genesis memadukan kartu trading dengan elemen arcade dalam suasana fantasi.',
            date: '2015',
          },
          {
            title: 'PEPE Langka',
            description:
              'Kartu trading digital yang menampilkan Pepe the Frog dibuat dan dipertukarkan di blockchain Bitcoin, melambangkan seni kripto yang unik dan dapat dikoleksi.',
            date: '2016',
          },
          {
            title: 'Pembaruan Segwit',
            description:
              'Pembaruan protokol Segregated Witness meningkatkan kapasitas blok dan meningkatkan skalabilitas dengan memisahkan data tanda tangan dari data transaksi.',
            date: '2017',
          },
          {
            title: 'Pembaruan Taproot',
            description:
              'Pembaruan protokol Taproot Bitcoin meningkatkan privasi, efisiensi, dan kemampuan kontrak pintar melalui skema tanda tangan baru yang disebut Schnorr Signatures.',
            date: '2021',
          },
          {
            title: 'Ordinal',
            description:
              'Protokol Ordinals menuliskan artefak digital unik langsung ke masing-masing Satoshi di blockchain Bitcoin, memungkinkan fungsionalitas seperti NFT.',
            date: '2023',
          },
        ],
        journeys: [
          {
            title: 'Jaringan Utama V1',
            texts: [
              'Model keamanan terbaik untuk mendaftarkan pengembang dan pengguna: sidechain PoS + MPC-TSS/Native Multisig',
            ],
            subtitle: '45383',
          },
          {
            title: 'Jaringan Utama V2',
            texts: [
              'Bertransformasi ke model yang setara dengan rollup: model keamanan terobosan untuk menjembatani aset, permainan menantang verifikasi L1 tanpa BitVM',
              'Bridge Finalitas',
            ],
            subtitle: '45536',
          },
          {
            title: 'Jaringan Utama V3',
            texts: [
              'Gambaran lengkap: Game menantang verifikasi L1 dengan skema BitVM, memungkinkan kesetaraan keamanan Bitcoin',
              'EVM Realtime',
            ],
            subtitle: '45809',
          },
        ],
      },
      events: {
        tag: 'Event & Berita',
        title: 'Event & Berita Ekosistem Bitlayer',
        subtitle: 'Tindak lanjuti pembaruan kami',
        items: [
          'Bitcoin 2024 Nashville',
          'Token 2049',
          'Bitlayer Night Korea',
          'Bitcoin Singapore',
        ],
      },
      jobs: {
        tag: 'Pekerjaan',
        title: 'Lowongan Kerja',
        subtitle: 'Mari Membuat Sejarah Bitcoin Bersama!',
        positions: [
          {
            title: 'Kepala Hubungan Pengembang',
            skills: [
              'Bertanggung jawab untuk memperluas dan memelihara hubungan pengembang dalam ekosistem blockchain publik.',
              'Menangani komunikasi teknis dan pendidikan pengembang dengan berbagai komunitas dan ekosistem teknis pengembang global.',
            ],
          },
          {
            title: 'Peneliti Protokol_',
            skills: [
              'Melakukan penelitian mutakhir tentang blockchain berdasarkan permasalahan industri.',
              'Menulis laporan riset dan makalah berkualitas tinggi',
            ],
          },
        ],
        more: 'Jelajahi posisi kosong',
      },
      community: {
        tag: 'Komunitas',
        title: 'Bergabunglah dengan komunitas masa depan',
        subtitle: 'Tetap aktif dengan Bitlayer',
        items: [
          {
            title: 'Berkontribusi untuk Bitlayer',
            description: 'Membangun bersama Kami',
          },
          {
            title: 'Bergabung di Discord',
            description: 'Bicara dengan kami',
          },
          {
            title: 'Ikuti di X',
            description: 'Berkomunikasi dengan Kami',
          },
          {
            title: 'Terhubung di LinkedIn',
            description: 'Terhubung dengan kami',
          },
          {
            title: 'Bergabung dengan Telegram kami',
            description: 'Mengobrol bersama kami',
          },
          {
            title: 'Dapatkan info terbaru di Medium',
            description: 'Belajar bersama kami',
          },
        ],
      },
      investors: {
        investors: 'Investor kami',
        'Co-lead': 'Co-Leads',
      },
      ecosystem: {
        ecosystem: 'Ekosistem',
        top: 'Teratas',
        Dapps: 'Dapps',
        on: 'pada',
        Bitlayer: 'Bitlayer',
        'Dapp Center': 'Pusat Dapp',
        lending: 'Pemberian pinjaman',
        dex: 'DEX',
        staking: 'Staking',
        gaming: 'Gaming',
        'stable coins': 'Koin stabil',
      },
    },
    developers: {
      title: {
        titleLine1: 'Selamat Datang Di',
        titleLine2: 'Dokumen Bitlayer',
        subtitle: 'Panduan untuk bergabung dengan ekosistem Bitlayer Oleh builder untuk builder',
        buildNow: 'Bangun sekarang',
        blog: 'Blog',
      },
      faq: {
        title: 'TJU',
        name1: 'Apa itu Bitlayer?',
        desc1:
          'Bitlayer bertindak sebagai solusi Layer 2 untuk Bitcoin, dengan 100% kompatibilitas EVM dan toolchain Ethereum, dengan BTC sebagai token asli (token gas). Bitlayer dapat mengaktifkan aplikasi dan pengembang dari ekosistem Ethereum yang ada untuk bermigrasi ke Bitlayer dengan biaya rendah, menghilangkan kebutuhan akan modifikasi atau penulisan ulang yang besar.',
        name2: 'Apakah Bitlayer kompatibel dengan Wallet Bitcoin yang ada?',
        desc2:
          'Ya, Bitlayer kompatibel dengan dompet yang ada seperti Metamask, Unisat, atau wallet lain yang kompatibel dengan Bitcoin/Ethereum, memungkinkan pengguna berinteraksi dengan dana dan aset mereka di Bitlayer dengan lancar.',
        name3: 'Bisakah pengembang memigrasikan proyek mereka yang ada ke Bitlayer?',
        desc3:
          'Ya, Bitlayer mendukung pengembang yang ada dengan menawarkan kompatibilitas EVM, memungkinkan migrasi yang lancar dan penerapan proyek yang ada dengan biaya rendah. Pengembang dapat memperoleh kemudahan migrasi kontrak pintar yang ditulis dalam Solidity, Vyper, atau bahasa lain apa pun yang mengompilasi bytecode EVM langsung ke Bitlayer, menggunakan toolchain yang Anda kenal: Ethereum JSON-RPC, Hardhat, dll.',
        name4: 'Bagaimana saya bisa membantu mendukung Bitlayer?',
        desc4: `Ada beberapa cara untuk mendukung Bitlayer. Anda dapat berpartisipasi aktif dalam diskusi komunitas, memberikan masukan dan saran, berkontribusi pada pengembangan aplikasi atau alat di platform, atau mempromosikan Bitlayer kepada orang lain yang mungkin mendapat manfaat dari layanannya. Selain itu, Anda dapat menjelajahi inisiatif atau program dukungan spesifik apa pun yang mungkin dimiliki Bitlayer. Klik untuk mengetahui lebih banyak tentang <span data-link-index='0' class='bl-underline bl-cursor-pointer'>Ready Player One</span>, sebuah program yang memberikan insentif sebesar $50.000.000 untuk pembuat dan kontributor awal.`,
      },
      feature: {
        title: 'Kekuatan Kami',
        title1: 'Kelengkapan-Turing',
        desc1: 'Mendukung banyak VM, memungkinkan Lingkungan yang 100% Kompatibel dengan EVM',
        title2: 'Peg 2 Arah Tanpa Trust',
        desc2:
          'Menggabungkan DLC & BitVM, menghadirkan model inovatif yang menggantikan multisig tradisional',
        title3: 'Verifikasi Layer 1',
        desc3: 'Mewarisi keamanan Bitcoin melalui BitVM',
      },
      quickstart: {
        title: 'Quickstart Pengembang',
        subtitle: 'Buka ekosistem Bitlayer dengan panduan yang dibuat oleh builder, untuk builder',
        title1: 'Hubungkan Wallet Anda ke Bitlayer Testnet',
        skill1:
          'Tambahkan konfigurasi Bitlayer Testnet ke wallet Anda dan interaksi dengan Dapps di Bitlayer Testnet',
        title2: 'Kompilasi, Jalankan, dan Terapkan',
        skill2:
          'Panduan ini memandu Anda dalam mengompilasi dan menjalankan Bitlayer hingga penerapan',
        title3: 'Perspektif Baru dan Pembaruan Terbaru Menanti',
        skill3: 'Nantikan pembaruan mendetail tentang teknologi dan wawasan pasar di blog kami',
        readMore: 'Baca Selengkapnya',
      },
      intro: {
        title: 'PETA JALAN',
        subtitle:
          'Panduan Builder untuk Bergabung dengan Ekosistem Bitlayer: Dibuat oleh Builder, untuk Builder.',
        buildNow: 'Bangun Sekarang',
      },
      tools: {
        title: 'Alat Pengembang',
        subtitle:
          'Manfaatkan rangkaian alat dalam ekosistem Bitlayer untuk memaksimalkan potensi Anda di Bitlayer',
        name1: 'Faucet',
        desc1: 'Dapatkan token Bitlayer Testnet Anda setiap 24 jam untuk pengembangan di sini',
        name2: 'Scan Jaringan Utama',
        desc2: `Alat penting untuk menjelajahi dan menganalisis data blockchain di Bitlayer Mainnet. Anda dapat mendalami transaksi, blok, dan alamat.`,
        name3: 'Scan Testnet',
        desc3:
          'Alat penting untuk menjelajahi dan menganalisis data blockchain di testnet. Anda dapat mendalami transaksi, blok, dan alamat testnet',
        name4: 'Grafik',
        desc4: 'Indeks dan akses data blockchain real-time di Bitlayer.',
        website: 'Situs Web',
        more: 'Jelajahi Alat Lainnya',
      },
      security: {
        title: 'Keamanan',
        subtitle: 'Gunakan lapisan keamanan untuk membangun cepat aplikasi yang aman dan andal',
        name1: 'Panduan Keamanan Dapp',
        desc1: `Tingkatkan efisiensi pengembangan modul keamanan aplikasi dengan mengikuti panduan keamanan.`,
        buttonLabel1: 'Baca dokumen',
        // name2: 'Audit alliance',
        name2: 'Jaringan Keamanan',
        desc2: `Hubungi penyedia layanan keamanan ternama secepatnya untuk menjaga keamanan dan meningkatkan nilai aplikasi Anda.`,
        buttonLabel2: 'Baca dokumen',
        name3: 'Alat Opensource',
        desc3: 'Lakukan deteksi mandiri cepat menggunakan alat keamanan open-source.',
        buttonLabel3: 'Jelahailah',
      },
      operationalSupport: {
        title: 'Dukungan Operasi',
        subtitle: 'Hibah, pendanaan, insentif dan dukungan',
        name1: 'Hibah Ready Player',
        desc1:
          'Membuka Potensi Bitlayer: Mencari Proyek Inovatif dan Tim Hebat untuk Memajukan Ekosistem BTC.',
        name2: 'Program Insentif',
        desc2: `Ready Player I——Insentif sebesar $50.000.000 untuk builder dan kontributor awal.`,
        name3: 'Sumber Ops & MKT',
        desc3:
          'Dukungan sumber daya resmi & Dukungan sumber daya pasar global. Papan peringkat, Pusat DApp, Pusat Pembalap.',
        name4: 'Kampanye Pertumbuhan Ekosistem',
        desc4: 'Gala Mining, Suara Bitlayer, Konferensi Kripto Global.',
        participate: 'Partisipasi',
        more: 'Dukungan Lainnya',
      },
      cards: {
        name1: 'Mulai Cepat',
        desc1:
          'Bitlayer adalah solusi Layer 2 pertama untuk Bitcoin yang memberikan keamanan setara dengan Bitcoin dan kelengkapan Turing. Pelajari cara membuat dApps dengannya.',
        name2: 'Arsitektur Bitlayer',
        desc2:
          'Mulailah dengan Jaringan Bitlayer dan pahami fitur-fitur uniknya. Pelajari tentang arsitektur jaringan dan perbedaannya dengan semua blockchain lainnya.',
        name3: 'Peta Jalan Bitlayer',
        desc3: `Visi Bitlayer akan terwujud melalui peluncuran mainnet dalam beberapa tahap, yang setiap fasenya didesain untuk meningkatkan pengalaman pengguna.`,
        readDoc: 'Baca Dokumen',
      },
      connect: {
        title: 'Terhubung dengan Kami',
        subtitle:
          'Tetap terinformasi tentang berita dan perkembangan terkini dalam Komunitas Bitlayer',
      },
    },
    bridge: {
      // transfer
      bridgeTitle: 'Bridge & Hasilkan',
      gasTitle: 'Dapatkan Bitlayer Gas',
      gasPromotion: 'Mulai Cepat - Raih Bitlayer Gas 1 Menit!',
      newToBridge: 'Masih baru dengan bridge?',
      from: 'Dari',
      to: 'Ke',
      amount: 'Jumlah',
      balance: 'Saldo: {{balance}}',
      available: 'Tersedia: {{balance}}',
      transferable: 'Inskripsi yang dapat ditransfer',
      max: 'Maks',
      recipientAddress: 'Alamat Penerima',
      recipientTip: 'Sambungkan wallet untuk menerima token',
      est: 'Perkiraan: {{time}}',
      fee: 'Biaya:',
      total: 'Total:',
      receivepPlaceholder: 'Silakan masukkan alamat {{chainName}}',
      transfer: 'Transfer',
      transferProgress: 'Transfer sedang berlangsung',
      approve: 'Setuju',
      approveInProgress: 'Menunggu persetujuan Anda',
      checkingAllowance: 'Memeriksa saldo',
      minLimit: 'Minimum {{amount}} {{symbol}} per transaksi',
      maxLimit: 'Maksimum {{amount}} {{symbol}} per transaksi',
      invalidAmount: 'Jumlah tidak valid',
      invalidAddress: 'Alamat tidak valid',
      switchChainDesc:
        'Beralih ke jaringan {{chainName}} memerlukan penggunaan wallet {{networkType}}.',
      transferFailed: 'Transfer Gagal',
      connectDesc: 'Hubungkan wallet Anda untuk mentransfer token',
      bridgeTip: 'Bridge ke Bitlayer, Tahan Helm Keberuntungan Bitlayer, Bagikan 400K Poin BWB!',
      historyTab: 'Riwayat',
      getGas: 'Dapatkan Gas Express',
      swap: 'Tukar',
      gasLimit: `Saldo ≥{{maxBalance}} tidak dapat ditukar`,
      dayLimit: `BTC yang tersisa hari ini adalah ({remainSwapAmount}}`,
      // history
      loadingData: 'Memuat Data',
      noData: 'Tidak Ada Data',
      sender: 'pengirim',
      receiver: 'penerima',
      transactionHash: 'Hash Transaksi',
      Pay: 'Anda membayar',
      gasPay: 'Bayar',
      gasGet: 'Dapatkan',
      Balance: 'Saldo:',
      Max: 'Maks',
      SwitchToBitlayer: 'Beralih ke Bitlayer',
      Maximum: 'Maksimum {{maxSwapAmount}} BTC per transaksi',
      History: 'Riwayat',
      Receive: 'Anda menerima',
      Fee: 'Biaya',
      Total: 'Total:',
      Slippage: 'Slippage:',
      ThirdPartyBridge: 'Bridge Pihak Ketiga',
      ConfirmSwap: 'Konfirmasi Tukar',
      ApproveToken: 'Setujui token',
      ApproveGas: 'Setujui tanda tangan tanpa membayar gas',
      ConfirmSwapRadio: 'Konfirmasikan pertukaran radio',
      ConfirmGas: 'Konfirmasikan pertukaran radio tanpa membayar gas',
      Processing: 'Memproses...',
      Completed: 'Selesai',
      From: 'Dari',
      To: 'Ke',
      Return: 'Kembali',
      thirdParty: {
        tipTitle: 'Pengingat',
        tipContent:
          'Bridge EVM Bitlayer saat ini adalah versi beta. Harap gunakan layanan bridge pihak ketiga untuk bridge jumlah kecil.',
      },
      btcHint: 'Hanya mendukung BTC dan aset protokol Ordinal',
      btcCaution: {
        title: 'Konfirmasi Transaksi',
        message:
          'Transaksi ini akan menggunakan Input berikut. Konfirmasi apakah Input ini tidak memuat aset lain.',
      },
      inscribeTransfer: 'Tulis TRANSFER',
      inscribedTitle: 'Transaksi Terkirim',
      inscribedTip: 'Penulisan akan dimulai setelah konfirmasi.',
      viewOnExplorer: 'Lihat di Penjelajah',
      refresh: 'Segarkan',
      pending: 'Tertunda',
      selectAll: 'Pilih Semua',
      usdc: {
        change: 'Perubahan USDC',
        changeLabel: 'Perubahan token Anda:',
        changeDesc:
          'Token ini diperbarui menurut kontrak pintar asli Circle. Alamat kontrak USDC baru:',
        confirm: 'Konfirmasi & Hadiah',
        learnMore: 'Lihat Detail Lainnya',
        bonusForYou: 'Poin Bonus Bitlayer buat Anda!',
        tips: 'Tip Bermanfaat',
        lendingDesc:
          'Kami mendeteksi Anda mempunyai pinjaman USDC di platform pinjaman. Lunasi pinjaman terlebih dahulu.',
        borrowingTitle: 'Kondisi peminjaman USDC.e',
        viewOn: 'Lihat di {{ platform }}',
        insufficentBalance: 'Saldo USDC tidak cukup. Silakan hubungi kami.',
      },
      errors: {
        invalidRequest: 'Permintaan tidak valid.',
        insufficentBtc:
          'Saldo BTC tidak cukup atau UTXO tidak tersedia. Jika ada transaksi yang belum dikonfirmasi, harap tunggu hingga transaksi dikonfirmasi dan coba lagi.',
        insufficentRune:
          'Saldo Rune tidak cukup atau UTXO tidak tersedia. Jika ada transaksi yang belum dikonfirmasi, harap tunggu hingga transaksi dikonfirmasi dan coba lagi.',
        insufficentBrc20:
          'Saldo BRC20 tidak cukup atau UTXO tidak tersedia. Jika ada transaksi yang belum dikonfirmasi, harap tunggu hingga transaksi dikonfirmasi dan coba lagi.',
        buildTxFailed: 'Gagal membuat transaksi.',
        transferError: 'Gagal mentransfer.',
        internalError: 'Kesalahan layanan internal.',
        swapError: 'Gagal menukar.',
        insufficientFunds:
          'Dana tidak cukup: Total biaya (gas * biaya gas + nilai) untuk mengeksekusi transaksi ini melebihi saldo akun.',
      },
    },
    faucet: {
      title: 'Faucet Testnet',
      description:
        'Dapatkan token Bitlayer Testnet setiap 24 jam untuk pengembangan. Token Testnet tidak memiliki nilai finansial dan tidak dapat diperdagangkan dengan harga sebenarnya.',
      selectField: {
        label: 'Pilih Token',
        placeholder: 'Pilih Token',
      },
      textFiled: {
        label: 'Alamat Wallet',
        placeholder: 'Masukkan alamat Bitlayer Testnet Anda',
      },
      result: {
        gotTip: 'Anda mendapat {{tokens}}!',
        gotAnother: 'Anda dapat meminta {{token}} lainnya dalam 24 jam.',
        sending: 'Mengirim...',
      },
      error: {
        testAddress: 'Silakan masukkan alamat testnet bitlayer Anda',
        verifyCaptcha: 'Silakan selesaikan verifikasi captcha',
        verifyFailed: 'Gagal memverifikasi captcha',
        exceededLimit:
          'Anda telah meminta token dalam 24 jam terakhir. Harap tunggu {{h}}{{m}}{{s}} sebelum mencoba lagi.',
        invalidAddress: 'Silakan masukkan alamat testnet bitlayer yang valid',
        unexpectedError: 'Terjadi kesalahan server yang tidak terduga. Silakan coba lagi nanti.',
      },
    },
    readyplayone: {
      title: 'Ready Player One',
      description:
        'Dapatkan insentif sebesar <text>$50,000,000</text> untuk builder dan kontributor awal.',
      register: 'Daftar',
      time: '29 Maret 2024 - 10 Mei 2024',
      airdrop: 'Insentif hingga $1 Juta Per Proyek!',
      Volume: 'Volume:',
      rules: {
        title: 'Aturan',
        ruleContents: [
          {
            title: 'Daftar',
            texts: ['Pastikan pendaftaran selesai paling lambat tanggal 10 Mei.'],
            date: 'Maret-April',
          },
          {
            title: 'Luncurkan',
            texts: ['Mulai peluncuran di mainnet dan bersiaplah untuk kompetisi yang akan datang.'],
            date: 'April-Mei',
          },
          {
            title: 'Airdrop',
            texts: [
              'Bergabunglah dalam kompetisi papan peringkat untuk memenangkan token airdrop senilai lebih dari $50 juta.',
            ],
            date: 'Juni-Juli',
          },
        ],
      },
      colead: 'Wakil Pemimpin',
      investors: 'Investor',
      partners: 'Mitra',
      setp: {
        register: {
          schemaName: 'Harap berikan nama proyek',
          schemaWebsite: 'Harap berikan situs web proyek',
          schemaProjectDemo: 'Harap berikan demo proyek',
          schemaContact: 'Harap berikan nama kontak',
          schemaEmail: 'Harap berikan email proyek',
          schemaTelegram: 'Harap berikan telegram proyek',
          schemaAgree: 'Harap setujui syarat dan ketentuan',
          newBuilderType: 'Builder Baru',
          experiencedBuilderType: 'Builder Berpengalaman',
          formValidError:
            'Kami menemukan beberapa masalah dalam formulir Anda. Harap tinjau bidang yang disorot dan lakukan koreksi sebelum mengirim ulang.',
          apiClientError:
            'Kami menemukan beberapa masalah dalam formulir Anda. Harap tinjau kolomnya dan coba kirimkan lagi. ({{code}})',
          apiServerError:
            'Tampaknya ada kesalahan sementara di pihak kami. Harap refresh halaman atau coba lagi dalam beberapa menit. ({{code}})',
          formOne: {
            title: 'Informasi Proyek',
            name: {
              label: 'Nama proyek',
              placeholder: 'Harap masukkan nama',
            },
            builderType: {
              label: 'Jenis Builder',
              placeholder: 'Harap pilih jenis builder',
            },
            fundInfo: {
              label:
                'Info Dana Proyek: (Jelaskan status pendanaan proyek dalam 500 karakter atau berikan tautan detail.)',
              placeholder: 'Harap masukkan info dana',
            },
            projectDemo: {
              label: 'Demo Proyek',
              placeholder: 'Harap masukkan tautan demo',
            },
            pitchDeck: {
              label: 'Dek Pitch Proyek (Hanya mendukung format pdf)',
              placeholder: 'Pilih file yang akan diunggah',
            },
            twitter: {
              label: 'Twitter proyek',
              placeholder: 'Harap masukkan id',
            },
            website: {
              label: 'Situs web proyek',
              placeholder: 'Harap masukkan URL',
            },
            logo: {
              label: 'Proyek logo <span>(disarankan rasio 1 banding 1)</span>',
              placeholder: 'Pilih file yang akan diunggah',
            },
            description: {
              label:
                'Deskripsi/Dek Proyek: Ringkas proyek Anda (tujuan, masalah yang terpecahkan, cara kerjanya) dalam 500 karakter atau berikan tautan dek.',
              placeholder: 'Harap masukkan deskripsi',
            },
            category: {
              label: 'Apa kategori proyek Anda?',
              placeholder: 'Harap pilih',
              selects: [
                'Infra',
                'Defi',
                'Sosial',
                'Inskripsi',
                'NFT',
                'Token MeMe',
                'DAO',
                'Peralatan',
                'Dompet',
                'Cross-chain',
                'Komputasi privasi',
                'Token Privasi',
                'Pasar Prediksi',
                'Penyimpanan',
                'Gamefi',
                'Hiburan',
                'Metaverse',
                'Lainnya',
              ],
            },
            stage: {
              label: 'Tahap proyek',
              placeholder: 'Harap pilih',
              selects: ['Fase ide', 'Dalam pengembangan', 'Purwarupa tersedia', 'Langsung/Beta'],
            },
            coin: {
              label:
                'Alamat token di Bitlayer <span>(jika tidak tersedia, harap isi dalam N/A)</span>',
              placeholder: 'Harap masukkan alamat token',
            },
            lockUp: {
              label:
                'Alamat terkunci di Bitlayer <span>(jika tidak tersedia, harap isi dalam N/A)</span>',
              placeholder: 'Harap masukkan alamat kunci',
            },
            contract: {
              label:
                'Alamat kontrak Dapp di Bitlayer <span>(jika tidak tersedia, harap isi dalam N/A)</span>',
              placeholder: 'Harap masukkan alamat kontrak',
            },
            defilama: {
              label:
                'Tautan Proyek Defillama <span>(jika tidak tersedia, harap isi dalam N/A)</span>',
              placeholder: 'Harap masukkan tautan defillama',
            },
          },
          formTwo: {
            title: 'Informasi Tim',
            team: {
              label:
                'Berapa jumlahnya? Buat daftar nama, peran, dan latar belakang anggota tim utama.',
              placeholder: 'Harap masukkan deskripsi',
            },
          },
          formThree: {
            title: 'Narahubung',
            name: {
              label: 'Nama',
              placeholder: 'Harap masukkan nama',
            },
            email: {
              label: 'Alamat email',
              placeholder: 'Harap masukkan alamat email',
            },
            telegram: {
              label: 'Telegram',
              placeholder: 'Harap masukkan url',
            },
            twitter: {
              label: 'Profil Twitter',
              placeholder: 'Harap masukkan url',
            },
          },
          consent: {
            title: 'Izin',
            label:
              'Saya setuju dengan syarat dan ketentuan, kebijakan privasi, dan kode etik hackathon.',
          },
        },
        launch: {
          title: 'Selamat, Anda berhasil mendaftar!',
          documents: 'Dokumen pengembang',
          deploy: 'Sekarang Anda dapat meluncurkan proyek Anda di bitlayer!',
          scanCode: 'Pindai untuk bergabung dengan komunitas',
          next: 'Berikutnya',
        },
        airdrop: {
          deploymentTip: 'Jika peluncuran proyek Anda sudah selesai, bagus sekali! ',
          description:
            'Anda bisa mengikuti pemilihan utama! Pemenang akhir akan diberi hibah antara $10.000 hingga $300.000. Bonus tambahan termasuk hadiah kehormatan sebesar $3.000 hingga $5.000 dan akses ke kolam hadiah sebesar $1 juta untuk penyelenggaraan event!',
        },
      },
      gems: {
        title: 'Airdrop Gem Bitlayer',
        gems: 'Gem',
        tips: ' akan segera didistribusikan!',
        button: 'Periksa kelayakan saya',
        claimGems: {
          button: 'Klaim Gem',
          label1: 'Anda memenuhi syarat untuk ',
          label2: 'Airdrop gem!',
          'failed-title': 'Ayo Teruskan!',
          'label-failed':
            'Anda tidak memenuhi syarat mendapatkan airdrop gem di babak ini. Nantikan event lainnya!',
          'button-failed': 'Dapatkan Hadiah dari Dapps',
        },
      },
      subTitle: 'Program Hibah Booster Bitlayer',
      descriptionV4:
        'Adil dan Transparan, Tinjauan Bulanan, Hingga <text> $1 Juta </text> Insentif per Proyek!',
      incentives: {
        tag: 'Insentif',
        'New Builder': {
          title: 'Builder Baru',
          bonus: 'Gelar kehormatan dan hadiah sebesar <span>{{amount}}</span>',
        },
        'Experienced builder': {
          title: 'Builder Berpengalaman',
          bonus: '<span>{{amount}}</span> kolam hadiah event eksklusif',
        },
        grants: 'Hibah',
        bonus: 'Bonus',
      },
      rulesV4: {
        tag: 'Aturan',
        ruleContents: [
          {
            title: 'Pendaftaran',
            texts: [
              'Proyek yang terdaftar sebelum tanggal 30 setiap bulannya akan dievaluasi pada bulan berjalan',
              'Proyek yang terdaftar setelah tanggal 30 akan dievaluasi pada bulan berikutnya',
              'Pastikan memberikan cukup informasi saat melakukan pendafataran',
            ],
            date: 'Bulanan',
          },
          {
            title: 'Pemilihan Utama',
            texts: [
              'Komite peninjau Bitlayer akan melakukan peninjauan menyeluruh menurut informasi yang dikirim.',
              'Tinjauan lanjutan dapat mencakup pemeriksaan teknis, riset pasar, dan analisis produk.',
            ],
            date: 'Estimasi 7 hari',
          },
          {
            title: 'Pemilihan Akhir',
            texts: [
              'Bitlayer Foundation, bersama dengan penilai dari lembaga investasi, akan mengevaluasi proyek yang lolos ronde utama.',
              'Setiap proyek akan membuat presentasi 10-15 menit',
              'Proyek pemenang akan menerima hibah sebesar $10.000 hingga $300.000',
            ],
            date: 'Estimasi 14 hari',
          },
        ],
      },
      currentShortlist: {
        tag: 'Daftar Singkat Kini',
        primaryElection: 'Pemilihan Utama',
        finalElection: 'Daftar Akhir',
        like: 'Suka',
        grant: 'Hibah',
      },
      updates: {
        tag: 'Pembaruan',
      },
    },
    luckyhelmet: {
      title: 'Helm Keberuntungan',
      description: 'Jadilah penggerak. Earn dari Bitlayer.',
      toGet: 'Bagaimana cara mendapatkannya?',
      miningBtn: 'Mint Dibuka 12:00 pm UTC pada 8 Mei',
      assetTitle: 'Aset Di Layer 1',
      assetDesc: 'Aset Ordinal Bitcoin Asli pada Bitcoin Layer 1',
      mintTitle: 'Mint Pada Layer 2',
      mintDesc: 'Biaya gas lebih rendah dan efisiensi lebih baik jika dibandingkan dengan layer 1',
      desc1: 'Pengalaman L1/L2 Yang Mulus',
      desc2: 'Keuntungan Bitlayer Golden Shovel',
      minting: 'Melakukan Mint',
      mint: 'Mint',
      checkWhitelist: 'Periksa Whitelist',
      minted: 'Di-mint',
      mintingSuccess: 'Minting Sukses',
      congratulations: 'Selamat!',
      success: 'Anda termasuk dalam whitelist.',
      sorry: 'Maaf',
      failure: 'Anda tidak termasuk dalam whitelist.',
      finished: 'Selesai',
      whitelistOnly: 'Hanya whitelist',
      trade: 'Trade Helm Keberuntungan',
    },
    rank: {
      head: {
        head1: 'Pool Hadiah',
        head2: 'Dapps',
        head3: 'Total Suara',
        head4: 'Suara Harian',
      },
      banner: {
        tag: 'Tahap Persiapan',
        title: 'READY PLAYER ONE',
        subtitle: 'token untuk builder',
        link: 'Mengenal Aturan Kami?',
        all: 'Semua kategori',
        switch: 'Beralih ke',
        register: 'Daftarkan Dapp',
      },
      list: {
        votes: 'SUARA',
        vote: 'Beri suara',
        out: 'Kehabisan suara',
        invite: 'Undang untuk Poin',
      },
      carouse: {
        title: 'Poin Ready Player I saya',
        note: 'Harap diperhatikan: Poin Ready Player I tidak setara dengan Poin Bitlayer. Poin tersebut dapat ditukarkan dengan harga tertentu di masa depan. Ikuti kami untuk pembaruan!',
        accepted: 'Undangan Diterima',
        taskCenter: 'Pusat Tugas',
        now: 'Ikuti Sekarang',
        vote: 'Setiap Suara',
        invite: 'Setiap Undangan',
        follow: 'Ikuti di X',
        hint1: 'Hingga 3 Kali Sehari',
        hint2: 'Tugas Satu Kali',
      },
      dialog: {
        band: {
          title: 'Undang Teman untuk Mendorong',
          sorry:
            '"Maaf! Twitter Anda sudah terhubung ke alamat lain. Silakan gunakan akun Twitter lain',
        },
        invite: {
          need: 'Anda perlu menyambungkan akun X (Twitter) terlebih dahulu! Dapatkan 500 Poin untuk setiap undangan yang berhasil sekarang!',
          auth: 'Otorisasi di X',
          attention:
            'Perhatian: Satu akun Twitter hanya dapat ditautkan ke satu alamat untuk menghindari kesalahan',
        },
        note: {
          please: 'Harap diperhatikan:',
          note: 'Poin Ready Player I tidak setara dengan Poin Bitlayer. Poin tersebut dapat ditukarkan dengan harga tertentu di masa depan. Ikuti kami untuk pembaruan!',
        },
        rules: {
          rules1: 'Dapatkan 300 poin setiap hari dengan menyelesaikan tiga pemberian suara.',
          rules2: 'Ikuti Twitter resmi untuk menerima 500 poin.',
          rules3:
            'Setiap pengguna undangan baru yang berhasil memberi suara akan ditambahkan 500 poin tambahan ke penghitungan Anda.',
          rules4:
            'Pengundian acak akan menghadiahkan Helm Keberuntungan 100 Bitlayer, dengan peringkat yang lebih tinggi akan meningkatkan peluang Anda.',
          vote: 'Beri Suara Proyek Favorit Anda',
          note: 'Bergabunglah dengan voting Daftar Popularitas Bitlayer untuk mendukung proyek favorit Anda! Periode Event : 23 April 2024 hingga 10 Mei 2024. Login setiap hari ke halaman Papan Peringkat, dengan masing-masing Pengguna memiliki tiga kesempatan memilih per hari. Dapatkan poin ekstra dengan mengikuti Twitter resmi dan mengundang pengguna baru. Peserta aktif akan mengakumulasi Poin Ready Player I dan berpeluang masuk Whitelist untuk Helm Keberuntungan Bitlayer.',
          rewards: 'Hadiah:',
          boost: `Tingkatkan popularitas proyek Anda, pengaruhi peringkat papan peringkat akhir, dan secara aktif berpartisipasi untuk memenangkan hadiah resmi NFT!`,
          gotIt: 'Mengerti',
        },
        share: {
          copy: 'Salin Tautan',
          share: 'Bagikan di',
          invite: 'Undang untuk memberi suara',
          more: 'Dapatkan lebih banyak vote dari teman',
          connect: 'Hubungkan X (Twitter)',
        },
        vote: {
          vote: 'Beri suara',
          voteFor: 'beri suara untuk',
          daily: '3 suara setiap hari',
          votes: 'Suara',
        },
        expire: {
          late: 'Maaf Anda terlambat!',
          expired: 'Pengambilan suara telah berakhir.',
          tuned: 'Detail lebih banyak akan dibagikan di twitter resmi kami, nantikanlah!',
          thx: 'Terima kasih!',
          follow: 'Ikuti Kami Sekarang',
        },
      },
    },
    getGas: {
      recipient: 'Alamat Penerima',
      placeholder: 'Harap masukkan alamat Bitlayer',
      invalid: 'Alamat tidak valid',
      purchase: 'Pembelian',
      time: 'Perkiraan waktu: Sekitar 1 menit setelah pembayaran selesai',
      getGas: 'Get Bitlayer Gas',
      getRegular: 'Dapatkan Gas Reguler',
      after: 'Setelah mentransfer, Anda akan dapat melihat pesanan',
      history: 'Riwayat',
      payment: 'Alamat Pembayaran',
      timeout: 'Batas Waktu Kode QR',
      warning:
        'Jangan mentransfer ke alamat yang sudah habis masa berlakunya, karena Anda berisiko kehilangan token',
      amount: 'Jumlah Pembayaran:',
      cancelWarning:
        'Jika pesanan dibatalkan, mohon jangan mentransfer token apa pun ke alamat pembayaran, karena dapat mengakibatkan hilangnya dana Anda.',
      ok: 'OKE',
      cancel: 'Batal Pesanan',
      Complete: 'Selesai',
      getBitlayerGas:
        'Anda bisa mendapatkan Bitlayer Gas dengan mentransfer token ke alamat pembayaran dari mana saja wallet/pertukaran yang didukung.',
      promotionNote1: 'Promosi: hanya',
      fee: '$1',
      promotionNote2: 'untuk mendapatkan biaya Dapatkan Gas Express, sekarang!',
      addressNotion:
        'Alamat pembayaran untuk setiap perintah hanya dapat digunakan satu kali, jangan transfer dana lebih dari satu kali.',
      promotion: 'Biaya Promosi:',
      estimated: 'Perkiraan waktu: 10-30 menit',
      ensure: 'Harap pastikan berikut ini',
      content: `Konfirmasikan <strong>Blockchain, Token, dan Jumlah</strong> Secara Akurat. Setiap Alamat
                Pembayaran Hanya Dapat <strong>Digunakan Sekali.</strong>
                <div>Kegagalan Melakukannya Dapat Mengakibatkan Hilangnya Dana Deposit Anda.</div>`,
      aboutFlash: 'Tentang Flash Bridge-In',
      aboutInfo: `a. Semua dana dari Flash Bridge-In ditransfer melalui bridge resmi Bitlayer.<br>b. Dirancang agar pengguna dapat mengakses Bitlayer dengan lebih cepat dan lebih hemat biaya.`,
      get: 'Dapatkan',
      right: 'Lakukan dengan benar!',
      otherwise: 'Sebaliknya,',
      lose: 'you may lose your assets',
      Blockchain: 'Blockchain',
      Amount: ' Jumlah',
      token: 'Token',
      pay: '1 alamat untuk 1 bayar',
      kind: 'Tip bermanfaat',
      hasBridge:
        'Anda akan mengakses tautan pihak ketiga bukan milik Bitlayer. Aktivitas di tautan pihak ketiga ini tunduk pada Kebijakan Privasi dan Perjanjian Pengguna tautan pihak ketiga, dan pihak ketiga bertanggung jawab penuh atas Anda. Harap pahami situasi dan risiko yang berhubungan dengannya.',
      notBridge:
        'Ini adalah tautan eksklusif untuk event PEPE. Kunjungi untuk mengklaim hadiah setelah menyelesaikan transaksi bridge.',
      commonRisk: 'Risiko atau penipuan umum',
      phishing: 'Situs phishing',
      authorization: 'Risiko otorisasi',
      highYield: 'Penipuan hasil tinggi',
      ponzischeme: 'Skema Ponzi',
      freeAirdrop: 'Airdrop "gratis"',
      contractLoophole: 'Celah kontrak',
      aware: 'Saya memahami risikonya',
    },
    leaderBoard: {
      title: 'Papan Peringkat Bitlayer kompetisi Epoch 1',
      support: 'Dukungan Pendaftaran',
      incentives: 'Insentif',
      liquidity: 'BANTUAN LIKUIDITAS',
      forPartners: 'Untuk Mitra Eco',
      rules: 'Kenali Peraturan Kami?',
      topTvl: 'TVL Teratas',
      topTransation24: 'Transaksi 24j Teratas',
      topTransaction: 'Transaksi Teratas',
      topPopularity: 'Populer Atas',
      viewAll: 'Lihat Semua',
      tryNow: 'Try Now',
      countdown: 'Hitung Mundur Epoch',
      countEnded: 'Epoch berakhir',
      stage3: 'Babak 3 Epoch Kompetisi',
      stage2: 'Babak 2 Babak Persiapan',
      gemsMinted: 'Total Gem Di-mint',
      locked: 'Total Nilai Dikunci',
      transactionCount: 'Hitungan Transaksi',
      totalLikes: 'Total Suka',
      tvlRanking: 'Daftar Peringkat TVL di Ekosistem Mainnet Bitlayer',
      discoverTvl: 'Temukan DApps Dibangun di Mainnet Bitlayer dengan TVL Teratas',
      txnRanking: 'Daftar Peringkat TXN di Ekosistem Mainnet Bitlayer',
      discoverTransactions: 'Temukan DApps Dibangun di Mainnet Bitlayer dengan Transaksi Terbanyak',
      rankingList: 'Daftar Peringkat Popularitas di Ekosistem Mainnet Bitlayer',
      discoverPopular: 'Temukan DApps Paling Populer Dibangun di Mainnet Bitlayer',
      top10: 'Papan Peringkat 10 Teratas',
      tvl: 'TVL',
      transactions: 'Transaksi',
      likes: 'Suka',
      valueUpdate:
        'Nilai total aset efektif yang dikunci di protokol DApp di Mainnet Bitlayer, pembaruan harian.',
      numberUpdate:
        'Jumlah transaksi efektif yang dibuat oleh pengguna yang memanggil kontrak Dapp, pembaruan harian.',
      likesUpdate: 'Akumulasi suka dari proyek selama periode kompetisi, pembaruan real-time.',
      name: 'Nama',
      category: 'Kategori',
      gemMinted: 'Gem Di-mint',
      gemsPoints:
        'Gem dihitung berdasarkan Total Nilai Dikunci (TVL), jumlah transaksi efektif, jumlah pengguna aktif, performa papan peringkat popularitas Bitlayer, dan kekuatan produk. Data akan diperbarui pada 00:00 (UTC) setiap hari',
      boost: 'Dorong',
      bindX: 'Tautkan X (Twitter) Anda',
      bindNeed: 'Anda harus menautkan akun X (Twitter) terlebih dahulu!',
      start: 'Mulai 23 Mei',
      airdrop: 'Airdrop Sedang Berlangsung!',
      reward: 'Hadiah',
      rank: 'Peringkat DApp',
      dappCenter: 'Pusat DApp',
      more: 'Selengkapnya',
      introduce: 'Perkenalan Aktivitas',
      gems: '100% Gem',
      distributed: 'Akan Didistribusikan Ke Pengguna!',
      rewards: 'Hadiah',
      completed: 'Selesai',
      winnerList: 'Daftar pemenang',
      slots: 'pemenang',
      win: 'Complete to Win',
      startAt: 'Mulai pada',
      eventCountdown: 'Event countdown',
      ended: 'Event berakhir',
      verify: 'verifikasi',
      verified: 'terverifikasi',
      tutorial: 'Tutorial Cara',
      participated: 'Berpartisipasi',
      tasks: 'Tugas',
      congratulation: 'Selamat!',
      foru: 'Poin Bonus Bitlayer buat Anda!',
      export: 'Jelajahi dApps Bitlayer dan dapatkan hadiah lainnya!',
      phase: 'musim {{num}}',
      'Free Drop': 'Drop Gratis',
      '$1 Sweeptake': 'Undian $1',
    },
    dappDetail: {
      reward: 'Hadiah:',
      activity: 'Aktivitas',
      join: 'Bergabung sekarang !',
      introduce: 'Perkenalan Aktivitas',
      whatIs: 'Apa itu {{name}} ?',
      overView: 'Ikhtisar',
      dappIntro: 'Memperkenalkan DApp Kami!',
      event: `Berikut ini cara Anda dapat bergabung dengan Event Airdrop:`,
      team: 'Tim',
      twitterText:
        'Bergabunglah bersama saya dan coba {{name}} di @BitlayerLabs sekarang! Dapatkan hadiah dari 100+ dapps ekosistem #Bitlayer!',
      shareTitle: 'Bagikan dengan teman',
      shareText:
        'Bagikan & Coba proyek favorit Anda bersama teman-teman sekarang! Jadilah yang pertama untuk meraih hadiah potensial!',
    },
    miningGala: {
      meta: {
        title: 'Menangkan hadiah Bitlayer & airdrop dapp',
      },
      head: {
        time: '27 Mei 2024 13PM UTC - 10 Juni 2024 13PM UTC',
        doubleGain: 'Perolehan Ganda',
      },
      miningPolls: {
        title: 'Kolam Mining',
        claim: 'Klaim',
        minted: 'Di-mint',
        taskTitle: 'Selesaikan Tugas untuk Membuat Lencana Perintis',
        taskTip: 'Lengkapi setidaknya satu transaksi dengan {{project}}',
        shareText: 'Bagikan dengan teman-teman Anda',
        or: 'Atau',
        hasHelmet: 'Pegang Helm Keberuntungan',
        auditReport: 'laporan audit',
        miningGuide: 'panduan mining',
        airdrop: 'Airdrop',
        detailRules: 'Peraturan Detail',
        claimSuccess: 'Klaim Berhasil!',
        claimFailed: 'Klaim gagal, Harap coba lagi nanti.',
        bitlayerTask: 'Partisipasi dalam Gala Mining Bitlayer',
        lorenze: {
          desc: 'Platform premier untuk penerbitan, perdagangan, dan pembayaran token restaking Bitcoin melalui Babylon.',
          tasks: [
            'Stake BTC ke Babylon Pre-launch Staking dan dapatkan stBTC.',
            'Jembatani stBTC yang diterima ke Mainnet Bitlayer.',
            'Partisipasi dalam ekosistem DeFi Bitlayer dengan stBTC Anda.',
          ],
        },
        bitsmiley: {
          desc: 'Protokol stablecoin BTC-Native, Awalnya didanai oleh OKX Ventures & ABCDELabs.',
          tasks: [
            'Mint bitUSD di bitsmiley.',
            'Tambahkan likuiditas ke bitUSD-USDT/bitUSD-WBTC di bitCow.',
            'Stake bitUSD di protokol Defi.',
          ],
        },
        avalon: {
          desc: 'Avalon Finance berusaha menjadi protokol pinjaman terdesentralisasi di BTC layer 2.',
          tasks: [
            'Suplai aset di Bitlayer, dapatkan setidaknya 1000 poin suplai.',
            'Pinjam aset di Bitlayer, dapatkan setidaknya 500 poin pinjam.',
            'Ulangi suplai dan peminjaman Anda di Avalon Finance.',
          ],
        },
        bitcow: {
          desc: 'Stabil BTC-Native dan AMM likuiditas terkonsentrasi.',
          tasks: [
            'Tambahkan likuiditas ke pair trading bitUSD-USDT/bitUSD-WBTC.',
            'Partisipasi dalam trading bitUSD-USDT atau pair lain.',
            'Buat pair trading baru dan tingkatkan volume trading.',
          ],
        },
        pell: {
          desc: 'Pell adalah marketplace trust terdesentralisasi yang mendiversifikasi hasil BTC dan LSD, yang bertujuan untuk meningkatkan keamanan untuk jaringan BTC L2.',
          tasks: [
            'Hubungkan wallet Anda untuk masuk.',
            'Stake lebih dari 0,001 BTC dan terus kunci selama 7 hari.',
            'Undian keberuntungan: 1,5x kartu poin permanen x 1000, 100 USDT x 10.',
          ],
        },
        enzo: {
          desc: 'Enzo Finance adalah protokol peminjaman desentralisasi terbaik di rantai BitLayer dengan algoritma mutakhirnya.',
          tasks: [
            'Deposit/Stake aset Bitlayer Anda di Enzo Finance.',
            'Pinjam aset Bitlayer di Enzo Finance.',
            'Giveaway harian 1 BTC (Persyaratan: total deposit/pinjaman > $100 USDT).',
          ],
        },
        bitparty: {
          desc: 'BitParty adalah “jaringan komunitas aset yang digamifikasi” pertama di ekosistem BTC!',
          tasks: [
            'Dapatkan aset Bitlayer melalui jembatan cross-chain.',
            'Stake aset Bitlayer Anda di Bitparty dan dapatkan poin untuk memperoleh $BTPX.',
            'Bergabung dengan grup dan dapatkan teritori dengan orang lain!',
          ],
        },
      },
      shareDialog: {
        title: 'Bagikan dengan teman',
        desc: 'Bagikan dengan teman-teman Anda, berpartisipasilah dalam Gala Mining Bitlayer, dan kumpulkan Lencana Perintis Gala Mining Bitlayer Eksklusif Sekarang!',
      },
      tipDialog: {
        title: 'Tip Bermanfaat',
        desc: 'Saldo BTC tidak cukup, Anda dapat mengisi ulang BTC dengan cara berikut.',
        depositBtn: 'Deposit kilat',
        bridgeBtn: 'Bridge',
      },
      twitterShare: {
        projectShare: `Bergabunglah bersama saya, berpartisipasi dalam kolam mining {{name}} di @BitlayerLabs #MiningGala sekarang! {{money}} airdrop menunggu untuk Anda bagikan!\n`,
        badgeShare:
          'Saya baru saja melakukan mint Lencana Perintis #MiningGala @BitlayerLabs saya di sini! Masuki halaman untuk melakukan mint gratis lencana Anda! Bergabunglah bersama saya untuk membagikan $24.350.000 airdrop!',
      },
      bottomTip: {
        title: 'Tutorial Bermanfaat untuk Gala Mining Bitlayer!',
        desc: 'Selamat datang di Gala Mining Bitlayer! Periksa panduan kami di bawah ini dan nikmati perjalanan mining Anda di sini!',
        tips: [
          'Dapatkan Gas Bitlayer dalam 1 menit.',
          'Tutorial untuk melakukan bridge aset ke Bitlayer.',
          'Cara terbaik untuk bergabung dengan Gala Mining Bitlayer.',
          'Panduan mint Perintis Gala Mining Bitlayer.',
        ],
      },
    },
    userCenter: {
      title: 'RACER CENTER',
      dailyTasks: 'TUGAS HARIAN',
      bitlayerPoints: 'POIN BITLAYER',
      bitlayerGems: 'GEM BITLAYER',
      tasks: 'Tasks',
      badges: 'Badges',
      bitlayerDays: '<span>{{ days }}</span> HARI DI BITLAYER',
      txn: 'TXN',
      bridged: 'DI-Bridge',
      unlocked: 'Lv{{ level }} unlocked',
      locked: 'Lv{{ level }} locked',
      unlocking: 'Lv{{ level }} unlocking <span>{{ progress }}%</span>',
      tabs: {
        newRacerTasks: 'Tugas Pembalap Baru',
        advancedTasks: 'Tugas Lanjutan',
        ecoTasks: 'Tugas Ekosistem',
        myBadges: 'Lencana Saya',
      },
      task: {
        complete: 'SELESAIKAN',
        claim: 'KLAIM',
        pointsAcquired: '<span>{{ points }}</span> Poin Bitlayer Diperoleh.',
        check: 'Periksa',
      },
      claimGems: {
        title: 'Selamat! Cepat klaim gem.',
        action: 'Buka untuk klaim',
      },
      badge: {
        coming: 'SEGERA HADIR',
        inProgress: 'DALAM PROSES',
        finished: 'SELESAI',
        claim: 'KLAIM POIN',
        join: 'GABUNG EVENT',
        owned: 'LENCANA YANG DIMILIKI',
        notOwned: 'LENCANA TIDAK DIMILIKI',
        rewardCanClaim: 'dapat diklaim',
        rewardClaimed: 'diklaim',
      },
      reward: {
        points: 'Poin',
        gems: 'Gem',
        btr: 'BTR',
      },
      errors: {
        twitter: {
          accountBinded:
            'Akun Twitter ini telah diikatkan ke alamat lain. Harap gunakan akun Twitter yang lain.',
        },
        unexpectedError: 'Terjadi kesalahan yang tak terduga. Silakan coba lagi nanti.',
      },
      rankTitle: 'Peringkat Poin SAYA',
      topRacers: 'Pembalap Teratas',
      racer: 'Pembalap',
      gains: 'Keuntungan',
      invite: {
        discover: 'Temukan bitlayer',
        and: 'dan dapatkan',
        invite: 'Undang teman',
        refer: 'RUJUK TEMAN',
        shareText:
          'Bagikan kode/tautan rujukan ke teman Anda saat mereka mendaftar di bitlayer.org dan mengirim token di Bitlayer lebih dari satu kali, lalu Anda bisa mendapatkan hadiah. (Diperbarui setiap 12 jam)',
        my: 'RUJUKAN SAYA',
      },
      twitter: {
        bindTitle: 'Tautkan X (Twitter) Anda',
        bindDesc: 'Anda harus menautkan akun X (Twitter) terlebih dahulu!',
        bindAction: 'Otorisasi di X',
        tips: 'Perhatian: Satu akun Twitter hanya dapat ditautkan ke satu alamat untuk menghindari kesalahan.',
      },
      draw: {
        history: {
          history: 'Riwayat Undian',
          time: ' Waktu Undian',
          reward: 'Hadiah',
        },
        error: 'Anda ada 0 peluang undian, undang teman untuk mendapatkan lebih banyak',
        up: 'Hadiah hingga $10.000',
        unit: 'Undian',
        invite:
          'Undang untuk mendapat lebih banyak peluang dan tingkatkan level keberuntungan Anda!!',
        card: 'Undian Kartu',
        wait: 'Menunggu Konfirmasi On-Chain, ~30 dtk',
        warning: 'Peringatan',
        sorry: 'Maaf! Minimal 1000 Poin Per Undian, Poin Anda Tidak Cukup.',
        ok: 'OKE',
      },
    },
    activities: {
      guessingGame: {
        title: 'Tebak untuk menghasilkan',
        subtitle: 'Pakar prediksi harga',
        olympics2024: {
          title: 'Tebak untuk menghasilkan',
          subtitle: 'Olimpiade Paris 2024',
        },
        status: {
          inProgress: 'Sedang berlangsung',
          coming: 'Segera hadir',
          waiting: 'Menunggu',
          finished: 'Selesai',
          releasing: 'Hasil dirilis dalam 24 jam',
          endIn: 'Berakhir dalam',
        },
        placeBet: 'PASANG TARUHAN ANDA',
        bet: 'Taruhan',
        betUnit: '{{ step }} {{ rewardType }} per unit',
        betFor: '<icon /> <span>{{ amount }}</span> untuk {{ text }}',
        wonRewardFor: 'Menangkan <icon /> <span>{{ amount }}</span> untuk {{ text }}',
        myBets: 'TARUHAN SAYA',
        won: 'Menang',
        return: 'Pengembalian',
        pool: 'Kolam',
        reward: {
          claim: 'KLAIM',
          claimed: 'DIKLAIM',
          claimAll: 'Klaim Semua Hadiah',
          congratulations: 'Selamat!',
          claimedAllPoints:
            'Anda menghasilkan <point /> <span>{{ points }}</span> dari pusat pembalap.',
          claimedAllGems: 'Anda menghasilkan <gem /> <span>{{ gems }}</span> dari pusat pembalap.',
          claimedAllBoth:
            'Anda menghasilkan <point /> <span>{{ points }}</span> dan <gem /> <span>{{ gems }}</span> dari pusat pembalap.',
        },
        messages: {
          placedBet: 'Anda berhasil memasang taruhan. Semoga beruntung!',
          failedToPlaceBet: 'Gagal memasang taruhan. Silakan coba lagi nanti.',
          claimedReward: 'Anda berhasil mengklaim hadiah Anda!',
          failedToClaim: 'Gagal mengkliam hadiah. Silakan coba lagi nanti.',
          insufficient: 'Anda butuh minimal {{ amount }} {{ rewardType }} untuk memasang taruhan.',
          alreadyClaimedAll: 'Anda telah mengklaim semua hadiah.',
          failedToClaimAll: 'Gagal mengkliam semua hadiah. Silakan coba lagi nanti.',
        },
      },
    },
    whiteList: {
      reward: 'Hadiah Saya',
      address: 'Alamat',
      xp: 'Poin Bitlayer',
      lucky: 'Hadiah Hoki',
      points: 'poin',
      winnerList: 'Daftar Pemenang',
      description: `Notes: Users completing and verifying all tasks will be rewarded with Bitlayer Points within 7 days after the events ends. Project rewards will be distributed according to the project's official announcements. `,
    },
    opvm: {
      title: {
        slogon1: 'OpVM',
        slogon2: 'hasilkan Bitcoin',
        slogon3: 'diverifikasi',
        description: 'Layer Verifikasi Bitcoin Pertama',
        whitePaper: 'Laporan Resmi',
        earlyAccess: 'Akses Dini',
        comingSoon: 'Segera Hadir',
      },
      feature: {
        badge: 'Fitur',
        title1: 'Keamanan Bitcoin Bawaan',
        description1: 'Dengan Biaya Rekayasa Minimal',
        title2: 'Modular & Dapat Disusun',
        description2: 'Layanan Berbasis API, Pasang Dan Gunakan',
        title3: 'Arsitektur bukti masa depan',
        description3: 'Bukti Penipuan + Bukti Validitas',
        title4: 'Dapat disesuaikan & ditingkatkan',
        description4: 'Open Source & Dapat Diaudit',
      },
      advantages: {
        badge: 'Keunggulan',
        num1: 'LINIMASA PENGEMBANG CEPAT',
        num2: 'Biaya gas rendah dengan agregasi',
        num3: 'Keamanan Level 1 sebagai bitcoin',
      },
      usecase: {
        badge: 'Kasus penggunaan',
        case1: 'Layer 2 Bitcoin',
        case2: 'Membungkus BTC',
        case3: 'Bridge Bitcoin',
        case4: 'Protokol Staking Bitcoin',
        start: 'mulailah sekarang !',
        early: 'Akses Dini',
      },
    },
  },
};
