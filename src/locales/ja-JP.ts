export default {
  title: 'Bitlayer',
  meta: {
    description: 'BitVMを使った初のビットコインセキュリティ同等のレイヤー２',
  },
  common: {
    readMore: 'もっと読む',
    learnMore: '詳細を学ぶ',
    upload: 'upload',
    submit: '提出する',
    return: '戻る',
    send: '送信',
    back: '戻る',
    coming: '近日公開',
    connect: 'ウォレット接続',
    disconnect: '接続を解除する',
    switchChain: '{{chainName}} に切り替える',
    insufficientBalance: '残高不足',
    search: '検索',
    toTop: 'Return to Top',
    getGas: 'Bitlayer Gasを入手',
    bridge: 'ブリッジ＆アーン',
    connectDesc: 'トークンをブリッジするにはウォレットを接続してください',
    MesonDes: '無料で安全にすぐ使えるステーブルコインのためのクロスチェーン',
    OwltoDes: 'Owlto Financeはインテント セントリックな相互運用性プロトコルです',
    OrbiterDes: 'Orbiter Financeは分散型のクロスロールアップブリッジです',
    BoolBridgeDes: 'ブールブリッジは、ビットコインとレイヤー2を接続する分散型ブリッジです。',
    OmnityBridgeDes:
      'Omnityは、完全にオンチェーンで、Bitcoinトークン保有者にとって使いやすいブリッジです。',
    login: 'ログイン',
    noData: 'データなし',
    confirm: '確認',
    Ok: 'OK',
    cancel: 'キャンセル',
    reject: '却下',
    website: 'ウェブサイト',
    share: 'シェアする',
    more: 'もっと見る',
    less: '少なく表示',
    flash: 'Flash Bridge-In',
    getIt: 'ゲットしよう',
    congratulations: 'おめでとうございます!',
    error: {
      gasLess: 'この取引に必要なガス残高が不足しています',
      transactionFailed: 'ブロックチェーン取引に失敗しました',
    },
  },
  resources: {
    github: 'https://github.com/bitlayer-org',
    discord: 'https://discord.gg/bitLayer',
    x: 'https://twitter.com/BitLayerLabs',
    linkedin: 'https://bit.ly/42B6v15',
    telegram: 'https://t.me/bitlayerofficial',
    medium: 'https://medium.com/@Bitlayer',
    rankRule:
      'https://medium.com/@Bitlayer/bitlayer-provides-20m-airdrop-for-dapp-leaderboard-competition-rewarding-ecos-projects-and-87ed3dc76b94',
  },
  navigation: {
    links: {
      opvm: 'OpVM',
      developers: '開発者',
      users: 'ユーザー',
      userCenter: 'レーサーセンター',
      resources: 'リソース',
      contact: 'お問い合わせ',
      mainnet: 'メインネット',
      bridgeEarn: 'ブリッジ＆アーン',
      usdcChange: 'USDC 変更',
      gas: 'ガスを取得',
      startBuilding: 'ビルドを開始する',
      btrScan: 'Bitlayer(BTR) スキャン',
      addMainnet: 'Bitlayerメインネットを追加',
      testnet: 'テストネット',
      faucet: 'フォーセット',
      testnetBridge: 'テストネットブリッジ',
      testnetBridgeHint: 'Bitlayerテストネットブリッジ',
      testnetScan: 'テストネットスキャン',
      addTestnet: 'Bitlayerテストネットを追加',
      language: '言語',
      getGas: 'Bitlayer Gas を入手',
      readyPlayerOne: '準備完了プレイヤー 1',
      luckyHelmet: 'ラッキーヘルメット',
      dAppsLeaderboard: 'DAppsリーダーボード',
      hint1: '$50,000,000 エアドロップ',
      hint2: 'ドライバーになる。Bitlayerから収益を得る',
      hint3: 'お気に入りのプロジェクトに投票',
      brandKit: 'ブランドキット',
      whitepaper: 'ホワイトペーパー2.0',
      leaderboard: 'リーダーボード',
      dappCenter: 'DAppセンター',
      dappCenterHint: 'Bitlayer DAppランキングリスト',
      miningGala: '採掘ガラ.',
      bridge: 'ブリッジ＆アーン',
      flash: 'Flash Bridge-In',
      developersSection: {
        hint: 'Bitlayerチェーンの構築を開始',
        documentation: 'ドキュメンテーション',
        documentationHint: '開発者向けドキュメントをサポートしています',
        tools: {
          title: 'ブーストツール',
          hint: 'プロジェクトの開発ツール',
          mainnetScan: 'メインネット スキャン',
          mainnetScanHint: 'Bitlayer (BTR) メインネットスキャン',
          testnetScan: 'テストネット スキャン',
          testnetScanHint: 'Bitlayer (BTR) テストネットスキャン',
          faucet: 'フォーセット',
          faucetHint: 'Bitlayerチェーン上のパイロットトークン',
          theGraph: 'グラフ',
          theGraphHint: 'Bitlayerでデータを索引付け、検索',
          multisigWallet: 'マルチシグウォレット',
          multisigWalletHint: '秘密鍵を安全かつ簡単に保管します',
        },
        security: {
          title: 'セキュリティ',
          hint: 'セキュリティサポート',
          dappSecurityManual: 'Dappセキュリティマニュアル',
          dappSecurityManualHint: 'セキュリティDappを構築するチュートリアル',
          auditAlliance: '監査アライアンス',
          auditAllianceHint: '監査アライアンス',
          securityNetwork: 'セキュリティネットワーク',
          securityNetworkHint: 'セキュリティネットワーク',
        },
        developerSupport: {
          title: '操作サポート',
          hint: '助成金、資金調達、インセンティブ、サポート',
          readyPlayerOne: 'Ready player I',
          readyPlayerOneHint: 'トレードで賞金総額最高10万ドルを獲得',
          luckyHelmet: 'ラッキーヘルメット',
          luckyHelmetHint: '早期ビルダーのために$ 50,000,000を解放する',
          miningGala: '採掘ガラ',
          miningGalaHint: 'BitlayerリワードとDapp Adropを獲得しよう',
          leaderboard: 'リーダーボード',
          leaderboardHint: 'BitLayerリーダーボードコンペティションエポック1',
        },
        hackathon: 'ハッカソン',
        hackathonHint: '開発者向けドキュメントをサポートしています',
        github: 'GitHub',
        githubHint: 'BitLayer技術リポジトリを探索',
        devCommunities: '開発者コミュニティ',
        devCommunitiesHint: '開発者のチャットに参加する',
        telegram: 'Telegram',
        opvm: 'OpVM',
        opvmHint: 'ビットコインを検証可能にする',
      },
    },
    footer: {
      Participate: '参加する',
      ReadyPlayerOne: '準備完了プレイヤー 1',
      Build: 'ビルド',
      BuildingBitlayer: 'Bitlayer上でビルドする',
      Faucet: 'フォーセット',
      SupportedWallets: 'サポートされているウォレット',
      TheGraph: 'グラフ',
      MainScan: 'Bitlayer(BTR)スキャン',
      TestScan: 'テストネットスキャン',
      About: '概要',
      Solutions: 'ソリューション',
      Roadmap: 'ロードマップ',
      EventNews: 'イベント＆ニュース',
      Jobs: '求人 (仕事）',
      Community: 'コミュニティ',
      blog: 'ブログ',
      Podcast: 'ポッドキャスト',
      Operation: '操作',
    },
    walletDrawer: {
      wrongNet: '{{chainName}}に切り替えてください',
      noNFT: 'コレクションがありません',
    },
  },
  pages: {
    home: {
      title: {
        notice:
          'Bitlayer Ready Player I が現在利用可能です！ポイントとホワイトリストに投票しましょう！',
        slogon1: '拡張する',
        slogon2: '可能性',
        slogon3: 'ビットコインの',
        startBuilding: 'ビルドを開始',
        bridgeEarn: 'ブリッジ＆アーン',
        bridge: 'ブリッジ',
        competition: 'Bitlayer DAppセンター',
        dapp100:
          "<span class='bl-text-xs md:bl-text-2xl bl-text-primary bl-font-[600]'>100+</span> ダップで報酬を獲得しましょう！",
        gasFee: 'ガス料金の中央値：',
        whitePaper: 'Bitlayer White Paper',
        leaderboard:
          "<span class='bl-text-xs md:bl-text-2xl bl-text-white bl-font-[600] bl-px-1'> Gemsエアドロップ </span> の資格を確認しましょう！",
      },
      spotlight: {
        spotlight: 'スポットライト',
        readMore: 'もっと読む',
        carousel: {
          item1: {
            type: 'ブログ',
            title: 'OpVM',
            content: '将来を見据えたビットコイン検証レイヤー',
          },
          item2: {
            type: 'オフライン イベント',
            title: 'ビットコインバー',
            content:
              'BitlayerとPizza Ninjasが主催するビットコインバーで、刺激的な暗号通貨の世界に飛び込む準備をしよう！ ',
          },
          item3: {
            type: 'オフライン イベント',
            title: 'ビットコイン次のVIPディナー',
            content: 'KBWウィークにBitlayerが主催する特別なディナーパーティー、登録へようこそ！',
          },
          item4: {
            type: 'オフライン イベント',
            title: 'Bitlayerフォーミュラナイト',
            content: 'Bitlayerはトークン2049ウィークにシンガポールでBitlayerナイトを開催します！',
          },
          item5: {
            type: 'エアドロップイベント',
            title: 'Bitlayer DAppセンター',
            content:
              'BitLayer DAppセンターの立ち上げを記念して、最大100,000,000のBitlayerポイント専用エアドロップをお楽しみください。 ',
          },
          item6: {
            type: 'エアドロップイベント',
            title: 'Bitlayerレーサーセンター',
            content:
              'Bitlayerレーサーセンターが稼働しました！タスクやミニゲームを探索して、Bitlayerの宝石やポイントを獲得しよう！',
          },
        },
      },
      liveData: {
        liveData: 'ライブ データ',
        explore: '探索する',
        'the community': 'コミュニティー',
        'of bitlayer': 'bitlayerの',
      },
      solutions: {
        solutions: 'ソリューション',
        title: 'ビットコインのレイヤ−２の未来は何であるべきか',
        subtitle: 'ビットコインエコシステムの未来のための主要なソリューション',
        items: [
          {
            title: 'レイヤー1の検証',
            text: 'BitVMを介したビットコインのセキュリティの継承',
          },
          {
            title: 'トラストレスな 2-Way ペッグ',
            text: 'DLCとBitVMを組み合わせ、伝統的なマルチシグを超えた画期的なモデルをもたらす',
          },
          {
            title: 'チューリング完全',
            text: '複数のVMをサポートし、100%EVM互換の環境を可能にするソリューション',
          },
        ],
        whitepaper: {
          title: 'ホワイトペーパーをダウンロード',
          button: 'ホワイトペーパー',
        },
      },
      innovations: {
        innovations: 'イノベーション',
        items: [
          {
            title: 'ビットコインセキュリティ',
            subtitle: '- OpVM -',
            text: 'OpVMは、ブロックチェーンアプリケーションがビットコイン上でファイナライズするのを支援する、トラストレスのファイナリティサービスです。',
          },
          {
            title: 'トラストレスブリッジ',
            subtitle: '- ファイナリティブリッジ -',
            text: 'ファイナリティブリッジは、OpVMサービスによって提供される、トラストを最小限に抑えたフロント＆リインバースモードのビットコインブリッジです。',
            imageDesc:
              'アトミックスワップと組み合わせると、BTCはBitcoinとL2の間でトラストレスに動くことができ、古いマルチシグに取って代わることができます',
          },
          {
            title: 'リアルタイムEVM',
            subtitle: '- RtEVM -',
            text: '並列実行と高速ステートアクセスにより、RtEVMは超高スループットとほぼリアルタイムのトランザクション確認を実現します',
            imageDesc1:
              'スケジューラは、マルチスレッドをフル活用して、トランザクションを複数のEVMエクスクータに最適にディスパッチします',
            imageDesc2:
              'スケーラブルなMPTトライにシャーディングされたKVストアを接続し、無制限の容量と非常に高速な状態アクセスを可能にします',
          },
        ],
      },
      roadmap: {
        roadmap: 'ロードマップ',
        title: 'ビットコインの旅',
        title2: 'Bitlayerの冒険',
        points: [
          {
            title: 'カラー コイン',
            description:
              'リアルワールドアセットを安全かつ透明性をもって管理するために、Colored Coinsはブロックチェーントークンにタグ付けします。',
            date: '2012',
          },
          {
            title: 'カウンターパーティー',
            description:
              'counterparty は、カスタムトークンとスマートコントラクトをビットコインに追加し、デジタルアセットのビルドと取引を行います。',
            date: '2014',
          },
          {
            title: 'Spells of Genesis',
            description:
              'ブロックチェーンベースのモバイルゲームである spells of genesis は、ファンタジーの設定でトレーディングカードとアーケード要素を融合させます。',
            date: '2015',
          },
          {
            title: 'レアPEPE',
            description:
              'Pepe the Frog をフィーチャーしたデジタルトレーディングカードが作成され、ビットコインのブロックチェーン上で交換され、ユニークでコレクション可能な暗号アートを象徴します。',
            date: '2016',
          },
          {
            title: 'Segwit アップデート',
            description:
              'Segregated Witnessプロトコルのアップデートは、署名データをトランザクションデータから分離することにより、ブロック容量を増やし、スケーラビリティを向上させます。',
            date: '2017',
          },
          {
            title: 'Taproot アップデート',
            description:
              'Taproot ビットコインプロトコルの更新は、Schnorr署名と呼ばれる新しい署名方式を介してプライバシー、効率性、およびスマートコントラクト機能を強化します。',
            date: '2021',
          },
          {
            title: 'Ordinals',
            description:
              'Ordinalsプロトコルは、ビットコインブロックチェーン上の個々のサトシに直接ユニークなデジタルアーティファクトを刻印し、NFTのような機能を可能にします。',
            date: '2023年',
          },
        ],
        journeys: [
          {
            title: 'メインネットV1',
            texts: [
              '開発者とユーザーを登録するための最高のセキュリティモデル：PoSサイドチェーン + MPC-TSS/ネイティブマルチシグ',
            ],
            subtitle: '2024年4月',
          },
          {
            title: 'メインネットV2',
            texts: [
              'ロールアップに相当するモデルへの変換：アセットブリッジングのための画期的なセキュリティモデル、BitVMなしでL1検証をするという挑戦的なゲーム',
              'ファイナリティブリッジ',
            ],
            subtitle: '2024年9月',
          },
          {
            title: 'メインネットV3',
            texts: [
              '全体概要：BitVMスキームを備えたL1検証の挑戦的なゲーム、ビットコインに相当するセキュリティを実現',
              'リアルタイムEVM',
            ],
            subtitle: '2025年6月',
          },
        ],
      },
      events: {
        tag: 'イベント&ニュース',
        title: 'Bitlayerエコシステムのイベント＆ニュース',
        subtitle: '私たちの最新情報をフォローしてください',
        items: [
          'ビットコイン2024ナッシュビル',
          'トークン2049',
          'Bitlayerナイト韓国',
          'ビットコイン シンガポール',
        ],
      },
      jobs: {
        tag: '求人',
        title: 'BitLayerの求人募集',
        subtitle: '一緒にビットコインの歴史を作りましょう！',
        positions: [
          {
            title: 'デベロッパーリレーションズの責任者_',
            skills: [
              'パブリックブロックチェーンエコシステム内での開発者との関係の構築を担当します。',
              'さまざまなグローバル開発者のテクニカルコミュニティやエコシステム内において、技術的なコミュニケーションと開発者育成を実施します。',
            ],
          },
          {
            title: 'プロトコル研究者_',
            skills: [
              '業界の課題に基づいたブロックチェーンの最先端研究を行います。',
              '高品質の研究レポートや論文を執筆します。',
            ],
          },
        ],
        more: 'オープンポジションを探す',
      },
      community: {
        tag: 'コミュニティ',
        title: '未来のコミュニティに参加する',
        subtitle: 'Bitlayerとの積極的な関わりを保つ',
        items: [
          {
            title: 'Bitlayerに貢献する',
            description: '私たちと一緒にビルドする',
          },
          {
            title: 'Discordに参加する',
            description: '私たちと話をしましょう',
          },
          {
            title: 'xをフォローする',
            description: '私たちと関わりを持ちましょう',
          },
          {
            title: 'LinkedInでつながる',
            description: '私たちとつながる',
          },
          {
            title: 'Telegramに参加する',
            description: '私たちとチャットする',
          },
          {
            title: 'Mediumで最新情報をチェックする',
            description: '私たちと一緒に学びましょう',
          },
        ],
      },
      investors: {
        investors: '投資家の皆さん',
        'Co-lead': 'COリード',
      },
      ecosystem: {
        ecosystem: 'エコシステム',
        top: 'トップ',
        Dapps: 'Dapps',
        on: 'オン',
        Bitlayer: 'Bitlayer',
        'Dapp Center': 'Dappセンター',
        lending: '貸付',
        dex: 'DEX',
        staking: 'ステーキング',
        gaming: 'ゲーム',
        'stable coins': 'ステーブルコイン',
      },
    },
    developers: {
      title: {
        titleLine1: 'へようこそ',
        titleLine2: 'Bitlayerドキュメント',
        subtitle: 'ビルダーによるビルダーのためのビットレイヤーエコシステムへの参加マニュアル',
        buildNow: 'Build Now',
        blog: 'ブログ',
      },
      faq: {
        title: 'よくある質問',
        name1: 'Bitlayerとは？',
        desc1:
          'Bitlayerは、ビットコインのレイヤー 2ソリューションとして機能し、BTCをネイティブトークン（ガストークン）として100％のEVMとイーサリアムツールチェーンの互換性を誇っています。Bitlayerは、既存のイーサリアムエコシステムからのアプリケーションと開発者が低コストでBitlayerに移行できるようにすることができ、大幅な修正や書き換えが不要になります。',
        name2: 'Bitlayerは既存のビットコインウォレットと互換性がありますか？',
        desc2:
          'はい、BitlayerはMetamask、Unisatなどの既存のウォレットと互換性があり、ユーザーがBitlayerネットワーク上の資金や資産とシームレスにやり取りできるようにします。',
        name3: '開発者は既存のプロジェクトをBitlayerに移行できますか？',
        desc3:
          'はい、Bitlayerは既存の開発者をサポートし、EVM互換性を提供することで、既存のプロジェクトを低コストでシームレスに移行およびデプロイできるようにします。開発者は、Solidity、Vyperなどで書かれたスマートコントラクトをEVMバイトコードにコンパイルする任意の言語を、Bitlayerに直接使用できます。お使いのツールチェーン：Ethereum JSON-RPC、Hardhatなど。',
        name4: 'Bitlayerをサポートするにはどうすればよいですか？',
        desc4: `Bitlayerをサポートする方法はいくつかあります。コミュニティディスカッションに積極的に参加したり、フィードバックや提案を提供したり、プラットフォーム上のアプリケーションやツールの開発に貢献したり、Bitlayerを利用してくれる可能性のある人にBitlayerを紹介したりすることができます。さらに、Bitlayerが設けている特定の支援イニシアチブやプログラムを探すこともできます。<span data-link-index='0' class='bl-underline bl-cursor-pointer'>Ready Player One</span> について詳しく知るには、ここをクリックしてください。このプログラムは、早期のビルダーやコントレビューター向けに5,000万ドルのインセンティブをアンロックします。`,
      },
      feature: {
        title: '私たちの強み',
        title1: 'チューリング完全性',
        desc1: '複数のVMをサポートし、100%のEVM互換環境を可能にします',
        title2: 'トラストレスな 2-Way ペッグ',
        desc2: 'DLCとBitVMを組み合わせ、従来のマルチシグを超える画期的なモデルを提供します',
        title3: 'レイヤー1の検証',
        desc3: 'BitVMを介してビットコインのセキュリティを継承します',
      },
      quickstart: {
        title: '開発者クイックスタート',
        subtitle: 'ビルダーによるビルダーのためのガイドでBitlayerエコシステムをアンロックする',
        title1: 'ウォレットをBitlayerテストネットに接続する',
        skill1:
          'ウォレットにBitlayerテストネットの設定を追加し、Bitlayerテストネット上のDappsとやり取りします',
        title2: 'コンパイル、実行、デプロイ',
        skill2: 'このガイドでは、Bitlayerをコンパイルして実行し、デプロイする手順を説明します',
        title3: '新しい視点や最新の情報にアクセスする',
        skill3: 'テクノロジーとマーケットについてのインサイトを弊社のブログでお楽しみください',
        readMore: 'もっと読む',
      },
      intro: {
        title: 'トラックパック',
        subtitle:
          'Bitlayerエコシステムに参加するためのビルダーガイド：ビルダーによって、ビルダーのために作成されました。',
        buildNow: 'Build Now',
      },
      tools: {
        title: '開発者ツール',
        subtitle:
          'Bitlayerエコシステム内の関連ツールを活用して、あなたのポテンシャルを最大限に引き出してください',
        name1: 'フォーセット',
        desc1: 'こちらで開発用のBitlayerテストネットトークンを毎日24時間ごとに取得できます',
        name2: 'メインネット スキャン',
        desc2: `Bitlayer メインネット上のブロックチェーンデータを検索して分析するための重要なツールです。トランザクション、ブロック、アドレスを詳しく調べることができます`,
        name3: 'テストネット スキャン',
        desc3:
          'テストネット上のブロックチェーンデータを検索して分析するための重要なツールです。テストネットのトランザクション、ブロック、アドレスを詳しく調べることができます',
        name4: 'グラフ',
        desc4: 'Bitlayerでリアルタイムのブロックチェーンデータをインデックスしてアクセスします。',
        website: 'ウェブサイト',
        more: 'その他のツールを探す',
      },
      security: {
        title: 'セキュリティ',
        subtitle:
          'セキュリティスイートを使用して、安全で信頼性の高いアプリケーションを迅速に構築します',
        name1: 'Dappセキュリティマニュアル',
        desc1: `セキュリティマニュアルに従い、アプリケーションのセキュリティモジュールの開発効率を向上させます。`,
        buttonLabel1: '文書を読む',
        // name2: 'Audit alliance',
        name2: 'セキュリティネットワーク',
        desc2: `著名なセキュリティサービスプロバイダーとすばやく接続して、アプリケーションを保護し、その価値を高めます。`,
        buttonLabel2: '文書を読む',
        name3: 'オープンソースツール',
        desc3: 'オープンソースのセキュリティツールを使用して、迅速に自己検出を実行します。',
        buttonLabel3: '探索する',
      },
      operationalSupport: {
        title: '操作サポート',
        subtitle: '助成金、資金調達、インセンティブ、サポート',
        name1: 'Ready Player助成金',
        desc1:
          'Bitlayerの潜在性を解放する：BTCエコシステムを進めるために革新的なプロジェクトと優れたチームを探しています。',
        name2: 'インセンティブプログラム',
        desc2: `Ready Player I——早期のビルダーやコントレビューター向けに $50,000,000 のインセンティブ。`,
        name3: 'Op & MKT ソース',
        desc3:
          '公式リソースのサポートとグローバルマーケットリソースのサポート。リーダーボード、DAppセンター、レーサーセンター。',
        name4: 'エコ成長キャンペーン',
        desc4: 'マイニングガラ、Bitlayerの声、グローバル暗号会議。',
        participate: '参加する',
        more: 'その他のサポート',
      },
      cards: {
        name1: 'クイックスタート',
        desc1:
          'Bitlayerは、ビットコインと同等のセキュリティとチューリング完全性を提供するビットコイン初のレイヤー2ソリューションです。その上でdAppsを構築する方法をご紹介します。',
        name2: 'Bitlayerアーキテクチャ',
        desc2:
          'Bitlayerネットワークを始め、そのユニークな特徴を知りましょう。ネットワークのアーキテクチャと他のブロックチェーンとの違いについて学びましょう。',
        name3: 'Bitlayerロードマップ',
        desc3: `Bitlayerのビジョンは、メインネットを複数の段階に分けて展開し、各段階でユーザー体験を向上させることで実現さ れます。`,
        readDoc: '文書を読む',
      },
      connect: {
        title: '私たちとつながる',
        subtitle: 'Bitlayerコミュニティ内の最新ニュースやアップデートの最新情報をご確認ください',
      },
    },
    bridge: {
      // transfer
      bridgeTitle: 'ブリッジ＆アーン',
      gasTitle: 'Bitlayer Gas を入手',
      gasPromotion: 'クイックスタート - Bitlayerのガスを1分で手に入れよう！',
      newToBridge: 'ブリッジは初めてですか？',
      from: '開始日',
      to: '終了日',
      amount: '金額',
      balance: '残高：: {{balance}}',
      available: '残高：: {{balance}}',
      transferable: '譲渡可能なInscriptions',
      max: '最大',
      recipientAddress: '受取人のアドレス',
      recipientTip: 'トークンを受け取るにはウォレットを接続してください',
      est: '見積もり： {{time}}',
      fee: '手数料:',
      total: '合計:',
      receivepPlaceholder: '{{chainName}} のアドレスを入力してください',
      transfer: '送金',
      transferProgress: 'ブリッジが進行中です',
      approve: '承認',
      approveInProgress: '承認待ち',
      checkingAllowance: '許可を確認中',
      minLimit: '取引ごとの最小{{amount}} {{symbol}}',
      maxLimit: '取引ごとの最大{{amount}} {{symbol}}',
      invalidAmount: '無効な金額',
      invalidAddress: '無効なアドレス',
      switchChainDesc:
        '{{chainName}}ネットワークへの切り替えには {{networkType}} ウォレットの使用が必要です。',
      transferFailed: 'ブリッジに失敗しました',
      connectDesc: 'トークンをブリッジするにはウォレットを接続してください',
      bridgeTip:
        'Bitlayerへのブリッジ、Bitlayerラッキーヘルメット の保有、400K BWBポイントの共有！',
      historyTab: '履歴',
      getGas: 'ガスを取得',
      swap: 'スワップする',
      gasLimit: `残高≥ {{maxBalance}} はスワップできません`,
      dayLimit: `今日の残りのBTCは {{remainSwapAmount}}`,
      // history
      loadingData: 'データを読み込んでいます',
      noData: 'データなし',
      sender: '送信者',
      receiver: '受取人',
      transactionHash: 'トランザクションハッシュ',
      Pay: 'あなたが支払う',
      gasPay: '支払う',
      gasGet: '得る',
      Balance: '残高：',
      Max: '最大',
      SwitchToBitlayer: '切り替える Bitlayer',
      Maximum: '取引ごとの最大 {{maxSwapAmount}} BTC',
      History: '履歴',
      Receive: 'あなたが受け取る',
      Fee: '手数料:',
      Total: '合計:',
      Slippage: 'スリッページ:',
      ThirdPartyBridge: 'サードパーティブリッジ',
      ConfirmSwap: '確認する',
      ApproveToken: 'トークンを承認する',
      ApproveGas: 'ガスを支払わずに署名を承認する',
      ConfirmSwapRadio: 'スワップレシオを確認する',
      ConfirmGas: 'ガスを支払わずにスワップレシオを確認する',
      Processing: '処理中...',
      Completed: '完了',
      From: '開始日',
      To: '終了日',
      Return: '戻る',
      thirdParty: {
        tipTitle: 'リマインダーです',
        tipContent:
          'Bitlayerのブリッジはベータバージョンです。第三者が提供しているブリッジサービスをご利用ください。（少額推奨）',
      },
      btcHint: 'BTCおよびOrdinalプロトコルアセットのみサポート',
      btcCaution: {
        title: 'トランザクションを確認する',
        message:
          'このトランザクションは以下の入力を使用します。これらの入力が他の資産を持っていないことを確認してください。',
      },
      inscribeTransfer: '転送を刻印する',
      inscribedTitle: 'トランザクションが送信されました',
      inscribedTip: '確認後に刻印が開始されます。',
      viewOnExplorer: 'エクスプローラーで表示',
      refresh: '更新',
      pending: '保留中',
      selectAll: '全て選択',
      usdc: {
        change: 'USDC変更',
        changeLabel: 'トークンの変更：',
        changeDesc:
          'このトークンは、Circleのネイティブスマートコントラクトに基づいて更新されました。新しい USDC コントラクトアドレス：',
        confirm: '確認＆報酬',
        learnMore: '詳細を表示',
        bonusForYou: 'あなたにBitlayerボーナスポイント！',
        tips: '親切なアドバイス',
        lendingDesc:
          '貸出プラットフォーム上にUSDCローンがあることがわかりました。まずそれらをご返済ください。',
        borrowingTitle: 'USDC.e借入状況',
        viewOn: '{{ platform }} で表示',
        insufficentBalance: 'USDC残高が不足しています。ご連絡ください。',
      },
      errors: {
        invalidRequest: '無効なリクエスト',
        insufficentBtc:
          'BTC残高が不足しているか、または利用可能なUTXOがありません。未確認のトランザクションがある場合は、トランザクションが確認されるのを待ってからもう一度やり直してください。',
        insufficentRune:
          'ルーン残高が不足しているか、または利用可能なUTXOがありません。未確認のトランザクションがある場合は、トランザクションが確認されるのを待ってからもう一度やり直してください。',
        insufficentBrc20:
          'BRC20残高が不足しているか、または利用可能なUTXOがありません。未確認のトランザクションがある場合は、トランザクションが確認されるのを待ってからもう一度やり直してください',
        buildTxFailed: 'トランザクションのビルドに失敗しました。',
        transferError: '転送に失敗しました。',
        internalError: 'Internal service error.',
        swapError: 'スワップに失敗。',
        insufficientFunds:
          '資金不足：この取引を実行する際の総コスト (ガス*ガス料金+価値) が、アカウントの残高を上回っています。',
      },
    },
    faucet: {
      title: 'テストネット Faucet',
      description:
        '開発のために24時間ごとにBitlayerテストネットトークンを取得してください。 テストネットトークンには金銭的価値はなく、実際の価格で取引することはできません。',
      selectField: {
        label: 'トークンを選択する',
        placeholder: 'トークンを選択してください',
      },
      textFiled: {
        label: 'ウォレットアドレス',
        placeholder: 'Enter your Bitlayer Testnet address',
      },
      result: {
        gotTip: '{{tokens}}を取得しました！',
        gotAnother: '24時間以内に別の{{token}}をリクエストできます。',
        sending: '送信中...',
      },
      error: {
        testAddress: 'Bitlayerのテストネットアドレスを入力してください',
        verifyCaptcha: 'キャプチャの確認を完了してください',
        verifyFailed: 'キャプチャの検証に失敗しました',
        exceededLimit:
          '過去24時間以内にトークンをリクエストしました。{{h}}{{m}}{{s}} 待ってからもう一度お試しください。',
        invalidAddress: '有効なBitlayerテストネットアドレスを入力してください',
        unexpectedError: '予期しないサーバーエラーが発生しました。後ほどもう一度お試しください。',
      },
    },
    readyplayone: {
      title: 'Ready player I',
      description:
        '早期のビルダーやコントレビューター向けに <text>$50,000,000</text> のインセンティブを提供します。',
      register: '登録する',
      time: '2024年3月29日〜2024年5月10日',
      airdrop: 'プロジェクトごとに最大$1Mのインセンティブ！',
      Volume: 'ボリューム:',
      rules: {
        title: 'ルール',
        ruleContents: [
          {
            title: '登録する',
            texts: ['5月10日までに登録を完了してください。'],
            date: '3月〜4月',
          },
          {
            title: 'ローンチ',
            texts: ['メインネットでの立ち上げを開始し、これからの競争に備えましょう。'],
            date: '4月〜5月',
          },
          {
            title: 'エアドロップ',
            texts: [
              'リーダーボード対決に参加して、5,000万ドル以上の価値があるトークンのエアドロップを獲得しましょう。',
            ],
            date: '6月〜7月',
          },
        ],
      },
      colead: 'リード',
      investors: '投資家',
      partners: 'パートナー',
      setp: {
        register: {
          schemaName: 'プロジェクト名を提供してください',
          schemaWebsite: 'プロジェクトのウェブサイトを提供してください',
          schemaProjectDemo: 'プロジェクトデモを提供してください',
          schemaContact: '連絡先を提供してください',
          schemaEmail: 'プロジェクトのメールアドレスを提供してください',
          schemaTelegram: 'プロジェクトのTelegramを提供してください',
          schemaAgree: '利用規約に同意してください',
          newBuilderType: '新規ビルダー',
          experiencedBuilderType: '経験豊富なビルダー',
          formValidError:
            'フォームにいくつかの問題が見つかりました。強調された項目を確認し、修正後に再提出してください。',
          apiClientError:
            'フォームにいくつかの問題が見つかりました。項目を確認して、再度送信してください。 ({{code}})',
          apiServerError:
            '一時的な問題が発生しているようです。ページを更新するか、数分後にもう一度お試しください。 ({{code}})',
          formOne: {
            title: 'プロジェクト情報',
            name: {
              label: 'プロジェクト名',
              placeholder: '名前を入力してください',
            },
            builderType: {
              label: 'ビルダータイプ',
              placeholder: 'ビルダータイプを選択してください',
            },
            fundInfo: {
              label:
                'プロジェクト資金情報：(プロジェクトの資金調達状況を500文字で記述するか、詳細リンクを提供してください。)',
              placeholder: '資金情報を入力してください',
            },
            projectDemo: {
              label: 'プロジェクトデモ',
              placeholder: 'デモリンクを入力してください',
            },
            pitchDeck: {
              label: 'プロジェクトピッチデッキ (PDF形式のみサポート)',
              placeholder: 'アップロードするファイルを選択してください',
            },
            twitter: {
              label: 'プロジェクトのTwitter',
              placeholder: 'IDを入力してください',
            },
            website: {
              label: 'プロジェクトのウェブサイト',
              placeholder: 'URLを入力してください',
            },
            logo: {
              label: 'プロジェクトのロゴ <span>（1:1比率のデータ推奨）</span>',
              placeholder: 'アップロードするファイルを選択してください',
            },
            description: {
              label:
                'プロジェクトの説明/デッキ：プロジェクトの概要 (目的、解決される問題、ロードマップ、その仕組み) を500文字で要約するか、デッキのリンクを提供してください。',
              placeholder: '説明を入力してください',
            },
            category: {
              label: 'プロジェクトのカテゴリは何ですか？',
              placeholder: '選択してください',
              selects: [
                'インフラ',
                'Defi',
                'ソーシャル',
                'インスクリプション',
                'NFT',
                'MeMe トークン',
                'DAO',
                'ツーリング',
                'ウォレット',
                'クロスチェーン',
                'プライバシーコンピューティング',
                'プライバシートークン',
                '予測市場',
                'ストレージ',
                'Gamefi',
                'エンターテイメント',
                'メタバース',
                'その他',
              ],
            },
            stage: {
              label: 'プロジェクトのステージ',
              placeholder: '選択してください',
              selects: ['アイデア段階', '開発中', 'プロトタイプ利用可能', 'ライブ/ベータ'],
            },
            coin: {
              label:
                'Bitlayerのトークンアドレス <span>（取得できない場合は、N/Aを記入してください）</span>',
              placeholder: 'トークンアドレスを入力してください',
            },
            lockUp: {
              label:
                'Bitlayerのロックアップアドレス <span>（取得できない場合は、N/Aを記入してください）</span>',
              placeholder: 'ロックアップアドレスを入力してください',
            },
            contract: {
              label:
                'BitlayerのDappコントラクトアドレス<span>（取得できない場合はN/Aと記入してください）</span>',
              placeholder: 'コントラクトアドレスを入力してください',
            },
            defilama: {
              label:
                'プロジェクトDefillamaのリンク <span>（取得できない場合はN/Aと記入してください）</span>',
              placeholder: 'Defillamaリンクを入力してください',
            },
          },
          formTwo: {
            title: 'チーム情報',
            team: {
              label:
                'メンバーは何人いますか？主要チームメンバーの名前、役割、バックグラウンドをリストしてください。',
              placeholder: '説明を入力してください',
            },
          },
          formThree: {
            title: '連絡先担当者',
            name: {
              label: '名前',
              placeholder: 'プロジェクト名を入力してください',
            },
            email: {
              label: 'メールアドレス',
              placeholder: 'メールアドレスを入力してください',
            },
            telegram: {
              label: 'defillamaリンクを入力してください',
              placeholder: 'URLを入力してください',
            },
            twitter: {
              label: 'Twitterプロフィール',
              placeholder: 'URLを入力してください',
            },
          },
          consent: {
            title: '同意',
            label: 'ハッカソンの利用規約、プライバシーポリシー、および行動規約に同意します。',
          },
        },
        launch: {
          title: '正常に登録されました！',
          documents: '開発者ドキュメント',
          deploy: '今すぐあなたのプロジェクトをbitlayerにデプロイできます！',
          scanCode: 'コミュニティに参加するためにスキャンしてください',
          next: '次へ',
        },
        airdrop: {
          deploymentTip: 'プロジェクトの展開が完了したならば、これで完了です！',
          description:
            'あなたは一次選考に参加することができます！最終的な受賞者は$10Kから$300Kの間の助成金を受け取ります。 追加のボーナスには、$3Kから$5Kの名誉報酬、およびイベントを実行するための$1Mの賞金プールが含まれます！',
        },
      },
      gems: {
        title: 'Bitlayer Gemsエアドロップ',
        gems: 'Gems',
        tips: ' まもなく配布されます！',
        button: ' まもなく配布されます！',
        claimGems: {
          button: 'Gemsを請求する',
          label1: 'あなたは資格があります ',
          label2: 'Gemsエアドロップ！',
          'failed-title': 'がんばりましょう！',
          'label-failed':
            'このラウンドではGemsエアドロップを受ける資格がありません。今後のイベントをお楽しみに！',
          'button-failed': 'Dappsで報酬を獲得しましょう！',
        },
      },
      subTitle: 'Bitlayerブースター助成金プログラム',
      descriptionV4:
        '公平で透明な、月次レビュー、最大 <text> $100万 </text>  各プロジェクトのインセンティブ！',
      incentives: {
        tag: 'インセンティブ',
        'New Builder': {
          title: '新規ビルダー',
          bonus: '<span>{{amount}}</span> の名誉称号と報酬',
        },
        'Experienced builder': {
          title: '経験豊富なビルダー',
          bonus: '<span>{{amount}}</span> 限定イベント賞品プール',
        },
        grants: '助成制度',
        bonus: 'ボーナス',
      },
      rulesV4: {
        tag: 'ルール',
        ruleContents: [
          {
            title: '登録',
            texts: [
              '毎月30日までに登録されたプロジェクトは、現在の月で評価されます',
              '30日以降に登録されたプロジェクトは、翌月に評価されます',
              '登録に十分な情報が提供されていることを確認します',
            ],
            date: '毎月',
          },
          {
            title: '一次選考',
            texts: [
              'Bitleayerレビュー委員会は、提出された情報に基づいてローリングレビューを実施します。',
              '技術的なチェック、市場調査、および製品分析などの詳細なレビューが含まれる場合があります。',
            ],
            date: '推定7日間',
          },
          {
            title: '最終選挙',
            texts: [
              'Bitlayer財団は、投資機関の審査員とともに、一次ラウンドを通過したプロジェクトを評価します。',
              '各プロジェクトは10～15分間のプレゼンテーションを行います。',
              '勝利したプロジェクトには $10,000 から $300,000 の助成金が与えられます',
            ],
            date: '推定14日間',
          },
        ],
      },
      currentShortlist: {
        tag: '現在のショートリスト',
        primaryElection: '一次選考',
        finalElection: '最終リスト',
        like: 'いいね',
        grant: '助成制度',
      },
      updates: {
        tag: 'アップデート',
      },
    },
    luckyhelmet: {
      title: 'ラッキーヘルメット',
      description: 'ドライバーになろう。Bitlayerから利益を得る。',
      toGet: '取得方法について',
      miningBtn: 'ミント開始：2024年5月4日',
      assetTitle: 'レイヤー1の資産',
      assetDesc: 'ビットコインレイヤー1上のネイティブビットコイン通貨資産',
      mintTitle: 'レイヤー2でのミント',
      mintDesc: 'レイヤー1と比較してガス料金が低く、効率が良い',
      desc1: 'シームレスなL1/L2体験',
      desc2: 'Golden Shovel Bitlayer の特典',
      minting: 'ミント',
      mint: 'ミント',
      checkWhitelist: 'ホワイトリストをチェックする',
      minted: 'ミントされた',
      mintingSuccess: 'ミント成功',
      congratulations: 'おめでとうございます!',
      success: 'ホワイトリストに登録されています。',
      sorry: '残念ながら',
      failure: 'ホワイトリストには含まれていません。',
      finished: '終了',
      whitelistOnly: 'ホワイトリストのみ',
      trade: 'ラッキーヘルメットをトレード',
    },
    rank: {
      head: {
        head1: 'アワードプール',
        head2: 'Dapps',
        head3: '合計投票数',
        head4: '毎日の投票数',
      },
      banner: {
        tag: '準備中',
        title: 'READY PLAYER ONE',
        subtitle: 'ビルダー向けトークン',
        link: '「ルールをお読みください」',
        all: 'すべてのカテゴリ',
        switch: '切り替える',
        register: 'Dappを登録する',
      },
      list: {
        votes: '投票',
        vote: '投票',
        out: '投票はありません',
        invite: '招待ポイント',
      },
      carouse: {
        title: '私の Ready Player I ポイント',
        note: '「注意: Ready Player I ポイントは Bitlayer ポイントと同等ではありません。将来の特定のレートで交換できます。最新情報をフォローしてください！」',
        accepted: '招待を受け入れました',
        taskCenter: 'タスクセンター',
        now: '今すぐフォローする',
        vote: '毎回の投票',
        invite: '毎回の招待',
        follow: 'X でフォローする',
        hint1: '1日最大3回',
        hint2: '1回のタスク',
      },
      dialog: {
        band: {
          title: '友達を招待してブーストする',
          sorry:
            '「申し訳ありません。 あなたのTwitterはすでに異なるアドレスにリンクされています。別のTwitterアカウントを使用してください」',
        },
        invite: {
          need: '最初にX（Twitter）アカウントをバインドする必要があります！成功した招待ごとに500ポイントを獲得できます！',
          auth: 'Xで認証する',
          attention:
            '注意：エラーを避けるために、1つのTwitterアカウントは1つのアドレスにしかリンクできません',
        },
        note: {
          please: '注意：',
          note: 'Ready Player I ポイントは Bitlayer ポイントと同等ではありません。将来の特定のレートで交換できます。最新情報をフォローしてください！',
        },
        rules: {
          rules1: '毎日3つの投票を完了して300ポイントを獲得します。',
          rules2: '公式Twitterをフォローして500ポイントを受け取ります。',
          rules3: '新しいユーザーを招待して投票に成功するごとに、追加で500ポイントが加算されます。',
          rules4:
            'ランダム抽選で100個のBitlayerラッキーヘルメット が授与され、ランキングが高いほど当選確率が高くなります。',
          vote: 'お気に入りのプロジェクトに投票する',
          note: '「Bitlayer人気リスト投票に参加して、お気に入りのプロジェクトをサポートしましょう！イベント期間：2024年4月23日から2024年5月10日まで。リーダーボードページに毎日ログインし、1人3回の投票機会があります。公式Twitterをフォローして新しいユーザーを招待することで、追加ポイントを獲得できます。積極的な参加者はReady Player I イベントポイントを蓄積し、Bitlayerラッキーヘルメットのホワイトリスト入りのチャンスを得ることができます。',
          rewards: 'リワード：',
          boost: `「あなたのお気に入りのプロジェクトの応援し、リーダーボードの順位を押し上げて、公式NFTリワードを獲得しましょう！」`,
          gotIt: 'わかりました',
        },
        share: {
          copy: 'リンクをコピー',
          share: '共有する',
          invite: '投票を招待する',
          more: '友達からの追加の投票を取得する',
          connect: 'X（Twitter）に接続する',
        },
        vote: {
          vote: '投票',
          voteFor: '投票先：',
          daily: '1日3票',
          votes: '投票',
        },
        expire: {
          late: '少々遅かったようです!',
          expired: '投票は終了しました。',
          tuned: '詳細は公式Xで公開されます。今しばらくお待ちください!',
          thx: 'ありがとうございます！',
          follow: 'フォローしてください',
        },
      },
    },
    getGas: {
      recipient: '受信者のアドレス',
      placeholder: 'Please enter a Bitlayer address',
      invalid: '無効なアドレスです',
      purchase: '購入する',
      time: 'おおよその所要時間：支払い完了後 約1分',
      getGas: 'Bitlayer Gas Express を取得',
      getRegular: '通常のガスを取得',
      after: '転送後、注文を表示できます',
      history: '履歴',
      payment: '支払いアドレス',
      timeout: 'QRコードのタイムアウト',
      warning: '期限切れのアドレスに送金しないでください。トークンを失う可能性があります。',
      amount: '支払い金額：',
      cancelWarning:
        '注文がキャンセルされた場合、資金の損失を招く可能性があるため、支払いアドレスにトークンを送金しないでください。',
      ok: 'OK',
      cancel: 'Cancel Order',
      Complete: '完了',
      getBitlayerGas:
        'サポートされているウォレット/取引所であればどこからでも、トークンを支払いアドレスに送金することで、Bitlayer Gasを取得できます。',
      promotionNote1: 'プロモーション：のみ',
      fee: ' $1 ',
      promotionNote2: 'Get Gas Expressの料金で、今すぐ！',
      addressNotion:
        'ブロックチェーン、トークン、金額について正しく確認してください。各支払いアドレスは1回のみ使用できます。失敗した場合、入金した資金が失われる可能性があります。',
      promotion: '手数料プロモーション：',
      estimated: '推定時間：10-30分',
      ensure: '以下を確認してください',
      content: `<strong>ブロックチェーン、トークン、金額</strong>                 について正しく確認してください。各支払いアドレスは<strong>1回のみ使用できます。</strong> <div>失敗した場合、入金した資金が失われる可能性があります。</div>`,
      aboutFlash: 'About Flash Bridge-In',
      aboutInfo: `a. Flash Bridge-Inからのすべての資金はBitlayer公式ブリッジを介して送金されます。<br>b. ユーザーがBitlayerに迅速かつコスト効率よくアクセスできるように設計されています。`,
      get: 'Get',
      right: '正しく理解しよう！',
      otherwise: 'さもないと、',
      lose: '資産を失うかもしれません',
      Blockchain: 'ブロックチェーン',
      Amount: ' 金額',
      token: 'トークン',
      pay: '1件の支払いに1つのアドレス',
      kind: '親切なアドバイス',
      hasBridge:
        'BitLayerに属していないサードパーティのリンクにアクセスしようとしています。 サードパーティのリンクにおけるお客様の行動は、サードパーティのリンクのプライバシーポリシーおよび利用規約の対象となり、サードパーティはあなたに対して単独で責任を負うものとします。関連する状況と結果をご理解ください。',
      notBridge:
        'これはPEPEイベント専用のリンクです。ブリッジトランザクションを完了すると報酬を受け取ることができます。',
      commonRisk: '一般的なリスクまたは詐欺行為',
      phishing: 'フィッシングサイト',
      authorization: '認証リスク',
      highYield: 'ハイイールド詐欺',
      ponzischeme: 'ポンジスキーム',
      freeAirdrop: '無料のエアドロップ',
      contractLoophole: '契約の抜け穴',
      aware: 'リスクは認識しています',
    },
    leaderBoard: {
      title: 'BitLayerリーダーボードコンペティションエポック1',
      support: 'リスティングサポート',
      incentives: 'インセンティブ',
      liquidity: '流動性サポート',
      forPartners: 'エコパートナー向け',
      rules: 'ルールをお読みください',
      topTvl: 'トップ TVL',
      topTransation24: 'Top Transactions',
      topTransaction: 'トップ取引',
      topPopularity: '人気トップ',
      viewAll: '全て表示',
      tryNow: 'Try Now',
      countdown: 'エポックカウントダウン',
      countEnded: 'Epoch ended',
      stage3: 'ステージ3の対決エポック',
      stage2: 'ステージ2準備ステージ',
      gemsMinted: 'Gems発行総数',
      locked: 'ロックされた総額',
      transactionCount: '取引件数',
      totalLikes: 'いいね！総数',
      tvlRanking: 'BitlayerメインネットエコシステムにおけるTVLランキングリスト',
      discoverTvl: 'Bitlayerメインネットで構築されたトップTVLのDAppsを見つけよう',
      txnRanking: 'BitlayerメインネットエコシステムにおけるTXNランキングリスト',
      discoverTransactions: 'Bitlayerメインネットで構築された最も取引の多いDAppsを見つけよう',
      rankingList: 'Bitlayerメインネットエコシステムにおける人気ランキングリスト',
      discoverPopular: 'Bitlayerメインネットで構築された最も人気のあるDAppsを見つけよう',
      top10: 'トップ10リーダーボード',
      tvl: 'TVL',
      transactions: '取引',
      likes: 'いいね！',
      valueUpdate: 'BitlayerメインネットでDAppプロトコルにロックされた有効資産の総額、毎日更新。',
      numberUpdate: 'Dappコントラクトをコールするユーザーによって行われた有効な取引数、毎日更新。',
      likesUpdate: 'コンペティション期間中のプロジェクトの「いいね！」累積数、リアルタイム更新。',
      name: '名前',
      category: 'カテゴリ',
      gemMinted: '発行されたGems',
      gemsPoints:
        'Gemsは、ロックされた総額（TVL）、有効取引数、アクティブユーザー数、Bitlayer人気リーダーボードのパフォーマンス、商品の強さに基づいて計算されます。データは毎日 00:00 (UTC) に更新されます。',
      boost: 'ブースト',
      bindX: 'X（Twitter）を紐づける',
      bindNeed: '最初にX（Twitter）アカウントを紐付ける必要があります！',
      start: '5月23日に開始',
      airdrop: 'Airdrop中！',
      reward: 'リワード：',
      rank: 'DAppランキング',
      dappCenter: 'DAppセンター',
      more: 'もっと見る',
      introduce: 'アクティビティ紹介',
      gems: 'Gemsの100%',
      distributed: 'ユーザーに配布します！',
      rewards: '報酬',
      completed: '完了',
      winnerList: '勝者リスト',
      slots: '勝者',
      win: 'コンプリートして勝利',
      startAt: '開始位置',
      eventCountdown: 'Event countdown',
      ended: 'イベント終了',
      verify: '検証',
      verified: '認証済み',
      tutorial: 'ハウツーチュートリアル',
      participated: '参加中',
      tasks: 'タスク',
      congratulation: 'おめでとうございます ！',
      foru: 'あなたにBitlayerボーナスポイント！',
      export: 'Bitlayer dAppsを探索して報酬を獲得しよう！',
      phase: 'season {{num}}',
      'Free Drop': '無料ドロップ',
      '$1 Sweeptake': '$1 賞金',
    },
    dappDetail: {
      reward: 'リワード：',
      activity: 'アクティビティ',
      join: '今すぐ参加しよう！',
      introduce: 'アクティビティ紹介',
      whatIs: '{{name}} は？',
      overView: 'オーバービュー',
      dappIntro: 'DAppのご紹介！',
      event: `Airdropイベントへの参加方法：`,
      team: 'チーム',
      twitterText:
        '今すぐ@BitlayerLabsで{{name}}を試してください！100以上の#BitlayerエコシステムDappsからリワードを手に入れよう！',
      shareTitle: '友達とシェア',
      shareText: 'お気に入りのプロジェクトをお友達とシェアして試してみましょう！早い者勝ちです！',
    },
    miningGala: {
      meta: {
        title: 'BitlayerリワードとDapp Adropを獲得しよう',
      },
      head: {
        time: '2024年5月27日 13PM UTC ～ 2024年6月10日 13PM UTC',
        doubleGain: 'ダブルゲイン',
      },
      miningPolls: {
        title: 'マイニングプール',
        claim: 'Claim',
        minted: '発行済',
        taskTitle: 'パイオニアバッジを獲得するためにタスクを完了してください',
        taskTip: '{{project}}で少なくとも1つの取引を完了してください',
        shareText: '友達とシェア',
        or: 'または',
        hasHelmet: 'ラッキーヘルメットをホールド',
        auditReport: '監査レポート',
        miningGuide: 'マイニングガイド',
        airdrop: 'Airdrop',
        detailRules: 'ルールの詳細',
        claimSuccess: 'クレーム成功！',
        claimFailed: 'クレームに失敗しました。後でもう一度お試しください。',
        bitlayerTask: 'Bitlayerマイニングガラに参加する',
        lorenze: {
          desc: 'Babylonを通じたBitcoinのリキッドリステイクトークンの発行、取引、決済のためのプレミアプラットフォーム。',
          tasks: [
            'BTCをBabylonのプレローンチ・ステーキングにステークして、stBTCを手に入れましょう。',
            'Bitlayerメインネットに受けとったstBTCをブリッジします。',
            'stBTCでBitlayerのDeFiエコシステムに参加してください。',
          ],
        },
        bitsmiley: {
          desc: 'BTCネイティブのステーブルコインプロトコルで、当初はOKX VenturesとABCDELabs が出資。',
          tasks: [
            'bitsmileyでbitUSDを発行します。',
            'bitCowのbitUSD-USDT / bitUSD-WBTCに流動性を追加します。',
            'DefiプロトコルにbitUSDをステークします。',
          ],
        },
        avalon: {
          desc: 'Avalon Financeは、BTCレイヤー2で最良の分散型融資プロトコルになることを目指しています。',
          tasks: [
            'Bitlayerにアセットを供給して、最低1000供給ポイントを獲得しましょう。',
            'Bitlayerでアセットを借りて、最低500借入ポイントを獲得しましょう。',
            'Avalon Financeでの供給と借入をループさせます。',
          ],
        },
        bitcow: {
          desc: 'BTCネイティブのステーブルで集中した流動性AMM。',
          tasks: [
            'bitUSD-WBTC / bitUSD-USDT取引ペアに流動性を追加します。',
            'BitUSD-USDTまたはその他のペアの取引に参加します。',
            '新しい取引ペアを作成し、取引量を増加させます。',
          ],
        },
        pell: {
          desc: 'PellはBTCとLSDの利回りを分散させる分散型トラストマーケットプレイスで、BTC L2ネットワークのセキュリティ強化を目的としています。',
          tasks: [
            'ウォレットを接続してサインインします。',
            '0.001BTC以上ステークして、7日間ロックしておきます。',
            'ラッキードロー：1.5倍の永久ポイントカード×1000、100USDT×10。',
          ],
        },
        enzo: {
          desc: 'Enzo Financeは、最先端のアルゴリズムによるBitLayerチェーン上で最高の分散型貸出プロトコルです。',
          tasks: [
            'Enzo FinanceにBitlayerアセットをデポジット/ステイクします。',
            'Enzo FinanceでBitlayerのアセットを借ります。',
            '毎日1BTCプレゼント（条件：合計デポジット/借入> $100 USDT）。',
          ],
        },
        bitparty: {
          desc: 'BitPartyは、BTCエコシステム初の「アセットゲーム化コミュニティネットワーク」です！',
          tasks: [
            'クロスチェーンブリッジを通じてBitlayerアセットを入手しましょう。',
            'BitpartyにBitlayerアセットをステークしてポイントを獲得し、$BTPXを手に入れましょう。',
            'グループに参加して、他のメンバーと一緒にテリトリーを占領しましょう！',
          ],
        },
      },
      shareDialog: {
        title: '友達とシェア',
        desc: '今すぐ友達とシェアし、Bitlayerマイニングガラに参加して、限定のBitlayerマイニングガラのパイオニアバッジを集めましょう！',
      },
      tipDialog: {
        title: '親切なアドバイス',
        desc: 'BTCの残高が不十分で、以下の方法でBTCを補充できます。',
        depositBtn: 'ライトニングデポジット',
        bridgeBtn: 'ブリッジ',
      },
      twitterShare: {
        projectShare: `一緒に参加し、@BitlayerLabs #マイニングガラの {{name}} マイニングプールに参加しましょう！ {{money}} エアドロップがあなたと共有するのを待っています！\n`,
        badgeShare:
          '@BitlayerLabs #マイニングガラ パイオニアバッジを発行しました！ページに入って無料で発行しましょう！一緒に参加して 24,350,000 ドルのエアドロップをシェアしましょう！\n',
      },
      bottomTip: {
        title: 'Useful Tutorials for Bitlayer Mining Gala!',
        desc: 'Bitlayerマイニングガラへようこそ！次のガイダンスをご確認の上、マイニングの旅をお楽しみください！',
        tips: [
          'Bitlayer Gasを1分で入手します。',
          'アセットをBitlayerにブリッジするためのチュートリアル。',
          'Bitlayerマイニングガラに参加する最良の方法。',
          'Bitlayerマイニングガラパイオニアマイニングガイダンス。',
        ],
      },
    },
    userCenter: {
      title: 'Racer Center',
      dailyTasks: 'デイリータスク',
      bitlayerPoints: 'Bitlayerポイント',
      bitlayerGems: 'Bitlayer Gems',
      tasks: 'Tasks',
      badges: 'Badges',
      bitlayerDays: 'Bitlayerで<span>{{ days }}</span> 日間',
      txn: 'TXN',
      bridged: 'ブリッジ',
      unlocked: 'Lv{{ level }} unlocked',
      locked: 'Lv{{ level }} locked',
      unlocking: 'Lv{{ level }} unlocking <span>{{ progress }}%</span>',
      tabs: {
        newRacerTasks: '新しいレーサータスク',
        advancedTasks: '高度なタスク',
        ecoTasks: 'エコタスク',
        myBadges: 'マイバッジ',
      },
      task: {
        complete: '完了',
        claim: '獲得',
        pointsAcquired: '<span>{{ points }}</span> Bitlayerポイント獲得。',
        check: 'チェック',
      },
      claimGems: {
        title: 'おめでとうございます！宝石を受け取ってください！',
        action: '獲得先に移動',
      },
      badge: {
        coming: 'まもなく開始',
        inProgress: '進行中',
        finished: '終了',
        claim: 'ポイントを獲得',
        join: 'イベントに参加',
        owned: '所有バッジ',
        notOwned: '所有しないバッジ',
        rewardCanClaim: '獲得可能',
        rewardClaimed: '獲得済み',
      },
      reward: {
        points: 'ポイント',
        gems: 'Gems',
        btr: 'BTR',
      },
      errors: {
        twitter: {
          accountBinded:
            'このTwitterアカウントは他のアドレスに紐付けられています。別のTwitterアカウントを使用してください。',
        },
        unexpectedError: '予期せぬエラーが発生しました。後でもう一度お試しください。',
      },
      rankTitle: 'マイポイントランキング',
      topRacers: 'トップレーサー',
      racer: 'レーサー',
      gains: 'ゲイン',
      invite: {
        discover: 'Bitlayerを発見する',
        and: 'そして取得',
        invite: '友人を招待する',
        refer: '友達を紹介する',
        shareText:
          'お友達に紹介コード/リンクをシェアしてください。お友達がbitlayer.orgに登録し、Bitlayerでトークンを複数回送信すると、報酬を受け取ることができます。 (12時間ごとに更新)',
        my: '私の紹介',
      },
      twitter: {
        bindTitle: 'X（Twitter）を紐づける',
        bindDesc: '最初にX（Twitter）アカウントを紐付ける必要があります！',
        bindAction: 'Xで認証する',
        tips: '注意：エラーを避けるために、1つのTwitterアカウントは1つのアドレスにしかリンクできません.',
      },
      draw: {
        history: {
          history: '抽選履歴',
          time: ' 抽選時間',
          reward: '報酬',
        },
        error: '抽選チャンスは0回、友だちを誘ってさらにゲットしよう',
        up: '最大$10Kの報酬があります',
        unit: '抽選',
        invite: '招待してチャンスを増やし、ラッキーレベルをアップグレードしよう！',
        card: 'カードを引く',
        wait: 'オンチェーン確認中（30秒程度)',
        warning: '注意',
        sorry: '申し訳ありません！抽選1回につき最低1000ポイントです。ポイントが足りません。',
        ok: 'OK',
      },
    },
    activities: {
      guessingGame: {
        title: '推測して獲得する',
        subtitle: '価格予測のマスター',
        olympics2024: {
          title: '推測して獲得する',
          subtitle: 'パリオリンピック2024',
        },
        status: {
          inProgress: '進行中',
          coming: '近日公開',
          waiting: '待機中',
          finished: '終了',
          releasing: '24時間以内に結果を発表',
          endIn: '終了時刻',
        },
        placeBet: '賭けをする',
        bet: 'ベット',
        betUnit: '{{ step }} {{ rewardType }} /ユニット',
        betFor: '<icon /> <span>{{ amount }}</span> で {{ text }}',
        wonRewardFor: '<icon /> <span>{{ amount }}</span> で {{ text }} を獲得',
        myBets: 'マイベット',
        won: '勝利',
        return: '戻る',
        pool: 'プール',
        reward: {
          claim: '獲得',
          claimed: '受取済み',
          claimAll: '全ての報酬を受け取る',
          congratulations: 'おめでとうございます ！',
          claimedAllPoints:
            'レーサーセンターで <point /> <span>{{ points }}</span> を獲得しました。',
          claimedAllGems: 'レーサーセンターで <gem /> <span>{{ gems }}</span> を獲得しました。',
          claimedAllBoth:
            'レーサーセンターで <point /> <span>{{ points }}</span> と <gem /> <span>{{ gems }}</span> を獲得しました。',
        },
        messages: {
          placedBet: 'ベット完了です。幸運を祈ります！',
          failedToPlaceBet: 'ベットできませんでした。しばらくしてからもう一度お試しください。',
          claimedReward: '報酬の請求は完了しました！',
          failedToClaim: '報酬を請求できませんでした。しばらくしてからもう一度お試しください。',
          insufficient: 'ベットを行うには少なくとも {{ amount }} {{ rewardType }} が必要です。',
          alreadyClaimedAll: '既に全ての報酬を受け取っています。',
          failedToClaimAll:
            'すべての報酬を請求できませんでした。しばらくしてからもう一度お試しください。',
        },
      },
    },
    whiteList: {
      reward: 'マイリワード',
      address: 'アドレス',
      xp: 'Bitlayerポイント',
      lucky: 'ラッキーボーナス',
      points: 'ポイント',
      winnerList: '勝者リスト',
      description: `Notes: Users completing and verifying all tasks will be rewarded with Bitlayer Points within 7 days after the events ends. Project rewards will be distributed according to the project's official announcements. `,
    },
    opvm: {
      title: {
        slogon1: 'OpVM',
        slogon2: 'ビットコインを作る',
        slogon3: '検証可能',
        description: '最初のビットコイン検証レイヤー',
        whitePaper: 'ホワイトペーパー',
        earlyAccess: 'アーリーアクセス',
        comingSoon: '近日公開',
      },
      feature: {
        badge: '機能',
        title1: 'ビットコインのセキュリティを継承する',
        description1: '最低限のエンジニアリングコストで',
        title2: 'モジュラー＆コンポーザブル',
        description2: 'APIベースのサービス、プラグアンドプレイ',
        title3: '将来を見据えたアーキテクチャ',
        description3: '不正証明＋有効性証明',
        title4: 'カスタマイズ＆アップグレード可能',
        description4: 'オープンソースと監査可能',
      },
      advantages: {
        badge: 'メリット',
        num1: 'より迅速な開発タイムライン',
        num2: 'アグリゲーションによるガス料金の低減',
        num3: 'ビットコインとしてレベル1のセキュリティ',
      },
      usecase: {
        badge: '使用ケース',
        case1: 'ビットコインレイヤー2',
        case2: 'BTCのラッピング',
        case3: 'ビットコインブリッジ',
        case4: 'ビットコインステーキングプロトコル',
        start: '今すぐ始めよう！',
        early: 'アーリーアクセス',
      },
    },
  },
};
