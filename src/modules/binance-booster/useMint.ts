import { Config, useAccount, useConfig, useReadContract, useReadContracts } from 'wagmi';
import {
  estimateFeesPerGas,
  estimateGas,
  readContract,
  waitForTransactionReceipt,
  writeContract,
} from '@wagmi/core';
import { abi } from './abi';
import { useMutation } from '@tanstack/react-query';
import { encodeFunctionData } from 'viem';
import { useToast } from '@/hooks/toast';
import { handleException } from '../bridge/transactions/evm.client';

const binanceBoosterContract = import.meta.env.VITE_BINANCE_BOOSTER;

export const useBinanceBoosterMint = () => {
  const { address } = useAccount();
  const config = useConfig() as Config;
  const { toast } = useToast();

  const {
    data: balances,
    isLoading: isBalancesLoading,
    refetch: refreshBalance,
  } = useReadContract({
    abi,
    address: binanceBoosterContract,
    functionName: 'balanceOfBatch',
    args: [
      Array.from({ length: 5 }, () => address!),
      Array.from({ length: 5 }, (_, i) => BigInt(i)),
    ],
    query: {
      enabled: !!address,
    },
  });

  const { data: maxMintAmount } = useReadContract({
    abi,
    address: binanceBoosterContract,
    functionName: 'MAX_MINT_AMOUNT',
  });

  const { data: allTotalSupply, refetch: refreshAllTotalSupply } = useReadContracts({
    contracts: Array.from({ length: 5 }, (_, i) => BigInt(i)).map((tokenId) => {
      return {
        abi,
        address: binanceBoosterContract,
        functionName: 'totalSupply',
        args: [tokenId],
      };
    }),
  });

  const { mutateAsync: getTotalSupply } = useMutation({
    mutationFn: async (tokenId: bigint) => {
      const result = await readContract(config, {
        abi,
        address: binanceBoosterContract,
        functionName: 'totalSupply',
        args: [tokenId],
      });
      return result;
    },
  });

  const {
    mutate: mint,
    mutateAsync: mintAsync,
    isPending: isMinting,
  } = useMutation({
    mutationFn: async (tokenId: bigint) => {
      try {
        const totalSupply = await getTotalSupply(tokenId);
        if (totalSupply >= (maxMintAmount ?? 0n)) {
          return 'OUT_MAX_MINT_AMOUNT';
          // throw Error('out of max mint amount')
        }

        const functionName = 'mint';
        const args = [tokenId] as const;
        const feesPerGas = await estimateFeesPerGas(config);
        const gas = await estimateGas(config, {
          data: encodeFunctionData({
            abi,
            functionName,
            args,
          }),
          to: binanceBoosterContract,
          account: address,
        });

        const result = await writeContract(config, {
          abi,
          account: address,
          functionName,
          args,
          address: binanceBoosterContract,
          gas: gas,
          maxFeePerGas: feesPerGas.maxFeePerGas,
          maxPriorityFeePerGas: feesPerGas.maxPriorityFeePerGas,
        });

        await waitForTransactionReceipt(config, {
          hash: result,
          pollingInterval: 5_000,
        });

        return result;
      } catch (error) {
        handleException(error);
      }
    },
    onSuccess: (res) => {
      if (res === 'OUT_MAX_MINT_AMOUNT') {
        return toast('Sorry, you are late! This badge has been minted out.');
      }
      refreshBalance();
      refreshAllTotalSupply();
      toast('Mint successfully!');
    },
  });

  return {
    isBalancesLoading,
    balances: balances ?? [],
    maxMintAmount: maxMintAmount ?? 0n,
    allTotalSupply: [...(allTotalSupply ?? [])].map((res) => res.result) as bigint[],
    mint,
    mintAsync,
    isMinting,
  };
};
