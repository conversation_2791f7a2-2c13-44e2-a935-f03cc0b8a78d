import { QueryClient, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Config,
  estimateFeesPerGas,
  estimateGas,
  waitForTransactionReceipt,
  writeContract,
} from '@wagmi/core';
import { Address, encodeFunctionData } from 'viem';
import { useConfig } from 'wagmi';
import { abi as ybtcAbi } from '../abi/ybtc.b';
import { abi as ybtcStakingAbi } from '../abi/ybtc-staking-vault';
import { approveErc20, handleException } from '@/modules/bridge/transactions/evm.client';
import { ERC20Token, Token } from '@/wallets/config/type';
import { BtcfiV2CustodyContractType, BtcfiV2UserProjectWithdrawItem } from '@/modules/btcfi/types';
import { BigNumber } from 'ethers';
import ky from 'ky';

type RedeemParams = {
  address: Address;
  amount: bigint;
  token: Token;
  contract: Address;
  contractType: BtcfiV2CustodyContractType;
  projectId: number;
};

export function useRedeem() {
  const config = useConfig() as Config;
  const queryClient = useQueryClient();

  const { mutate, mutateAsync, ...mutation } = useMutation({
    mutationKey: ['btcfi-v2/redeem'],
    mutationFn: async (params: RedeemParams) => {
      try {
        if (params.contractType === 'YBTC.B') {
          await ybtcRedeem(config, params);
        } else {
          await requestClaim(config, params, queryClient);
        }
      } catch (err) {
        handleException(err, 'Failed to redeem');
      }
    },
  });

  return {
    redeem: mutate,
    redeemAsync: mutateAsync,
    ...mutation,
  };
}

async function ybtcRedeem(config: Config, params: RedeemParams) {
  const functionName = 'withdraw';
  const args = [params.amount] as const;

  const feesPerGas = await estimateFeesPerGas(config);
  const gas = await estimateGas(config, {
    data: encodeFunctionData({
      abi: ybtcAbi,
      functionName,
      args,
    }),
    to: params.contract,
  });

  const tx = await writeContract(config, {
    abi: ybtcAbi,
    address: params.contract,
    functionName,
    args,
    gas,
    maxFeePerGas: feesPerGas.maxFeePerGas,
    maxPriorityFeePerGas: feesPerGas.maxPriorityFeePerGas,
  });
  await waitForTransactionReceipt(config, {
    hash: tx,
    pollingInterval: 5_000,
  });
}

async function requestClaim(config: Config, params: RedeemParams, queryClient: QueryClient) {
  await approveErc20(
    params.contract,
    { config },
    {
      from: params.address,
      amount: BigNumber.from(params.amount),
      token: params.token as ERC20Token,
    },
  );

  const withdraws = await loadUserProjectWithdraws({
    queryKey: ['btcfi-v2/redeems', params.projectId, params.address],
  });

  const functionName = 'requestClaim';
  const args = [params.amount] as const;

  const feesPerGas = await estimateFeesPerGas(config);
  const gas = await estimateGas(config, {
    data: encodeFunctionData({
      abi: ybtcStakingAbi,
      functionName,
      args,
    }),
    to: params.contract,
  });

  const tx = await writeContract(config, {
    abi: ybtcStakingAbi,
    address: params.contract,
    functionName,
    args,
    gas,
    maxFeePerGas: feesPerGas.maxFeePerGas,
    maxPriorityFeePerGas: feesPerGas.maxPriorityFeePerGas,
  });

  await waitForTransactionReceipt(config, {
    hash: tx,
    pollingInterval: 5_000,
  });

  let newWithdraws = withdraws.slice();
  // Poll for updated withdraw data until new withdrawal is detected
  // eslint-disable-next-line no-constant-condition
  while (true) {
    await new Promise((resolve) => setTimeout(resolve, 5_000));

    newWithdraws = await loadUserProjectWithdraws({
      queryKey: ['btcfi-v2/redeems', params.projectId, params.address],
    });

    // Check if new withdrawal was added or existing withdrawal amount increased
    const hasNewWithdrawal = newWithdraws.length > withdraws.length;
    const hasIncreasedAmount =
      withdraws.length > 0 &&
      newWithdraws.length > 0 &&
      newWithdraws[0].amount > withdraws[0].amount;

    if (hasNewWithdrawal || hasIncreasedAmount) {
      break;
    }
  }
  queryClient.setQueryData(['btcfi-v2/redeems', params.projectId, params.address], newWithdraws);
}

type ClaimParams = {
  contract: Address;
  projectId: number;
  address: Address;
  queryClient: QueryClient;
};

export function useClaim() {
  const config = useConfig() as Config;

  const { mutate, mutateAsync, ...mutation } = useMutation({
    mutationKey: ['btcfi-v2/claim'],
    mutationFn: async (params: ClaimParams) => {
      const withdraws = await loadUserProjectWithdraws({
        queryKey: ['btcfi-v2/redeems', params.projectId, params.address],
      });

      try {
        const functionName = 'claim';
        const args = [] as const;

        const feesPerGas = await estimateFeesPerGas(config);
        const gas = await estimateGas(config, {
          data: encodeFunctionData({
            abi: ybtcStakingAbi,
            functionName,
            args,
          }),
          to: params.contract,
        });

        const tx = await writeContract(config, {
          abi: ybtcStakingAbi,
          address: params.contract,
          functionName,
          args,
          gas,
          maxFeePerGas: feesPerGas.maxFeePerGas,
          maxPriorityFeePerGas: feesPerGas.maxPriorityFeePerGas,
        });

        await waitForTransactionReceipt(config, {
          hash: tx,
          pollingInterval: 5_000,
        });
      } catch (err) {
        handleException(err, 'Failed to claim');
      }

      let newWithdraws = withdraws.slice();
      // eslint-disable-next-line no-constant-condition
      while (newWithdraws.length > 0) {
        await new Promise((resolve) => setTimeout(resolve, 5_000));

        newWithdraws = await loadUserProjectWithdraws({
          queryKey: ['btcfi-v2/redeems', params.projectId, params.address],
        });
      }
      params.queryClient.setQueryData(
        ['btcfi-v2/redeems', params.projectId, params.address],
        newWithdraws,
      );
    },
  });

  return {
    claim: mutate,
    claimAsync: mutateAsync,
    ...mutation,
  };
}

export async function loadUserProjectWithdraws({
  queryKey,
}: {
  queryKey: [string, number, Address];
}) {
  const [, projectId, address] = queryKey;
  if (!address) {
    return [];
  }
  return ky
    .get(`/btcfi-v2/projects/${projectId}/redeems`, {
      searchParams: {
        address: address,
      },
    })
    .json<BtcfiV2UserProjectWithdrawItem[]>();
}
