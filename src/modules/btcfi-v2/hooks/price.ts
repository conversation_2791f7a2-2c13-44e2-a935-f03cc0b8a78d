import { BtcfiV2CustodyContractType } from '@/modules/btcfi/types';
import { Token } from '@/wallets/config/type';
import { useMemo } from 'react';
import { Address, parseUnits } from 'viem';
import { useConfig } from 'wagmi';
import { abi as ybtcStakingAbi } from '../abi/ybtc-staking-vault';
import { useQuery } from '@tanstack/react-query';
import { Config, readContract } from '@wagmi/core';

const PRICE_DECIMALS = 18n;

// Calculate LP token amount from token amount, price is LP/Token in 18 decimals
export function lpFrom(token: Token, amount: bigint, lp: Token, price: bigint): bigint {
  // Convert token amount to 18 decimals for calculation
  // If token is BTC with 18 decimals, it's already in the right format
  // If token has 8 decimals, we need to scale it up to 18 decimals
  const tokenAmountIn18Decimals =
    token.decimals === 18 ? amount : amount * 10n ** (18n - BigInt(token.decimals));

  // Calculate LP amount: tokenAmount * price / 10^18
  // price is LP/Token in 18 decimals, so we multiply by 10^18 and divide by price
  const lpAmountIn18Decimals = (tokenAmountIn18Decimals * 10n ** PRICE_DECIMALS) / price;

  // Convert LP amount from 18 decimals to LP token's actual decimals
  const lpAmount =
    lp.decimals === 18
      ? lpAmountIn18Decimals
      : lpAmountIn18Decimals / 10n ** (18n - BigInt(lp.decimals));

  return lpAmount;
}

// Calculate token amount from LP token amount, price is LP/Token in 18 decimals
export function fromLp(lp: Token, amount: bigint, token: Token, price: bigint): bigint {
  // Convert LP amount to 18 decimals for calculation
  const lpAmountIn18Decimals =
    lp.decimals === 18 ? amount : amount * 10n ** (18n - BigInt(lp.decimals));

  // Calculate token amount: lpAmount * 10^18 / price
  // price is LP/Token in 18 decimals, so we multiply and then divide by 10^18
  const tokenAmountIn18Decimals = (lpAmountIn18Decimals * price) / 10n ** PRICE_DECIMALS;

  // Convert token amount from 18 decimals to token's actual decimals
  const tokenAmount =
    token.decimals === 18
      ? tokenAmountIn18Decimals
      : tokenAmountIn18Decimals / 10n ** (18n - BigInt(token.decimals));

  return tokenAmount;
}

type UseLpPriceParams = {
  token: Token;
  lp: Token;
  amount: string;
  contract: Address;
  contractType: BtcfiV2CustodyContractType;
};

export function useLpFrom({ token, lp, amount, contract, contractType }: UseLpPriceParams) {
  const { data: price } = usePrice(contract, contractType);
  return useMemo(() => {
    if (!price) {
      return undefined;
    }

    let amountBigInt: bigint | undefined = undefined;
    try {
      amountBigInt = parseUnits(amount, token.decimals);
    } catch (error) {
      return undefined;
    }
    return lpFrom(token, amountBigInt, lp, price);
  }, [token, lp, amount, price]);
}

export function useFromLp({ lp, token, amount, contract, contractType }: UseLpPriceParams) {
  const { data: price } = usePrice(contract, contractType);
  return useMemo(() => {
    if (!price) {
      return undefined;
    }

    let amountBigInt: bigint | undefined = undefined;
    try {
      amountBigInt = parseUnits(amount, lp.decimals);
    } catch (error) {
      return undefined;
    }
    return fromLp(lp, amountBigInt, token, price);
  }, [lp, token, amount, price]);
}

function usePrice(contract: Address, contractType: BtcfiV2CustodyContractType) {
  const config = useConfig() as Config;
  return useQuery({
    queryKey: ['btcfi-v2/price', contract, contractType] as const,
    queryFn: async () => {
      if (contractType !== 'btcfi-v2') {
        return 1_000_000_000_000_000_000n;
      }
      const data = await readContract(config, {
        abi: ybtcStakingAbi,
        address: contract,
        functionName: 'getCurrentPrice',
        args: [],
      });
      return data;
    },
  });
}
