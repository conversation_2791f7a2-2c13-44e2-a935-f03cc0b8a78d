import { WalletConnector } from '@/components/featured/wallet';
import { Button } from '@/components/ui/button';
import { CornerMarkGroup } from '@/components/ui/corner-mark';
import { DynamicIcon } from '@/components/ui/icon';
import { useAccount as useWalletAccount } from '@/hooks/wallet/account';
import { walletDrivers } from '@/hooks/wallet/common';
import { useDisconnect } from '@/hooks/wallet/disconnect';
import { shortHash } from '@/lib/utils';
import { BtcfiV2Project, BtcfiV2ThirdPartyCustody } from '@/modules/btcfi/types';
import { chainMap } from '@/wallets/config/chains';
import { BaseChainType, NetworkType } from '@/wallets/config/type';
import { ConnectModal, useWallet } from '@suiet/wallet-kit';
import { useQuery } from '@tanstack/react-query';
import ky from 'ky';
import { ArrowRightIcon, ArrowUpRightIcon, ShareIcon } from 'lucide-react';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

type ThirdPartyContentProps = {
  project: BtcfiV2Project;
  name: string;
  custody: BtcfiV2ThirdPartyCustody;
};

export function ThirdPartyContent({ project, custody, name }: ThirdPartyContentProps) {
  const { t } = useTranslation();
  const { links, staked: sourceToken, receipt: targetToken } = custody;
  const sourceChain = chainMap[sourceToken.chain];
  const targetChain = chainMap[targetToken.chain];

  return (
    <div className="bl-space-y-6">
      <BTRAllowance chain={targetChain} project={project} />
      <div className="bl-pt-2">
        <div className="bl-flex bl-gap-5 bl-items-center">
          <NumberPoint>1</NumberPoint>
          <div className="bl-text-white bl-text-lg lg:bl-text-xl">
            {t('pages.btcfi.custody.bridgeTo', {
              token: sourceToken.symbol,
              chain: targetChain.name,
            })}
          </div>
        </div>
        <div className="bl-border-l bl-border-dashed bl-border-primary bl-my-2 bl-ml-3.5 bl-pl-4 bl-py-4 bl-flex bl-flex-col bl-items-center bl-gap-5">
          <div className="bl-grid bl-grid-cols-2 bl-gap-2 bl-w-full bl-relative">
            <Button
              variant="outline-3"
              className="bl-w-full bl-text-base lg:bl-text-xl disabled:bl-opacity-100"
              disabled
            >
              <div className="bl-flex bl-items-center bl-gap-2">
                <DynamicIcon icon={sourceChain.icon} className="bl-size-6" />
                {sourceToken.symbol}
              </div>
            </Button>
            <Button
              variant="outline-3"
              className="bl-w-full bl-text-base lg:bl-text-xl disabled:bl-opacity-100"
              disabled
            >
              <div className="bl-flex bl-items-center bl-gap-2">
                <DynamicIcon icon={targetChain.icon} className="bl-size-6" />
                {targetToken.symbol}
              </div>
            </Button>
            <div className="bl-size-6 bl-absolute bl-top-1/2 -bl-translate-y-1/2 bl-left-1/2 -bl-translate-x-1/2 bl-flex bl-items-center bl-justify-center">
              <ArrowRight className="bl-size-6" />
            </div>
          </div>
          <Button
            className="bl-w-44 lg:bl-w-52 bl-text-base lg:bl-text-xl"
            overlayFrom="none"
            asChild
          >
            <a href={links.bridge} target="_blank" rel="noreferrer">
              <span>{t('pages.btcfi.custody.goBridge')}</span>
            </a>
          </Button>
        </div>

        <div className="bl-flex bl-gap-5">
          <NumberPoint>2</NumberPoint>
          <div className="bl-text-white bl-text-lg lg:bl-text-xl">
            {t('pages.btcfi.custody.earnFrom', { name })}
          </div>
        </div>
        <div className="bl-pl-2 bl-pt-4 bl-flex bl-flex-col bl-items-center">
          <div className="bl-grid bl-grid-cols-3 bl-gap-3.5">
            <Button variant="outline-3" className="bl-w-full bl-text-base lg:bl-text-xl" asChild>
              <a href={links.stake} target="_blank" rel="noreferrer">
                <span>{t('pages.btcfi.custody.stake')}</span>
              </a>
            </Button>
            <Button variant="outline-3" className="bl-w-full bl-text-base lg:bl-text-xl" asChild>
              <a href={links.unstake} target="_blank" rel="noreferrer">
                <span>{t('pages.btcfi.custody.unstake')}</span>
              </a>
            </Button>
            <Button variant="outline-3" className="bl-w-full bl-text-base lg:bl-text-xl" asChild>
              <a href={links.claim} target="_blank" rel="noreferrer">
                <span>{t('pages.btcfi.custody.claim')}</span>
              </a>
            </Button>
          </div>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width={241}
            height={40}
            viewBox="0 0 241 40"
            fill="none"
          >
            <path
              d="M1 0v19.088h119.5M240 0v19.088H120.5m0 0V0m0 19.088V40"
              stroke="#AEB5C5"
              strokeWidth={0.848}
            />
          </svg>
          <a
            href={links.project}
            target="_blank"
            rel="noreferrer"
            className="bl-text-[22px] bl-text-white bl-flex bl-items-center bl-gap-1 hover:bl-text-primary"
          >
            <DynamicIcon icon={project.icon} className="bl-size-6" />
            {name}
            <ArrowUpRightIcon className="bl-size-4" />
          </a>
        </div>
      </div>

      <article className="btcfi-v2-risk">
        <h3>Risk</h3>
        <h4>Contract Risk:</h4>
        <p>
          If there are vulnerabilities in the Vault contract or cooperating protocol contracts, it
          may result in asset loss.
        </p>
        <h4>Strategy Execution Risk:</h4>
        <p>
          If the strategies do not meet expectations, returns may be lower than predicted or even
          negative.
        </p>
        <h4>Liquidity Risk:</h4>
        <p>
          If the strategy funds are not recovered in time when the Vault matures, redemption may be
          delayed.
        </p>
        <h4>Cross-chain Bridge Risk:</h4>
        <p>
          If the Vault involves cross-chain bridge operations, users bear the potential technical or
          delay risks during the bridging process.
        </p>
      </article>
    </div>
  );
}

function NumberPoint({ children }: { children: React.ReactNode }) {
  return (
    <div className="bl-text-white bl-bg-primary bl-rounded-full bl-flex bl-items-center bl-justify-center bl-text-xs bl-size-7 bl-font-title bl-min-w-0 bl-shrink-0">
      {children}
    </div>
  );
}

type BTRAllowanceProps = {
  chain: BaseChainType;
  project: BtcfiV2Project;
};

function useAccount(chain: BaseChainType) {
  const { address, driver } = useWalletAccount({ network: chain.networkType });
  const { address: suiAddress, adapter } = useWallet();
  const isSui = chain.networkType === NetworkType.sui;

  return isSui
    ? {
        address: suiAddress,
        icon: adapter?.icon,
      }
    : {
        address,
        icon: driver ? walletDrivers[driver]?.icon : undefined,
      };
}

function BTRAllowance({ chain, project }: BTRAllowanceProps) {
  const { t } = useTranslation();
  const { address, icon } = useAccount(chain);
  const { disconnect } = useDisconnect();

  const isSui = chain.networkType === NetworkType.sui;
  const [showModal, setShowModal] = useState(false);

  const { data } = useQuery({
    queryKey: ['btcfi-v2/btr-allowance', project.id, address] as const,
    queryFn: async () => {
      if (!address) {
        return undefined;
      }
      const response = await ky.get(`/btcfi-v2/user-reward`, {
        searchParams: {
          project_id: project.id,
          address: address,
        },
      });
      const data = await response.json<{ reward: number }>();
      return data.reward;
    },
    enabled: !!address,
  });

  const content = address ? (
    <div className="bl-font-title bl-text-2xl bl-text-primary bl-flex bl-items-center bl-gap-2">
      <img src="/images/btcfi/btr-new.svg" alt="" className="bl-size-8" />
      {data === undefined ? '--' : data.toFixed(2)} $BTR
    </div>
  ) : isSui ? (
    <ConnectModal
      open={showModal}
      onOpenChange={setShowModal}
      onConnectSuccess={() => {
        setShowModal(false);
      }}
      onConnectError={() => {
        setShowModal(false);
      }}
    >
      <Button overlayFrom="none" className="bl-w-52 bl-h-9">
        <span>{t('common.connect')}</span>
      </Button>
    </ConnectModal>
  ) : (
    <WalletConnector chain={chain}>
      <Button overlayFrom="none" className="bl-w-52 bl-h-9">
        <span>{t('common.connect')}</span>
      </Button>
    </WalletConnector>
  );

  const handleDisconnect = () => {
    disconnect(chain.networkType);
  };

  return (
    <div className="bl-py-4 bl-px-3 bl-relative bl-flex bl-flex-col bl-gap-4 bl-bg-primary/10">
      <CornerMarkGroup className="[--bl-space:8px]" />
      <div
        className="bl-absolute bl-top-0 bl-left-0 bl-w-full bl-h-full bl-opacity-10 -bl-z-10"
        style={{
          backgroundImage: `url("/images/bg-grid.svg")`,
          backgroundSize: '10px 10px',
          backgroundRepeat: 'repeat',
        }}
      ></div>
      <div className="bl-text-sm bl-ml-2 bl-relative">{t('pages.btcfi.btrAllowance')}</div>
      <div className="bl-flex bl-items-center bl-justify-center bl-relative bl-z-10 last:bl-mb-4">
        {content}
      </div>
      {address && (
        <div className="bl-flex bl-justify-center bl-gap-1">
          {icon && (
            <div className="bl-size-8 bl-bg-white/10 bl-flex bl-items-center bl-justify-center">
              <DynamicIcon icon={icon} className="bl-size-4" />
            </div>
          )}
          <div className="bl-h-8 bl-bg-white/10 bl-px-4 bl-flex bl-items-center bl-justify-between bl-gap-4 bl-w-fit">
            <div className="bl-text-xs bl-text-white">{shortHash(address || '')}</div>
            <button className="bl-text-primary hover:bl-text-white" onClick={handleDisconnect}>
              <ShareIcon className="bl-rotate-90 bl-size-4" />
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

const ArrowRight = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    {...props}
  >
    <rect
      x={-0.5}
      y={0.5}
      width={23}
      height={23}
      rx={11.5}
      transform="matrix(-1 0 0 1 23 0)"
      fill="#111"
    />
    <rect
      x={-0.5}
      y={0.5}
      width={23}
      height={23}
      rx={11.5}
      transform="matrix(-1 0 0 1 23 0)"
      stroke="#363636"
    />
    <path
      d="M16.914 11.944H4.736c-.278 0-.544.113-.74.313a1.077 1.077 0 0 0 0 1.508c.196.2.462.312.74.312h14.381c.235 0 .465-.07.66-.204.196-.133.349-.322.438-.544a1.23 1.23 0 0 0-.262-1.32l-3.815-3.853a1.046 1.046 0 0 0-.742-.31 1.03 1.03 0 0 0-.739.317 1.068 1.068 0 0 0-.303.756 1.086 1.086 0 0 0 .31.753l2.248 2.272h.002Z"
      fill="#AEB5C5"
    />
  </svg>
);
