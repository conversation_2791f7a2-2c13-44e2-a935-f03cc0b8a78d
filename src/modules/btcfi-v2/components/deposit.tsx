import { S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>on, WalletConnector } from '@/components/featured/wallet';
import { Button } from '@/components/ui/button';
import { AmountInput } from '@/components/ui/form-field';
import { DynamicIcon } from '@/components/ui/icon';
import { useAccount } from '@/hooks/wallet/account';
import { useWalletBalance } from '@/hooks/wallet/balance';
import { BaseChainType, Token } from '@/wallets/config/type';
import { ArrowDownIcon, LoaderIcon } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Address, parseUnits } from 'viem';
import { useDeposit, useMaxDepositAmount } from '../hooks/deposit';
import { ReadableError, UserRejectedError } from '@/modules/bridge/transactions/errors';
import { useToast } from '@/hooks/toast';
import { ButtonSelectTrigger, Select, SelectContent, SelectItem } from '@/components/ui/select';
import { formatUnits } from '@/lib/utils';
import { BtcfiV2CustodyContractType } from '@/modules/btcfi/types';
import { useLpFrom } from '../hooks/price';
import { useRevalidator } from '@remix-run/react';

export type DepositFormProps = {
  chain: BaseChainType;
  sourceTokens: Token[];
  targetToken: Token;
  contract: Address;
  contractType?: BtcfiV2CustodyContractType;
};

export function DepositForm({
  sourceTokens,
  targetToken,
  chain,
  contract,
  contractType = 'YBTC.B',
}: DepositFormProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const { address, chain: currentChain } = useAccount({ network: chain.networkType });
  const [amount, setAmount] = useState('');
  const [sourceTokenIndex, setSourceTokenIndex] = useState(0);
  const sourceToken = sourceTokens[sourceTokenIndex];
  const { revalidate } = useRevalidator();

  const { data: balance, refetch: refetchSource } = useWalletBalance({
    network: chain.networkType,
    address,
    token: sourceToken,
    chain,
  });

  const { data: targetBalance, refetch: refetchTarget } = useWalletBalance({
    network: chain.networkType,
    address,
    token: targetToken,
    chain,
  });

  const { depositAsync, isPending } = useDeposit();
  const { queryMaxAsync } = useMaxDepositAmount();

  const amountValidator = (value: string) => {
    try {
      parseUnits(value, sourceToken.decimals);
    } catch (error) {
      return false;
    }

    const [, decimal] = value.split('.');
    if (decimal && decimal.length > Math.min(sourceToken.decimals, targetToken.decimals)) {
      return false;
    }

    return !isNaN(Number(value)) && Number(value) >= 0;
  };

  const handleClickMax = async () => {
    if (!balance) {
      return;
    }

    if (!address) {
      return;
    }

    const max = await queryMaxAsync({
      address: address as Address,
      token: sourceToken,
      amount: 10000000000n,
      contract: contract,
      contractType,
      balance: balance.value,
    });

    setAmount(formatUnits(max, sourceToken.decimals, { keepDecimals: 8, trimZeros: true }));
  };

  const [isAmountValid, amountTips] = useMemo(() => {
    if (!amount || !balance) {
      return [false, t('common.confirm')];
    }

    let bigAmount = 0n;
    try {
      bigAmount = parseUnits(amount, sourceToken.decimals);
    } catch (error) {
      return [false, t('pages.bridge.invalidAmount')];
    }

    if (bigAmount === 0n) {
      return [false, t('pages.bridge.invalidAmount')];
    }

    if (bigAmount > balance.value) {
      return [false, t('common.insufficientBalance')];
    }

    return [true, ''];
  }, [amount, balance, sourceToken.decimals, t]);

  const lpAmount = useLpFrom({
    token: sourceToken,
    lp: targetToken,
    amount,
    contract,
    contractType,
  });

  const handleDeposit = async () => {
    if (!isAmountValid || !address) {
      return;
    }

    try {
      await depositAsync({
        address: address as Address,
        token: sourceToken,
        amount: parseUnits(amount, sourceToken.decimals),
        contract: contract,
        contractType,
      });
      toast(t('pages.btcfi.custody.depositSuccess'));
    } catch (e) {
      if (e instanceof UserRejectedError) {
        return;
      }

      if (e instanceof ReadableError) {
        toast(t(`pages.bridge.errors.${e.message}`));
      } else {
        toast(t('pages.btcfi.custody.depositFailed'));
      }
      return;
    }

    refetchSource();
    refetchTarget();
    setAmount('');
    revalidate();
  };

  const renderActionButton = () => {
    if (!address || !currentChain) {
      return (
        <WalletConnector chain={chain}>
          <Button className="bl-w-full" overlayFrom="none">
            {t('common.connect')}
          </Button>
        </WalletConnector>
      );
    }

    if (currentChain.id !== chain.id) {
      return <SwitchChainButton chain={chain} className="bl-w-full" overlayFrom="none" />;
    }

    if (isPending) {
      return (
        <Button className="bl-w-full bl-gap-1" overlayFrom="none" disabled>
          <LoaderIcon className="bl-size-6 bl-animate-spin" />
          <span>{t('common.pending')}</span>
        </Button>
      );
    }

    if (!isAmountValid) {
      return (
        <Button className="bl-w-full" overlayFrom="none" disabled>
          <span>{amountTips}</span>
        </Button>
      );
    }

    if (!lpAmount) {
      return (
        <Button className="bl-w-full" overlayFrom="none" disabled>
          <span>{t('pages.bridge.invalidAmount')}</span>
        </Button>
      );
    }

    return (
      <Button overlayFrom="none" className="bl-w-full" onClick={handleDeposit}>
        <span>{t('common.confirm')}</span>
      </Button>
    );
  };

  return (
    <div className="bl-space-y-3">
      <div className="bl-border bl-border-card-border bl-p-5 bl-space-y-3.5 bl-bg-black bl-relative">
        <div className="bl-flex bl-items-center bl-justify-between">
          <div>{t('pages.btcfi.custody.deposit')}</div>
          {sourceTokens.length > 1 ? (
            <Select onValueChange={(v) => setSourceTokenIndex(Number(v))}>
              <ButtonSelectTrigger buttonSize="md" className="bl-w-40 bl-h-10 bl-px-2">
                <div className="bl-flex bl-items-center bl-gap-2">
                  <DynamicIcon icon={sourceToken.icon} className="bl-size-5" />
                  {sourceToken.symbol}
                </div>
              </ButtonSelectTrigger>
              <SelectContent>
                {sourceTokens.map((token, index) => (
                  <SelectItem key={index} value={index.toString()}>
                    <div className="bl-flex bl-items-center bl-gap-2">
                      <DynamicIcon icon={token.icon} className="bl-size-5" />
                      {token.symbol}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <Button variant="outline-3" className="bl-w-40">
              <div className="bl-flex bl-items-center bl-gap-2">
                <DynamicIcon icon={sourceToken.icon} className="bl-size-5" />
                {sourceToken.symbol}
              </div>
            </Button>
          )}
        </div>
        <div className="bl-flex bl-items-center bl-justify-between">
          <AmountInput
            value={amount}
            onChange={setAmount}
            validator={amountValidator}
            className="bl-w-full"
            placeholder="0.00"
          />
        </div>
        <div className="bl-flex bl-items-center bl-justify-between">
          <div></div>
          <div className="bl-flex bl-gap-2">
            <span>
              {balance
                ? formatUnits(balance.value, sourceToken.decimals, {
                    keepDecimals: 8,
                    trimZeros: true,
                  })
                : '--'}{' '}
              {sourceToken.symbol}
            </span>
            <button className="bl-text-primary" onClick={handleClickMax}>
              {t('pages.btcfi.custody.max')}
            </button>
          </div>
        </div>
        <div className="bl-absolute -bl-bottom-7 lg:-bl-bottom-8 bl-left-1/2 -bl-translate-x-1/2">
          <div className="bl-size-[42px] lg:bl-size-12 bl-rounded-full bl-bg-primary bl-text-black bl-flex bl-items-center bl-justify-center">
            <ArrowDownIcon className="bl-size-6" />
          </div>
        </div>
      </div>
      <div className="bl-border bl-border-card-border bl-p-5 bl-space-y-3.5 bl-bg-black">
        <div className="bl-flex bl-items-center bl-justify-between">
          <div>{t('pages.btcfi.custody.get')}</div>
          <Button
            variant="outline-3"
            className="bl-w-40 bl-min-w-0 bl-shrink-0 disabled:bl-opacity-100"
            disabled
          >
            <div className="bl-flex bl-items-center bl-gap-2">
              <DynamicIcon icon={targetToken.icon} className="bl-size-5" />
              {targetToken.symbol}
            </div>
          </Button>
        </div>
        <div className="bl-flex bl-items-center bl-justify-between bl-gap-4">
          <div className="bl-text-4xl bl-text-indicator bl-grow">
            <div className="bl-w-full bl-max-w-60 lg:bl-max-w-80 bl-overflow-hidden">
              {isAmountValid && lpAmount
                ? formatUnits(lpAmount, targetToken.decimals, {
                    keepDecimals: 8,
                    trimZeros: true,
                  })
                : '--'}
            </div>
          </div>
        </div>
        <div className="bl-flex bl-items-center bl-justify-between">
          <div></div>
          <div className="bl-flex bl-gap-2">
            <span>
              {targetBalance ? targetBalance.formatted : '--'} {targetToken.symbol}
            </span>
          </div>
        </div>
      </div>
      <div className="bl-pt-4 lg:bl-pt-5">{renderActionButton()}</div>
    </div>
  );
}
DepositForm.displayName = 'DepositForm';
