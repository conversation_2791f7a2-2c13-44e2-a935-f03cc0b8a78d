import React from 'react';
import { SUIShape } from '@/components/icons/coins/SUIIcon';
import { cn, toPercent } from '@/lib/utils';
import { BtcfiV2Project } from '@/modules/btcfi/types';
import { AVAXShape } from '@/components/icons/coins/AVAXIcon';
import BitlayerLogo from '@/components/icons/BitlayerLogo';
import { DynamicIcon } from '@/components/ui/icon';
import { Link } from '@/components/i18n/link';
import { useTranslation } from 'react-i18next';
import { chainMap } from '@/wallets/config/chains';
import { staticAsset } from '@/lib/static';

type ProjectCardProps = {
  className?: string;
  project: BtcfiV2Project;
};

const backgroundShapes: Record<string, () => React.ReactNode> = {
  sui: () => {
    return (
      <SUIShape className="bl-w-[202px] bl-h-[258px] bl-absolute bl-bottom-[-30px] bl-right-[-53px] bl-opacity-5" />
    );
  },
  avalanche: () => {
    return (
      <AVAXShape className="bl-size-[194px] bl-absolute bl-bottom-[-13px] bl-right-[-51px] bl-opacity-5" />
    );
  },
  bitlayer: () => {
    return (
      <BitlayerLogo className="bl-w-[213px] bl-h-[194px] bl-absolute bl-bottom-[-13px] bl-right-[-74px] bl-opacity-5" />
    );
  },
};

export function ProjectCard({ className, project }: ProjectCardProps) {
  const { t } = useTranslation();
  const badge =
    project.tag === 'btr' ? (
      <ProjectCardBadge variant="primary">{t('pages.btcfi.btrAllowance')}</ProjectCardBadge>
    ) : project.tag === 'coming' ? (
      <ProjectCardBadge variant="secondary">{t('pages.btcfi.comingSoon')}</ProjectCardBadge>
    ) : null;

  const chain = project.chain?.toLowerCase().split('_')[0] || '';
  const backgroundShape = backgroundShapes[chain]?.();

  if (chain !== 'bitlayer') {
    return <ChainProjectCard className={className} project={project} />;
  }

  return (
    <ProjectCardBase
      id={project.id}
      name={project.name}
      icon={project.icon}
      apr={project.apy === undefined ? '--' : toPercent(project.apy)}
      badge={badge}
      backgroundShape={backgroundShape}
      disabled={project.tag === 'coming'}
      className={className}
    />
  );
}

const chainWhiteLogos: Record<string, string> = {
  sui: staticAsset('/images/btcfi/sui-white.2d5787d3b5.svg'),
  avalanche: staticAsset('/images/btcfi/avax-white.fe0aab7653.svg'),
  plume: staticAsset('/images/btcfi/plume-white.a5b96aa58a.svg'),
};

function ChainProjectCard({ className, project }: ProjectCardProps) {
  const { t } = useTranslation();

  const isComing = project.tag === 'coming';
  const disabled = isComing;
  const apr = project.apy === undefined ? '--' : toPercent(project.apy);
  const chain = chainMap[project.chain];
  const chainPrefix = project.chain.split('_')[0];

  const Comp = disabled ? 'div' : Link;
  return (
    <Comp
      to={`/btcfi-v2/projects/${project.id}`}
      className={cn(
        'bl-relative bl-w-[289px] bl-h-[134px] bl-p-5 bl-border bl-bg-gradient-to-b bl-overflow-hidden bl-duration-200 lg:bl-w-full lg:bl-h-[168px] lg:bl-p-8',
        {
          'bl-border-[#4DA2FF]/70 bl-from-[rgba(46,97,153,0.20)] bl-to-[rgba(77,162,255,0.20)] hover:bl-border-[#4da2ff]':
            chainPrefix === 'sui',
          'bl-border-[#E84142]/70 bl-from-[rgba(52,52,52,0.15)] bl-to-[rgba(232,65,66,0.15)] hover:bl-border-[#e84142]':
            chainPrefix === 'avalanche',
          'bl-border-[#FF3D00]/40 bl-from-[rgba(52,52,52,0.15)] bl-to-[rgba(255,61,0,0.15)] hover:bl-border-[#FF3D00]':
            chainPrefix === 'plume',
        },
        className,
      )}
    >
      <div className="bl-flex bl-items-center bl-gap-4 bl-size-full bl-relative lg:bl-gap-5">
        <img
          src={chainWhiteLogos[chainPrefix]}
          className="bl-size-14 bl-min-w-0 bl-shrink-0 lg:bl-size-18"
          alt="chain"
        />
        <div className="bl-space-y-2">
          <div className="bl-text-white bl-text-base lg:bl-text-xl">{project.name}</div>
          <div className="bl-flex bl-gap-3.5 bl-text-2xl lg:bl-text-3xl">
            <div className="bl-text-secondary">{t('pages.btcfi.apy')}</div>
            <div className="bl-font-title bl-text-primary">{apr === undefined ? '--' : apr}</div>
          </div>
        </div>
      </div>
      <div className="bl-absolute bl-flex bl-right-3 bl-top-2.5 lg:bl-top-3 lg:bl-right-3.5">
        <DynamicIcon icon={chain.icon} className="bl-size-6 lg:bl-size-8" />
        <DynamicIcon icon={project.icon} className="bl-size-6 -bl-ml-1 lg:bl-size-8 lg:-bl-ml-2" />
      </div>
      {isComing && (
        <ProjectCardBadge variant="secondary" className="bl-left-0 bl-top-0 bl-w-fit">
          {t('pages.btcfi.comingSoon')}
        </ProjectCardBadge>
      )}
    </Comp>
  );
}

type ProjectCardBaseProps = {
  id: number;
  name: string;
  icon: React.FC<React.SVGProps<SVGSVGElement>> | string;
  apr?: string;
  badge?: React.ReactNode;
  backgroundShape?: React.ReactNode;
  disabled?: boolean;
  className?: string;
};

export function ProjectCardBase({
  id,
  name,
  icon,
  apr,
  badge,
  backgroundShape,
  disabled,
  className,
}: ProjectCardBaseProps) {
  const { t } = useTranslation();
  const Comp = disabled ? 'div' : Link;
  return (
    <Comp
      to={`/btcfi-v2/projects/${id}`}
      className={cn(
        'bl-relative bl-w-[289px] bl-h-[134px] bl-p-5 bl-border bl-border-card-border bl-bg-gradient-to-b bl-from-[#242424] bl-to-black bl-overflow-hidden bl-duration-200 lg:bl-w-full lg:bl-h-[168px] lg:bl-p-8 hover:bl-border-primary',
        {
          'hover:bl-border-card-border': disabled,
        },
        className,
      )}
    >
      <div className="bl-flex bl-flex-col bl-justify-between bl-h-full">
        <div className="bl-flex bl-items-center bl-gap-4">
          <DynamicIcon icon={icon} className="bl-size-8 lg:bl-size-10" />
          <span className="bl-text-white bl-text-xl lg:bl-text-2xl">{name}</span>
        </div>
        <div className="bl-text-white bl-flex bl-gap-3.5 bl-text-xl lg:bl-text-2xl">
          <div>{t('pages.btcfi.apy')}</div>
          <div className="bl-font-title bl-text-primary">{apr === undefined ? '--' : apr}</div>
        </div>
      </div>
      {badge}
      {backgroundShape}
    </Comp>
  );
}

type ProjectCardBadgeProps = {
  variant?: 'primary' | 'secondary';
  children?: React.ReactNode;
  className?: string;
};

export function ProjectCardBadge({ children, variant, className }: ProjectCardBadgeProps) {
  return (
    <div
      className={cn(
        'bl-h-[18px] lg:bl-h-[22px] bl-px-2 bl-bg-primary/30 bl-text-primary bl-absolute bl-top-3 bl-right-3 bl-text-xs/[18px] lg:bl-text-base/[22px]',
        {
          'bl-bg-secondary/30 bl-text-secondary': variant === 'secondary',
        },
        className,
      )}
    >
      {children}
    </div>
  );
}
