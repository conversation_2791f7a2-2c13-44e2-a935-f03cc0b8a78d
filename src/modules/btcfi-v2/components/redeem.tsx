import { S<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, WalletConnector } from '@/components/featured/wallet';
import { Button } from '@/components/ui/button';
import { AmountInput } from '@/components/ui/form-field';
import { DynamicIcon } from '@/components/ui/icon';
import { useAccount } from '@/hooks/wallet/account';
import { useWalletBalance } from '@/hooks/wallet/balance';
import { BaseChainType, Token } from '@/wallets/config/type';
import { ArrowDownIcon, ClockIcon, LoaderIcon, TriangleAlertIcon } from 'lucide-react';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Address, parseUnits } from 'viem';
import { loadUserProjectWithdraws, useClaim, useRedeem } from '../hooks/redeem';
import { useToast } from '@/hooks/toast';
import { ReadableError, UserRejectedError } from '@/modules/bridge/transactions/errors';
import { ButtonSelectTrigger, Select, SelectContent, SelectItem } from '@/components/ui/select';
import { BtcfiV2CustodyContractType, BtcfiV2UserProjectWithdrawItem } from '@/modules/btcfi/types';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useParams, useRevalidator } from '@remix-run/react';
import { cn, formatUnits } from '@/lib/utils';
import { useCountDown } from 'ahooks';
import { useFromLp } from '../hooks/price';
import { format } from 'date-fns';

const StartTime = *************;

export type RedeemFormProps = {
  chain: BaseChainType;
  sourceToken: Token;
  targetTokens: Token[];
  contract: Address;
  contractType?: BtcfiV2CustodyContractType;
};

export function RedeemForm({
  sourceToken,
  targetTokens,
  chain,
  contract,
  contractType = 'YBTC.B',
}: RedeemFormProps) {
  const { id: projectId } = useParams();
  const { address, chain: currentChain } = useAccount({ network: chain.networkType });
  const { t } = useTranslation();
  const { toast } = useToast();
  const [amount, setAmount] = useState('');
  const [targetTokenIndex, setTargetTokenIndex] = useState(0);
  const { revalidate } = useRevalidator();

  if (contractType === 'btcfi-v2') {
    targetTokens = [targetTokens[0]];
  }

  const targetToken = targetTokens[targetTokenIndex];

  const { data: balance, refetch: refetchSource } = useWalletBalance({
    network: chain.networkType,
    address,
    token: sourceToken,
    chain,
  });

  const { data: targetBalance, refetch: refetchTarget } = useWalletBalance({
    network: chain.networkType,
    address,
    token: targetToken,
    chain,
  });

  const { redeemAsync, isPending } = useRedeem();

  const { data: redeemData } = useQuery({
    queryKey: ['btcfi-v2/redeems', Number(projectId), address as Address],
    queryFn: loadUserProjectWithdraws,
    enabled: !!address && contractType === 'btcfi-v2',
  });

  const amountValidator = (value: string) => {
    try {
      parseUnits(value, sourceToken.decimals);
    } catch (error) {
      return false;
    }

    const [, decimal] = value.split('.');
    if (decimal && decimal.length > sourceToken.decimals) {
      return false;
    }

    return !isNaN(Number(value)) && Number(value) >= 0;
  };

  const handleClickMax = () => {
    if (balance) {
      setAmount(balance.formatted);
    }
  };

  const [isAmountValid, amountTips] = useMemo(() => {
    if (!amount || !balance) {
      return [false, t('common.confirm')];
    }

    let bigAmount = 0n;
    try {
      bigAmount = parseUnits(amount, sourceToken.decimals);
    } catch (error) {
      return [false, t('pages.bridge.invalidAmount')];
    }

    if (bigAmount === 0n) {
      return [false, t('pages.bridge.invalidAmount')];
    }

    if (bigAmount > balance.value) {
      return [false, t('common.insufficientBalance')];
    }

    return [true, ''];
  }, [amount, balance, sourceToken.decimals, t]);

  const tokenAmount = useFromLp({
    token: targetToken,
    lp: sourceToken,
    amount,
    contract,
    contractType,
  });

  const handleRedeem = async () => {
    if (!isAmountValid) {
      return;
    }

    try {
      await redeemAsync({
        address: address as Address,
        token: sourceToken,
        amount: parseUnits(amount, sourceToken.decimals),
        contract: contract,
        contractType,
        projectId: Number(projectId),
      });
      toast(t('pages.btcfi.custody.redeemSuccess'));
    } catch (e) {
      if (e instanceof UserRejectedError) {
        return;
      }

      if (e instanceof ReadableError) {
        toast(t(`pages.bridge.errors.${e.message}`));
      } else {
        toast(t('pages.btcfi.custody.redeemFailed'));
      }
      return;
    }

    refetchSource();
    refetchTarget();
    setAmount('');
    revalidate();
  };

  const isStart = () => {
    const now = new Date().getTime();
    return StartTime <= now;
  };

  const renderActionButton = () => {
    if (contractType === 'YBTC.B' && !isStart()) {
      return (
        <Button variant="secondary" className="bl-w-full" disabled>
          <span>Available on 8/14 9:00 UTC</span>
        </Button>
      );
    }

    if (!address || !currentChain) {
      return (
        <WalletConnector chain={chain}>
          <Button className="bl-w-full" overlayFrom="none">
            {t('common.connect')}
          </Button>
        </WalletConnector>
      );
    }

    if (currentChain.id !== chain.id) {
      return <SwitchChainButton chain={chain} className="bl-w-full" overlayFrom="none" />;
    }

    if (isPending) {
      return (
        <Button className="bl-w-full bl-gap-1" overlayFrom="none" disabled>
          <LoaderIcon className="bl-size-6 bl-animate-spin" />
          <span>{t('common.pending')}</span>
        </Button>
      );
    }

    if (!isAmountValid) {
      return (
        <Button className="bl-w-full" overlayFrom="none" disabled>
          <span>{amountTips}</span>
        </Button>
      );
    }

    if (!tokenAmount) {
      return (
        <Button className="bl-w-full" overlayFrom="none" disabled>
          <span>{t('pages.bridge.invalidAmount')}</span>
        </Button>
      );
    }

    return (
      <Button overlayFrom="none" className="bl-w-full" onClick={handleRedeem}>
        <span>{t('common.confirm')}</span>
      </Button>
    );
  };

  return (
    <div className="bl-space-y-3">
      <div className="bl-border bl-border-card-border bl-p-5 bl-space-y-3.5 bl-bg-black bl-relative">
        <div className="bl-flex bl-items-center bl-justify-between">
          <div>{t('pages.btcfi.custody.redeem')}</div>
          <Button variant="outline-3" className="bl-w-40 disabled:bl-opacity-100" disabled>
            <div className="bl-flex bl-items-center bl-gap-2">
              <DynamicIcon icon={sourceToken.icon} className="bl-size-5" />
              {sourceToken.symbol}
            </div>
          </Button>
        </div>
        <div className="bl-flex bl-items-center bl-justify-between">
          <AmountInput
            value={amount}
            onChange={setAmount}
            validator={amountValidator}
            className="bl-w-full"
            placeholder="0.00"
          />
        </div>
        <div className="bl-flex bl-items-center bl-justify-between">
          <div></div>
          <div className="bl-flex bl-gap-2">
            <span>
              {balance ? balance.formatted : '--'} {sourceToken.symbol}
            </span>
            <button className="bl-text-primary" onClick={handleClickMax}>
              {t('pages.btcfi.custody.max')}
            </button>
          </div>
        </div>
        <div className="bl-absolute -bl-bottom-7 lg:-bl-bottom-8 bl-left-1/2 -bl-translate-x-1/2">
          <div className="bl-size-[42px] lg:bl-size-12 bl-rounded-full bl-bg-primary bl-text-black bl-flex bl-items-center bl-justify-center">
            <ArrowDownIcon className="bl-size-6" />
          </div>
        </div>
      </div>
      <div className="bl-border bl-border-card-border bl-p-5 bl-space-y-3.5 bl-bg-black">
        <div className="bl-flex bl-items-center bl-justify-between">
          <div>{t('pages.btcfi.custody.get')}</div>
          {targetTokens.length > 1 ? (
            <Select onValueChange={(v) => setTargetTokenIndex(Number(v))}>
              <ButtonSelectTrigger buttonSize="md" className="bl-w-40 bl-px-2">
                <div className="bl-flex bl-items-center bl-gap-2">
                  <DynamicIcon icon={targetToken.icon} className="bl-size-5" />
                  {targetToken.symbol}
                </div>
              </ButtonSelectTrigger>
              <SelectContent>
                {targetTokens.map((token, index) => (
                  <SelectItem key={index} value={index.toString()}>
                    <div className="bl-flex bl-items-center bl-gap-2">
                      <DynamicIcon icon={token.icon} className="bl-size-5" />
                      {token.symbol}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <Button variant="outline-3" className="bl-w-40">
              <div className="bl-flex bl-items-center bl-gap-2">
                <DynamicIcon icon={targetToken.icon} className="bl-size-5" />
                {targetToken.symbol}
              </div>
            </Button>
          )}
        </div>
        <div className="bl-flex bl-items-center bl-justify-between bl-gap-4">
          <div className="bl-text-4xl bl-text-indicator bl-grow">
            <div className="bl-w-full bl-max-w-60 lg:bl-max-w-80 bl-overflow-hidden bl-text-ellipsis">
              {isAmountValid && tokenAmount
                ? formatUnits(tokenAmount, targetToken.decimals, {
                    keepDecimals: 8,
                    trimZeros: true,
                  })
                : '--'}
            </div>
          </div>
        </div>
        <div className="bl-flex bl-items-center bl-justify-between">
          <div></div>
          <div className="bl-flex bl-gap-2">
            <span>
              {targetBalance ? targetBalance.formatted : '--'} {targetToken.symbol}
            </span>
          </div>
        </div>
      </div>
      <div className="bl-pt-4 lg:bl-pt-5">{renderActionButton()}</div>

      {redeemData && redeemData.length > 0 && (
        <div className="bl-space-y-3 bl-pt-2">
          <div className="bl-text-xs">
            <TriangleAlertIcon className="bl-size-4 bl-inline-block bl-mr-1" />
            {t('pages.btcfi.custody.redeemNotice', {
              time: format(new Date(redeemData[0].expireAt), 'yyyy-MM-dd HH:mm'),
            })}
          </div>
          <div className="bl-space-y-2">
            {redeemData.map((item, index) => (
              <ClaimableItem
                item={item}
                key={index}
                refetchBalance={refetchTarget}
                contract={contract}
                address={address as Address}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
RedeemForm.displayName = 'RedeemForm';

type ClaimableItemProps = {
  item: BtcfiV2UserProjectWithdrawItem;
  refetchBalance: () => void;
  contract: Address;
  address: Address;
};

function ClaimableItem({ item, refetchBalance, contract, address }: ClaimableItemProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { id: projectId } = useParams();
  const [countdown, formatted] = useCountDown({
    leftTime: item.duration,
  });

  const { claimAsync, isPending } = useClaim();

  const handleClaim = async () => {
    try {
      await claimAsync({
        contract: contract,
        queryClient,
        projectId: Number(projectId),
        address,
      });
      toast(t('pages.btcfi.custody.claimSuccess'));
    } catch (e) {
      if (e instanceof UserRejectedError) {
        return;
      }

      if (e instanceof ReadableError) {
        toast(t(`pages.bridge.errors.${e.message}`));
      } else {
        toast(t('pages.btcfi.custody.claimFailed'));
      }
      return;
    }

    refetchBalance();
  };

  const renderActionButton = () => {
    const className = 'bl-h-6 bl-w-28';

    if (countdown > 0) {
      return (
        <Button
          size="xs"
          variant="secondary"
          disabled
          className={cn(className, `bl-w-32 bl-justify-start disabled:bl-opacity-100`)}
        >
          <span className="bl-flex bl-items-center bl-gap-1">
            <ClockIcon className="bl-size-4" />
            {formatted.days > 0 ? (
              <>
                {formatted.days}d:
                {formatted.hours.toString().padStart(2, '0')}h:
                {formatted.minutes.toString().padStart(2, '0')}m
              </>
            ) : (
              <>
                {formatted.hours.toString().padStart(2, '0')}h:
                {formatted.minutes.toString().padStart(2, '0')}m:
                {formatted.seconds.toString().padStart(2, '0')}s
              </>
            )}
          </span>
        </Button>
      );
    }

    if (isPending) {
      return (
        <Button size="xs" overlayFrom="none" disabled className={className}>
          <span className="bl-flex bl-items-center bl-gap-1">
            <LoaderIcon className="bl-size-4 bl-animate-spin" />
            {t('common.pending')}
          </span>
        </Button>
      );
    }

    return (
      <Button size="xs" overlayFrom="none" className={className} onClick={handleClaim}>
        Claim
      </Button>
    );
  };

  return (
    <div className="bl-h-10 bl-w-full bl-flex bl-justify-between bl-items-center bl-bg-[#1d1d1d] bl-px-2.5">
      <div className="bl-flex bl-items-center bl-gap-1">
        <DynamicIcon icon={item.token.icon} className="bl-size-4" />
        <div className="bl-text-white bl-text-sm">
          {formatUnits(BigInt(item.amount), item.token.decimals)} {item.token.symbol}
        </div>
      </div>
      {renderActionButton()}
    </div>
  );
}
