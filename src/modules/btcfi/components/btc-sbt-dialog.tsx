import {
  Dialog,
  DialogCloseRounded,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { chain as BitlayerNetwork } from '@/modules/user-center/config';
import { useToast } from '@/hooks/toast';
import { useAccount } from '@/hooks/wallet/account';

import {
  estimateFeesPerGas,
  estimateGas,
  waitForTransactionReceipt,
  writeContract,
} from '@wagmi/core';
import { Address, encodeFunctionData } from 'viem';
import { useState } from 'react';
import { bitlayerMainnetChain, bitlayerTestnetChain } from '@/wallets/config/chains';
import { useBalance, useConfig, useReadContract } from 'wagmi';
import { abi } from '@/wallets/abi/btcSbt';
import { incrBy } from '@/lib/utils';
import { BtcDialog } from '@/modules/mining-gala/components/btc-dialog';
import { ActionButton } from '@/modules/btcfi/components/ActionButton';
import { isBinanceMobile } from '@/wallets/evm/connectors/binance.client';

export interface SbtDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  handleMintClick: () => void;
}

const swapContractAddress: Record<number, Address> = {
  [bitlayerMainnetChain.id]: '******************************************',
  [bitlayerTestnetChain.id]: '******************************************',
};

export function BtcSbtDialog({ open, onOpenChange, handleMintClick }: SbtDialogProps) {
  const { t } = useTranslation();
  const key = 'pages.btcfi.sbt';
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { address, driver } = useAccount({ network: BitlayerNetwork.networkType });
  const [noBalance, setNoBalance] = useState(false);
  const config = useConfig();
  const { data: btcBalance } = useBalance({
    address: address as `0x${string}`,
    config,
    chainId: BitlayerNetwork.chain.id,
  });

  const contract = swapContractAddress[BitlayerNetwork.chain.id];

  const { data: isMinted, refetch: refetchMinted } = useReadContract({
    abi: abi,
    address: contract,
    functionName: 'isMinted',
    args: [address],
  });

  const handleClick = () => {
    const link = isBinanceMobile(driver!)
      ? 'bnc://app.binance.com/mp/app?appId=xoqXxUSMRccLCrZNRebmzj&startPagePath=L3BhZ2VzL3N1YnBhY2thZ2VzL2Rpc2NvdmVyL2NhbXBhaWduLXdlYnZpZXcvaW5kZXg=&startPageQuery=dXJsPWh0dHBzOi8vd3d3LmJpbmFuY2UuY29tL2VuL3dlYjMtY2FtcGFpZ24vYWlyZHJvcC80MjcyNDY3MTU3MTMwODYwNTQ0&showOptions=2'
      : 'https://www.binance.com/en/web3-campaign/airdrop/4272467157130860544';
    if (!isMinted) {
      handleMintClick();
    } else {
      window.open(link, '_black');
    }
  };

  const handleMint = async () => {
    setIsLoading(true);
    const feesPerGas = await estimateFeesPerGas(config);
    const perGas = Number(feesPerGas.maxFeePerGas) * 50000;
    const _isNotBalance = Number(perGas) > Number(btcBalance?.value);
    if (_isNotBalance) {
      setNoBalance(true);
    }
    const args = [] as const;
    const gas = await estimateGas(config, {
      data: encodeFunctionData({
        abi,
        functionName: 'mint',
        args,
      }),
      to: contract,
      account: address as Address,
    });
    try {
      const tx = await writeContract(config, {
        abi,
        address: contract,
        chainId: BitlayerNetwork.chain.id,
        functionName: 'mint',
        args,
        maxFeePerGas: incrBy(feesPerGas.maxFeePerGas, 0.5),
        gas: incrBy(gas, 0.2),
        maxPriorityFeePerGas: incrBy(feesPerGas.maxPriorityFeePerGas, 0.5),
      });

      await waitForTransactionReceipt(config, {
        hash: tx,
        pollingInterval: 5_000,
      });
      await refetchMinted();
      setIsLoading(false);
      onOpenChange?.(false);
      toast('Mint successful');
    } catch (error) {
      setIsLoading(false);
      // onOpenChange?.(false);
      toast('Blockchain transaction failed');
      console.error(error);
    }
  };

  const cancel = () => {
    onOpenChange(false);
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent
          className="bl-py-7 md:bl-w-[534px] bl-bg-primary bl-px-7 lg:bl-px-10 lg:bl-border bl-border-primary lg:bl-bg-primary"
          overlayClassName="bl-blur"
        >
          <DialogHeader className="bl-relative">
            <DialogTitle>
              <span className="bl-text-black bl-uppercase">
                {isMinted ? t(`${key}.hasMinted`) : t(`${key}.canMint`)}
              </span>
            </DialogTitle>
            <DialogCloseRounded className="bl-border-[#B65713] bl-text-black hover:bl-border-primary bl-absolute bl--top-6 md:bl--top-4 bl-right-0" />
          </DialogHeader>

          {isLoading ? (
            <div className="bl-text-red-600 bl-text-center bl-text-lg">{t(`${key}.waiting`)}</div>
          ) : (
            <div className="bl-flex md:bl-flex-row bl-flex-col bl-gap-4 bl-px-[80px]">
              <Button
                size="sm"
                onClick={handleMint}
                className="bl-w-30 bl-gap-2 bl-bg-black bl-text-white hover:bl-text-primary"
                disabled={isMinted == true}
                overlayFrom="none"
              >
                <div> {isMinted ? t(`${key}.minted`) : t(`${key}.mint`)}</div>
              </Button>
              <Button
                size="sm"
                onClick={cancel}
                className="bl-w-30 bl-gap-2 bl-bg-black bl-text-white hover:bl-text-primary"
                overlayFrom="none"
              >
                <div>{t(`${key}.cancel`)}</div>
              </Button>
            </div>
          )}
        </DialogContent>
      </Dialog>
      <ActionButton disabled={!!isMinted} isLoading={isLoading} onClick={handleClick}>
        {isMinted ? 'Minted' : 'Mint'}
      </ActionButton>
      <BtcDialog open={noBalance} flashOnly onOpenChange={setNoBalance} />
    </>
  );
}
