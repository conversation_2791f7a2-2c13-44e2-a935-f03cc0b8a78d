import { Address } from 'viem';

export interface XIntent {
  type: string;
  params: Record<string, string>;
}

export interface TaskAction {
  type: string;
  payload?: {
    path?: string;
    timeOnPage?: number;
    isBind?: boolean;
    template?: string;
    intent?: XIntent;
    contract_addr?: `0x${string}` | null | undefined;
    project_id?: number;
    task_name?: string;
    progress_cfg?: { key: number; value: number }[];
    sub_title?: string;
  };
}

export type TaskItemProps = {
  title: string;
  rewardPoints: 0;
  action: TaskAction;
  logoType: string;
  isCompleted: boolean;
  completedCount: number;
  targetCount: number;
  taskId: number;
  rank: number;
  mainTitle: string;
  canClaim: boolean;
  taskType: number;
  extraData: {
    cur_claimed_progress?: number;
    rewards: string;
    cur_done_progress?: number;
    interrupted_at?: number;
    is_tx_pending?: boolean;
    today_progress: string;
    rechecked_in_days: string;
    max_query_timestamp?: number;
    min_query_timestamp?: number;
    my_rewards?: number;
    total_rewards_issued?: number;
    missing_days?: string;
    red_point?: boolean;
    activity_ended?: boolean;
  };
};

export interface BtcFiTaskResponse {
  btcfiTasks: TaskItemProps[];
}

export interface DailyTaskResponse {
  taskInfo: TaskItemProps;
}

type InfoType = {
  timestamp: number;
  status: 'future_check' | 'miss_check' | 'cur_check' | 'checked' | 'cur_check';
  rewardAmount: number;
};

export interface RecheckResponse {
  isTxPending?: boolean;
  reCheckDays?: string;
  infos?: InfoType[];
  reCheckReward?: string;
}

export interface DailyResponse {
  claimablePoints: string;
}

export type WinnerItemProps = {
  address: string;
  rewardAmount: string;
  rewardLevel: string;
};

export interface LotteryResponse {
  timestamp: string;
  winners: WinnerItemProps[];
}

export interface InviteCodeResponse {
  inviteCode: string;
}

export interface InviteInfoResponse {
  claimableRewardAmount: number;
  hasCheckedIn: boolean;
  inviteeNum: number;
  maxInviteeNum: number;
  totalRewardFromInvitee: number;
}

export type InviteItem = {
  invitee_address: string;
  has_signed_in: boolean;
  has_checked_in: boolean;
  reward_for_inviter: number;
};

export interface InviteListResponse {
  currentPage: number;
  inviteeList: InviteItem[];
}

export interface HelmetResponse {
  holdNum: number;
  claimableNum: number;
  btrPerNft: number;
  weekNum: number;
}

// BtcfiV2 related types

export interface Range {
  min: number;
  max: number;
}

export interface BtcfiV2Project_Links {
  stake?: string;
  unstake?: string;
  claim?: string;
  bridge?: string;
  project?: string;
}

export interface BtcfiV2CustodyToken {
  id: string;
  name: string;
  symbol: string;
  decimals: number;
  icon: string;
  address: Address;
  chain: string;
  type: 'native' | 'erc20';
}

export type BtcfiV2CustodyContractType = 'YBTC.B' | 'btcfi-v2';

export interface BtcfiV2SelfCustody {
  staked: BtcfiV2CustodyToken[];
  receipt: BtcfiV2CustodyToken;
  contract: Address;
  contractType: BtcfiV2CustodyContractType;
}

export interface BtcfiV2ThirdPartyCustody {
  links: BtcfiV2Project_Links;
  staked: BtcfiV2CustodyToken;
  receipt: BtcfiV2CustodyToken;
}

export interface BtcfiV2Custody {
  self?: BtcfiV2SelfCustody;
  thirdParty?: BtcfiV2ThirdPartyCustody;
}

export interface BtcfiV2Project {
  id: number;
  name: string;
  icon: string;
  description: string;
  banner?: string;
  apy?: number;
  custody: BtcfiV2Custody;
  chain: string;
  links?: BtcfiV2Project_Links;
  tvl?: string;
  tag?: 'btr' | 'coming' | 'btc';
}

export type BtcfiV2ListResponse<T> = {
  list: T[];
};

export type BtcfiV2APYHistoryResponse = BtcfiV2ListResponse<BtcfiV2APYHistoryItem>;

export interface BtcfiV2APYHistoryItem {
  timestamp: number;
  apy: number;
}

export interface BtcfiV2Vault {
  id: number;
  name: string;
  symbol: string;
  icon: string;
  description: string;
  apy: Range;
  tvl: string;
  projects: BtcfiV2Project[];
}

export interface BtcfiV2HeadlineResponse {
  tvl: number;
  apy: Range;
  recommend: BtcfiV2Project[];
}

export type BtcfiV2VaultsResponse = BtcfiV2ListResponse<BtcfiV2Vault>;

export interface BtcfiV2ProjectInfoResponse {
  project: BtcfiV2Project;
}

export interface BtcfiV2UserProjectRewardResponse {
  reward: number;
}

export interface BtcfiV2UserProjectWithdrawItem {
  amount: string;
  expireAt: number;
  duration: number;
  token: BtcfiV2CustodyToken;
}

export type BtcfiV2UserProjectWithdrawsResponse =
  BtcfiV2ListResponse<BtcfiV2UserProjectWithdrawItem>;
