import SquareArrowOutUpRightIcon from '@/components/icons/SquareArrowOutUpRightIcon';
import { SectionBadge } from '@/components/ui/section-badge';
import { Token } from '@/wallets/config/type';
import { useParams } from '@remix-run/react';
import { useTranslation } from 'react-i18next';

type TokenMatcher = (token?: Token) => boolean;

interface ThirdPartyBridge {
  href: string;
  logo: string;
  title: string;
  desc: string;
  matcher: TokenMatcher;
}

const thirdBirdges: ThirdPartyBridge[] = [
  {
    href: 'https://meson.fi/',
    logo: '/images/meson.svg',
    title: 'Meson',
    desc: 'common.MesonDes',
    matcher: (token?: Token) => token?.type === 'erc20' || token?.type === 'native',
  },
  // {
  //   href: 'https://owlto.finance/',
  //   logo: '/images/owlto.svg',
  //   title: 'Owlto',
  //   desc: 'common.OwltoDes',
  //   matcher: (token?: Token) => token?.type === 'erc20' || token?.type === 'native',
  // },
  {
    href: 'https://www.orbiter.finance/',
    logo: '/images/orbiter.svg',
    title: 'Orbiter',
    desc: 'common.OrbiterDes',
    matcher: (token?: Token) => token?.type === 'erc20' || token?.type === 'native',
  },
  {
    href: 'https://boolbridge.com/?network=bitlayer',
    logo: '/images/bool-bridge.png',
    title: 'Bool',
    desc: 'common.BoolBridgeDes',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    matcher: (token?: Token) => true,
  },
  {
    href: 'https://bridge.omnity.network/?sourceChain=Bitcoin&targetChain=Bitlayer',
    logo: '/images/omnity-bridge.svg',
    title: 'Omnity',
    desc: 'common.OmnityBridgeDes',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    matcher: (token?: Token) => true,
  },
  {
    href: 'https://minibridge.chaineye.tools/?dst=bitlayer&mode=swap&src=zksyncera',
    logo: '/images/mini-bridge.jpg',
    title: 'MiniBridge',
    desc: 'common.miniBridgeDes',
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    matcher: (token?: Token) => token?.type === 'erc20' || token?.type === 'native',
  },
];

export const ThirdPartyBridge = ({ token }: { token?: Token }) => {
  const params = useParams();
  const { t } = useTranslation();
  if (params.network === 'testnet') {
    return null;
  }
  return (
    <div
      id="third-party-bridge"
      className="bl-w-full md:bl-w-[600px] bl-flex bl-flex-col bl-items-center bl-pt-11"
    >
      <SectionBadge className="bl-text-xl/none bl-h-[42px]">
        {t('pages.bridge.ThirdPartyBridge')}
      </SectionBadge>
      <div className="bl-w-full bl-grid bl-grid-cols-3 md:bl-border-card-border bl-mt-9 bl-gap-2 md:bl-gap-4">
        {thirdBirdges
          .filter((item) => item.matcher(token))
          .map((item, index) => (
            <a
              key={index}
              href={item.href}
              target="_blank"
              rel="noreferrer"
              className="bl-w-full bl-border bl-break-all bl-border-card-border bl-p-4 md:bl-p-5 bl-flex bl-flex-col bl-gap-4 bl-items-center bl-duration-200 hover:bl-border-primary"
            >
              <img
                src={item.logo}
                alt={item.title}
                className="bl-min-0 bl-shrink-0 bl-w-[35px] bl-h-[35px]"
              />
              <div className="bl-grow">
                <div className="bl-text-primary bl-text-center">{item.title}</div>
                <div className="bl-text-xs">{t(item.desc)}</div>
              </div>
              <div className="bl-w-full bl-flex bl-justify-end">
                <SquareArrowOutUpRightIcon className="bl-size-6 bl-min-0 bl-shrink-0" />
              </div>
            </a>
          ))}
      </div>
    </div>
  );
};
