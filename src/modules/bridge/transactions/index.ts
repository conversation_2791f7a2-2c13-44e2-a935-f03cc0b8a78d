import {
  MaxTransferAmountParams,
  TransferContext,
  TransferContextInit,
  TransferProps,
} from './types';
import {
  buildBtcTransfer,
  buildBrc20Transfer,
  buildRuneTransfer,
  signAndPushPsbt,
  requestConfirmCaution,
  getMaxBtcTransferAmount,
} from './btc.client';
import {
  transfer as transferFromEvmToBtc,
  transferETHViaCCIP,
  approveErc20,
  transferErc20ToBtc,
  transferErc20ViaCCIP,
  ccipLimitCheck,
  getMaxTransferAmount,
  getMaxTransferETHViaCCIPAmount,
} from './evm.client';
import { NetworkType, Token } from '@/wallets/config/type';
import {
  Validator,
  createBRC20Validator,
  createERC20ToBtcValidator,
  createERC20Validator,
  createETHValidator,
  defaultValidator,
} from './validator';
import partial from 'lodash/partial';
import { Address } from 'viem';
import { ResolvedRegister } from '@wagmi/core';

export type { TransferProps } from './types';

export type StepFunction<TokenType extends Token = Token> = (
  context: TransferContext,
  params: TransferProps<TokenType>,
) => Promise<string | undefined>;

export type MaxAmountFunc<TokenType extends Token = Token> = (
  context: TransferContext,
  params: MaxTransferAmountParams<TokenType>,
) => Promise<bigint>;

export class Workflow<TokenType extends Token = Token> {
  protected steps: Array<StepFunction<TokenType>> = [];
  #validator: Validator;

  protected currentStep = 0;

  protected context: TransferContext;

  protected result: string | undefined;

  protected maxAmountFunc: MaxAmountFunc<TokenType> | undefined;

  constructor(
    context: TransferContext,
    steps: StepFunction<TokenType>[],
    validator: Validator | null,
    maxAmountFunc: MaxAmountFunc<TokenType> | undefined,
  ) {
    this.steps = steps;
    this.context = context;
    this.#validator = validator ?? defaultValidator;
    this.maxAmountFunc = maxAmountFunc;
  }

  async next(params: TransferProps<TokenType>) {
    if (this.currentStep >= this.steps.length) {
      return this.result;
    }
    const step = this.steps[this.currentStep];
    this.result = await step(this.context, params);
    this.currentStep++;
    await this.next(params);
  }

  async getMaxTransferAmount(params: MaxTransferAmountParams<TokenType>) {
    if (!this.maxAmountFunc) {
      return params.balance;
    }
    return this.maxAmountFunc(this.context, params);
  }

  get txId() {
    return this.result;
  }

  get validator() {
    return this.#validator;
  }
}

type BuildResult<TokenType extends Token = Token> = {
  steps: StepFunction<TokenType>[];
  validator: Validator | null;
  maxAmountFunc?: MaxAmountFunc;
};

const buildBtcSteps = <TokenType extends Token = Token>(
  token: TokenType,
): BuildResult<TokenType> => {
  let steps: StepFunction<TokenType>[] = [];
  let validator: Validator | null = null;
  let maxAmountFunc: MaxAmountFunc | undefined;
  switch (token.type) {
    case 'rune':
      steps = [
        buildRuneTransfer,
        requestConfirmCaution,
        signAndPushPsbt,
      ] as StepFunction<TokenType>[];
      break;
    case 'brc20':
      steps = [
        buildBrc20Transfer,
        requestConfirmCaution,
        signAndPushPsbt,
      ] as StepFunction<TokenType>[];
      validator = createBRC20Validator();
      break;
    default:
      steps = [buildBtcTransfer, requestConfirmCaution, signAndPushPsbt];
      maxAmountFunc = getMaxBtcTransferAmount as MaxAmountFunc;
      break;
  }
  return { steps, validator, maxAmountFunc };
};

const buildEvmSteps = <TokenType extends Token = Token>(
  context: TransferContext,
  {
    token,
    fromChain,
    toChain,
    from,
  }: Pick<TransferProps<TokenType>, 'from' | 'fromChain' | 'toChain' | 'token'>,
): BuildResult<TokenType> => {
  if (token.id === 'BTC') {
    return { steps: [transferFromEvmToBtc], validator: null, maxAmountFunc: getMaxTransferAmount };
  }

  const validatorCtx = { config: context.config, account: from as Address };

  if (token.type === 'erc20') {
    if (toChain.networkType === NetworkType.evm) {
      // const spender = fromChain.contracts!.zkBridgeLightning.address as Address;
      const spender = fromChain.contracts!.ccip.address as Address;
      const validator = createERC20Validator({ ...validatorCtx, spender });
      return {
        steps: [
          partial(approveErc20, spender),
          ccipLimitCheck,
          transferErc20ViaCCIP,
        ] as StepFunction<TokenType>[],
        validator,
      };
    } else if (toChain.networkType === NetworkType.btc) {
      const spender = fromChain.contracts!.bridge.address as Address;
      const validator = createERC20ToBtcValidator({ ...validatorCtx, spender });
      return {
        steps: [partial(approveErc20, spender), transferErc20ToBtc] as StepFunction<TokenType>[],
        validator,
      };
    }
  } else if (token.id === 'ETH') {
    const validator = createETHValidator(validatorCtx);
    return {
      steps: [ccipLimitCheck, transferETHViaCCIP] as StepFunction<TokenType>[],
      validator,
      maxAmountFunc: getMaxTransferETHViaCCIPAmount as MaxAmountFunc,
    };
  }

  return { steps: [], validator: null };
};

export const buildWorkflow = <TokenType extends Token = Token>(
  params: Pick<TransferProps<TokenType>, 'from' | 'fromChain' | 'toChain' | 'token'>,
  contextInit: TransferContextInit,
): Workflow<TokenType> => {
  const { fromChain, token } = params;
  let steps: StepFunction<TokenType>[] = [];
  let validator: Validator | null = null;
  let maxAmountFunc: MaxAmountFunc | undefined;

  const context: TransferContext = new Context(contextInit);

  if (fromChain.networkType === NetworkType.btc) {
    const result = buildBtcSteps<TokenType>(token);
    steps = result.steps;
    validator = result.validator;
    maxAmountFunc = result.maxAmountFunc;
  } else {
    const result = buildEvmSteps<TokenType>(context, params);
    steps = result.steps;
    validator = result.validator;
    maxAmountFunc = result.maxAmountFunc;
  }

  return new Workflow(context, steps, validator, maxAmountFunc);
};

class Context {
  config: ResolvedRegister['config'];

  protected store: Record<string, unknown> = {};

  constructor({ config }: TransferContextInit) {
    this.config = config;
  }

  set<T>(key: string, value: T) {
    this.store[key] = value;
  }

  get<T>(key: string): T | undefined {
    return this.store[key] as T;
  }
}
