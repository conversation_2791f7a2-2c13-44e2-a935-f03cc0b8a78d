import { BaseChainType, Token } from '@/wallets/config/type';
import type { ResolvedRegister } from '@wagmi/core';
import { BigNumber } from 'ethers';
import { atom } from 'jotai';

export const transferHintAtom = atom<string | undefined>(undefined);

export interface TransferContextInit {
  config: ResolvedRegister['config'];
}

export interface TransferContext extends TransferContextInit {
  set: <T>(v: string, value: T) => void;
  get: <T>(v: string) => T | undefined;
}

export interface TransferProps<TokenType extends Token = Token> {
  from: string; // address on chain A
  to: string; // address on chain B
  fromChain: BaseChainType;
  toChain: BaseChainType;
  amount: BigNumber;
  token: TokenType;
  bridgeAddress: string;
  feeReceiver: string;
  fee: BigNumber;
  payload?: string;
}

export interface MaxTransferAmountParams<TokenType extends Token = Token> {
  from: string;
  to: string; // address on chain B
  fromChain: BaseChainType;
  toChain: BaseChainType;
  token: TokenType;
  fee?: BigNumber;
  balance: bigint;
  min?: bigint;
  max?: bigint;
}

export type TransferStatus = 'pending' | 'completed' | 'failed';

export interface TransactionData {
  from: string;
  to: string;
  fromChain: BaseChainType;
  toChain: BaseChainType;
  token: Token;
  amount: BigNumber;
  fromTxId: string;
  fromTxStatus: TransferStatus;
  toTxId?: string;
  toTxStatus?: TransferStatus;
  status: TransferStatus;
  reason?: string;
  createdTime: number;
  finishedTime?: number;
}

export interface PsbtInput {
  txid: string;
  vout: number;
  type: string;
  sequence: number;
  witnessUtxo?: {
    script: string;
    value: number;
  };
}

export interface BuildPsbtInput {
  address: string;
  amount: number;
  transactionHash: string;
  voutIndex: number;
}

export interface BuildPsbtExtendData {
  txHex: string;
  vinList: BuildPsbtInput[];
}
