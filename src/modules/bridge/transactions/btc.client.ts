import { PSBTBuilder as OriginalPSBTBuilder } from '@sadoprotocol/ordit-sdk';
import * as bitcoin from 'bitcoinjs-lib';
import { toBuffer } from 'ethereumjs-util';
import {
  PsbtInput,
  BuildPsbtExtendData,
  TransferContext,
  TransferProps,
  MaxTransferAmountParams,
} from './types';
import { UserRejectedError } from './errors';
import { toHex } from 'viem';
import { BitlayerDatasource } from './datasource/bitlayer';
import { BuildPsbtParams, getBitcoinAPI } from '@/lib/api/bitcoin.client';
import { BRC20Token, RuneToken } from '@/wallets/config/type';
import { getWalletState, getXverseAddresses } from '@/wallets/bitcoin/state';
import { openBTCCautionDialog } from '../components/btc-caution';
import { WalletDriverName } from '@/hooks/wallet/common';
import { SignAndPushPsbtParams } from '@/wallets/bitcoin/types';

class PSBTBuilder extends OriginalPSBTBuilder {
  // Overwrite the original method to calculate the fee.
  // Because we have a custom PSBT output, we need to make it in count.
  calculateNetworkFee(): number {
    // @ts-expect-error calling private method
    // 2 is op_code and data size
    // 42 is the size of eth address
    // 8 is the size of value
    this.fee = (this.calculateVirtualSize() + 2 + 42 + 8) * this.feeRate;

    // @ts-expect-error calling private method
    this.sanityCheckFee();

    return this.fee;
  }
}

export const buildBtcTransfer = async (
  context: TransferContext,
  { from, to, fromChain, amount, bridgeAddress, fee }: TransferProps,
) => {
  const { connector: wallet } = getWalletState();

  if (!wallet) {
    throw new Error('Bitcoin wallet not found');
  }

  const network = fromChain.testnet ? 'testnet' : 'mainnet';
  const datasource = new BitlayerDatasource({ network });
  const bitcoinApi = getBitcoinAPI({ network });
  try {
    const feeRate = await bitcoinApi.getFeeRate();
    const pubKey = await wallet.getPublicKey('btc');
    const transferAmountAndFee = amount.add(fee).toNumber();
    const psbtBuilder = new PSBTBuilder({
      address: from,
      publicKey: pubKey,
      outputs: [
        {
          address: bridgeAddress,
          value: transferAmountAndFee,
        },
      ],
      network: fromChain.testnet ? 'testnet' : 'mainnet',
      feeRate: feeRate,
      datasource,
    });
    //psbtBuilder.disableRBF();
    await psbtBuilder.prepare(); // calculate inputs

    const psbt = psbtBuilder.toPSBT();
    const addressBuffer = toBuffer(toHex(to));
    const nullData = bitcoin.payments.embed({ data: [addressBuffer] });
    psbt.addOutput({
      script: nullData.output!,
      value: 0,
    });
    const hex = psbt.toHex();
    context.set('hex', hex);
    return undefined;
  } catch (e: unknown) {
    handleTransferError(e);
  }
};

export const getMaxBtcTransferAmount = async (
  context: TransferContext,
  { from, to, fromChain, fee, balance, min, max }: MaxTransferAmountParams,
) => {
  const { connector: wallet } = getWalletState();

  if (!wallet) {
    throw new Error('Bitcoin wallet not found');
  }

  if (!fee) {
    console.warn('Fee is required');
    return balance;
  }

  const network = fromChain.testnet ? 'testnet' : 'mainnet';
  const datasource = new BitlayerDatasource({ network });
  const bitcoinApi = getBitcoinAPI({ network });
  const feeRate = await bitcoinApi.getFeeRate();
  const pubKey = await wallet.getPublicKey('btc');

  const value = min ? Number(min) + fee.toNumber() : fee.toNumber() + 1;

  try {
    const psbtBuilder = new PSBTBuilder({
      address: from,
      publicKey: pubKey,
      outputs: [
        {
          address: from,
          value: value,
        },
      ],
      network: fromChain.testnet ? 'testnet' : 'mainnet',
      feeRate: feeRate,
      datasource,
    });
    await psbtBuilder.prepare(); // calculate inputs

    const networkFee = psbtBuilder.calculateNetworkFee();
    let maxTransferAmount = BigInt(balance - fee.toBigInt() - BigInt(networkFee));

    if (max && maxTransferAmount > max) {
      maxTransferAmount = max;
    }

    // verify
    try {
      const psbtBuilder2 = new PSBTBuilder({
        address: from,
        publicKey: pubKey,
        outputs: [
          {
            address: from,
            value: Number(maxTransferAmount),
          },
        ],
        network: fromChain.testnet ? 'testnet' : 'mainnet',
        feeRate: feeRate,
        datasource,
      });
      await psbtBuilder2.prepare(); // calculate inputs
    } catch (e) {
      console.warn('Failed to verify max transfer amount', e);
      throw e;
    }

    return maxTransferAmount;
  } catch (e) {
    console.error(e);
    return balance;
  }
};

export const signAndPushPsbt = async (
  context: TransferContext,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _props: TransferProps,
) => {
  const hex = context.get<string>('hex');

  if (!hex) {
    throw new Error('Psbt not found');
  }

  const { connector: wallet } = getWalletState();
  if (!wallet) {
    throw new Error('Bitcoin wallet not found');
  }

  try {
    if (wallet.signAndPushPsbt) {
      const psbt = bitcoin.Psbt.fromHex(hex, {
        network: _props.fromChain.testnet ? bitcoin.networks.testnet : bitcoin.networks.bitcoin,
      });
      const params: SignAndPushPsbtParams = { psbt };
      const tokenType = _props.token.type;
      if (tokenType === 'brc20' || tokenType === 'rune') {
        const extendData = context.get<BuildPsbtExtendData>('extendData');
        if (!extendData) {
          throw new Error('ExtendData not found');
        } else {
          Object.assign(params, { inputList: extendData.vinList });
        }
      }

      const { txid } = await wallet.signAndPushPsbt(params);
      return txid;
    }

    const signedPsbt = await wallet.signPsbt(hex);
    const txid = await wallet.pushPsbt(signedPsbt);
    return txid;
  } catch (e: unknown) {
    handleTransferError(e);
  }
};

function handleTransferError(e: unknown) {
  if (e instanceof Error) {
    console.error(e);
    throw e;
  }
  if (typeof e === 'object') {
    const error = e as { code: number; message: string };
    switch (error.code) {
      case 4001:
        throw new UserRejectedError();
      default:
        console.error(e);
        throw new Error(error.message);
    }
  }
}

export const buildRuneTransfer = async (
  context: TransferContext,
  { from, to, token, amount, fromChain, bridgeAddress, feeReceiver, fee }: TransferProps<RuneToken>,
) => {
  const { connector, wallet } = getWalletState();
  if (!wallet) {
    throw new Error('Bitcoin wallet not found');
  }

  const network = fromChain.testnet ? 'testnet' : 'mainnet';
  const bitcoinApi = getBitcoinAPI({ network });

  try {
    const feeRate = await bitcoinApi.getFeeRate();
    const publicKey = await wallet.getPublicKey('brc20');
    const params: BuildPsbtParams = {
      address: from,
      to: bridgeAddress,
      tokenId: token.runeId,
      amount: amount.toString(),
      feeRate,
      network,
      publicKey,
      enableRBF: false,

      bridgeTargetAddress: to,
      bridgeFeeAddress: feeReceiver,
      bridgeFee: fee.toNumber(),
    };

    if (connector?.id === WalletDriverName.Xverse) {
      const { payment } = getXverseAddresses();
      Object.assign(params, {
        payGasAddress: payment.address,
        payGasPublicKey: payment.publicKey,
      });
    }

    const resp = await bitcoinApi.buildRuneTransferPsbt(params);
    context.set('hex', resp.data);
    context.set('extendData', resp.extendData || undefined);

    return undefined;
  } catch (e: unknown) {
    handleTransferError(e);
  }
};

export const buildBrc20Transfer = async (
  context: TransferContext,
  {
    from,
    to,
    token,
    amount,
    fromChain,
    bridgeAddress,
    feeReceiver,
    fee,
    payload = '[]',
  }: TransferProps<BRC20Token>,
) => {
  const { connector, wallet } = getWalletState();
  if (!wallet) {
    throw new Error('Bitcoin wallet not found');
  }

  const network = fromChain.testnet ? 'testnet' : 'mainnet';
  const bitcoinApi = getBitcoinAPI({ network });

  try {
    const feeRate = await bitcoinApi.getFeeRate();
    const publicKey = await wallet.getPublicKey('brc20');

    const params: BuildPsbtParams = {
      address: from,
      to: bridgeAddress,
      tokenId: token.tick,
      amount: amount.toString(),
      feeRate,
      network,
      publicKey,
      enableRBF: false,

      bridgeTargetAddress: to,
      bridgeFeeAddress: feeReceiver,
      bridgeFee: fee.toNumber(),

      inscriptionId: JSON.parse(payload),
    };

    if (connector?.id === WalletDriverName.Xverse) {
      const { payment } = getXverseAddresses();
      Object.assign(params, {
        payGasAddress: payment.address,
        payGasPublicKey: payment.publicKey,
      });
    }

    const resp = await bitcoinApi.buildBrc20TransferPsbt(params);
    context.set('hex', resp.data);
    context.set('extendData', resp.extendData || undefined);

    return undefined;
  } catch (e: unknown) {
    handleTransferError(e);
  }
};

export const requestConfirmCaution = async (
  context: TransferContext,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  { fromChain }: TransferProps,
) => {
  const hex = context.get<string>('hex');
  if (!hex) {
    throw new Error('Psbt not found');
  }

  const psbt = bitcoin.Psbt.fromHex(hex, {
    network: fromChain.testnet ? bitcoin.networks.testnet : bitcoin.networks.bitcoin,
  });

  const inputs: PsbtInput[] = psbt.txInputs.map((input, i) => {
    const data = psbt.data.inputs[i];

    return {
      txid: input.hash.reverse().toString('hex'),
      vout: input.index,
      type: psbt.getInputType(i),
      sequence: input.sequence,

      ...(data.witnessUtxo
        ? {
            witnessUtxo: {
              script: data.witnessUtxo.script.toString('hex'),
              value: data.witnessUtxo.value,
            },
          }
        : {}),
    } as PsbtInput;
  });

  return new Promise<string | undefined>((resolve, reject) => {
    openBTCCautionDialog(inputs, {
      chain: fromChain,
      onConfirm: () => resolve(undefined),
      onReject: () => reject(new UserRejectedError()),
    });
  });
};
