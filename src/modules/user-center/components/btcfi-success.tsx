import { Button } from '@/components/ui/button';
import { CornerMarkGroup } from '@/components/ui/corner-mark';
import { Dialog, DialogCloseRounded, DialogContent } from '@/components/ui/dialog';
import { TaskDetail } from '@/modules/user-center/types';
import { cn } from '@/lib/utils';
import { Link } from '@/components/i18n/link';
import CheckSquareIcon from '@/components/icons/CheckSquareIcon';
import { useReport } from '@/modules/btcfi/hooks/task';
import { chain } from '@/modules/user-center/config';
import { useAccount } from '@/hooks/wallet/account';

interface CheckinSuccessDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  children?: React.ReactNode;
  data?: TaskDetail;
}

export function CheckinSuccessDialog({ open, onOpenChange, data }: CheckinSuccessDialogProps) {
  const { driver } = useAccount({ network: chain.networkType });
  const { mutate: report } = useReport();
  const handleConfirm = () => {
    if (onOpenChange) {
      onOpenChange(false);
    }
    report({
      from_page: 'page_usercenter',
      from_wallet: driver || '',
      op_type: 'usercenter_2btcfi',
    });
  };

  const progress_cfg = data?.action?.payload?.progress_cfg || [];
  const checkDay = data?.extraData?.cur_done_progress || 1;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="bl-py-7 bl-border-primary bl-w-[373px] md:bl-w-[466px] bl-bg-white bl-px-4 lg:bl-px-12 lg:bl-border"
        // overlayClassName="bl-blur"
      >
        <DialogCloseRounded className="bl-bg-white bl-border-[#393D44] bl-text-[#393D44] hover:bl-text-[#393D44] bl-absolute bl-top-2 bl-right-4" />
        <span className="bl-text-[20px] bl-uppercase bl-text-black bl-pt-4 bl-text-center md:bl-text-nowrap">
          Checked in successfully！
        </span>

        <div className="bl-grid bl-grid-cols-3 bl-gap-2 md:bl-gap-6">
          {progress_cfg?.map((item, index) => {
            const isSuccess = Number(checkDay) === Number(item.key);
            const btcImg =
              index > 2 ? '/images/mining-gala/btrs.png' : '/images/mining-gala/btr.png';
            return (
              <div
                key={index}
                className={cn('bl-h-[113px] bl-relative bl-rounded-lg', {
                  'bl-col-span-3': Number(item.key) === 7,
                })}
                style={{
                  background:
                    'linear-gradient(160deg, #211F1E 6.19%, #322F2B 17.68%, #383531 33.63%, #23211D 52.85%, #100F0D 77%, #0C0A0A 96.45%)',
                }}
              >
                <div className="bl-absolute bl-left-3 bl-top-3 bl-text-[#D9D9D9]">D{item.key}</div>
                <div
                  className={cn('bl-text-center bl-flex-col bl-flex-center bl-pt-8', {
                    'bl-hidden': index === 6,
                  })}
                >
                  <div className="bl-flex-center bl-size-10">
                    {isSuccess ? (
                      <CheckSquareIcon className="bl-text-[#11D000] bl-size-8" />
                    ) : (
                      <img src={btcImg} alt="btr" className="bl-size-10" />
                    )}
                  </div>
                  <div
                    className={cn('bl-text-center bl-pt-1 bl-text-xl', {
                      'bl-text-primary': isSuccess,
                    })}
                  >
                    <span className="bl-font-bold bl-text-lg md:bl-text-xl bl-text-[#D9D9D9]">
                      {item.value}BTR
                    </span>
                  </div>
                </div>
                <div
                  className={cn('bl-text-center bl-flex-center bl-gap-15', {
                    'bl-hidden': index < 6,
                  })}
                >
                  <span className="bl-font-bold bl-text-lg md:bl-text-xl bl-text-[#D9D9D9]">
                    {item.value}BTR
                  </span>
                  <img src="/images/mining-gala/super.png" alt="btr" className="bl-h-[116px]" />
                </div>
              </div>
            );
          })}
        </div>

        <div className="bl-w-full bl-flex bl-justify-center bl-pt-4">
          <Link to="/btcfi-v2">
            <Button
              variant="secondary"
              keepOverlay
              className="bl-w-[229px] bl-gap-2 bl-bg-black"
              onClick={handleConfirm}
            >
              <div className="bl-uppercase">get more BTR</div>
            </Button>
          </Link>
        </div>
        <CornerMarkGroup variant="dark" positions={['tl', 'br', 'bl']} />
      </DialogContent>
    </Dialog>
  );
}
