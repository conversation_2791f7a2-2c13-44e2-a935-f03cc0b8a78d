import { CopyButton } from '@/components/ui/copy-button';
import { PointIcon } from '../icons/reward-icons';
import { CornerMarkGroup } from '@/components/ui/corner-mark';
import ExchangeIcon from '../icons/exchange-icon';
import CheckSquareIcon from '@/components/icons/CheckSquareIcon';
import { cn } from '@/lib/utils';
import { Link } from '@/components/i18n/link';
import { staticAsset } from '@/lib/static';
import { useTranslation } from 'react-i18next';
import { Dialog, DialogCloseRounded, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { useInviteList } from '@/modules/user-center/hooks/invite';
import { useLoginContext } from '@/modules/user-center/components/login';

import { ReferralsItem, TaskDetail } from '@/modules/user-center/types';
import useChange from '@react-hook/change';

import { Button } from '@/components/ui/button';
import { TelegramFillIcon } from '@/components/icons/TelegramIcon';
import { XFillIcon } from '@/components/icons/XIcon';
import { QrCode } from '@/components/ui/qrcode.client';
import { ClientOnly } from 'remix-utils/client-only';
import { useWindowOpen } from '@/hooks/window';

const shareText =
  '🏎️ @BitlayerLabs Racer Center live! Earn #BitlayerPoints by completing tasks with me!';

export function ReferralNav() {
  return (
    <Link
      className="bl-relative bl-group bl-flex bl-items-center bl-justify-center bl-border bl-border-secondary bl-h-7 bl-duration-200 bl-overflow-hidden hover:bl-border-primary lg:bl-h-11 bl-w-[75px] lg:bl-w-30"
      to="/me/referral"
    >
      <img
        src={staticAsset('/images/user-center/referral-link.d6fa1f48db.png')}
        alt="bg"
        className="bl-size-full bl-absolute bl-top-0 bl-left-0 bl-duration-200 group-hover:bl-scale-125"
      />
      <span className="bl-relative bl-z-10 bl-text-white bl-text-xs lg:bl-text-xl">Referral</span>
    </Link>
  );
}

interface ReferralInfoProps {
  icon: React.FC<React.SVGProps<SVGSVGElement>>;
  label: string;
  value: string;
}

interface TaskItemProps {
  task: TaskDetail;
  showIcon?: boolean;
  showProgress?: boolean;
  showPoints?: boolean;
  isGrowth?: boolean;
}

export const TaskItem = ({ task, showPoints = true }: TaskItemProps) => {
  const { t } = useTranslation();
  const { isSigned } = useLoginContext();

  const CompleteButton = () => {
    if (!isSigned) {
      return null;
    }

    console.log(task.isCompleted, 'task');

    const Comp = 'button';

    return (
      <Comp className="bl-group bl-bg-white bl-h-full bl-border-2 bl-border-black bl-w-24 lg:bl-w-60 bl-min-w-0 bl-shrink-0 bl-duration-200">
        <div
          className={cn(
            'bl-h-full bl-flex bl-items-center bl-justify-center bl-text-white bl-text-xs lg:bl-text-xl bl-font-medium bl-bg-black',
            'lg:bl-corner-cutout bl-corner-cutout-br-4',
          )}
        >
          {t(`pages.userCenter.task.${task.isCompleted ? 'completed' : 'ended'}`)}
        </div>
      </Comp>
    );
  };

  return (
    <>
      <li className={cn('bl-flex bl-h-12 bl-relative bl-overflow-visible lg:bl-h-18 bl-w-full')}>
        <div
          className={cn(
            'bl-h-full bl-text-xl bl-relative bl-flex-1 bl-text-black bl-bg-[#F3F3F3] lg:bl-corner-cutout bl-corner-cutout-tl-3',
          )}
        >
          <div
            className={cn(
              'bl-min-h-12 md:bl-min-h-18  lg:bl-corner-cutout bl-corner-cutout-tl-3 bl-relative bl-top-px bl-left-px bl-px-6 md:bl-px-9 bl-flex bl-justify-between bl-gap-4 lg:bl-gap-12 bl-items-center',
            )}
          >
            <div className={cn('bl-flex bl-gap-4 bl-items-center')}>
              <div className="bl-text-xs bl-flex bl-items-center lg:bl-text-xl/normal bl-line-clamp-1 bl-gap-2">
                <span>{task.title}</span>
              </div>
              <div className={cn('bl-flex bl-items-center bl-gap-1 lg:bl-gap-3.5', {})}>
                {showPoints && (
                  <div className="bl-flex bl-items-center bl-gap-1 lg:bl-gap-2 bl-relative -bl-left-1">
                    <img
                      src="/images/user-center/bitlayer-golden.png"
                      alt="point"
                      className="bl-size-4 lg:bl-size-7"
                    />
                    <div className="bl-text-[10px] lg:bl-text-xl bl-text-[#818181] bl-font-medium">
                      +{task.rewardPoints.toLocaleString('en-US')}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        {isSigned && <CompleteButton />}
      </li>
    </>
  );
};

export function ReferralInfo({ icon: Icon, label, value }: ReferralInfoProps) {
  return (
    <div className="bl-flex bl-items-center bl-justify-between bl-gap-2 bl-h-10 bl-px-4 bl-bg-white/10 bl-text-sm lg:bl-h-14 lg:bl-px-7 lg:bl-text-[22px]">
      <div className="bl-flex bl-items-center bl-gap-1">
        <Icon className="bl-size-5" />
        <span className="bl-whitespace-nowrap">{label}</span>
      </div>
      <div className="bl-flex bl-items-center bl-gap-1">
        <span className="bl-max-w-40 bl-text-nowrap bl-overflow-clip bl-overflow-ellipsis lg:bl-max-w-64">
          {value}
        </span>
        <CopyButton text={value} iconClassName="bl-size-5 lg:bl-size-5 bl-text-primary" />
      </div>
    </div>
  );
}

interface ReferralRewardsProps {
  value: number;
  className?: string;
}

export function ReferralRewards({ className }: ReferralRewardsProps) {
  return (
    <div className={cn('bl-flex bl-justify-between', className)}>
      <h3 className="bl-text-white/50 lg:bl-text-[28px]">Referral Rewards</h3>
    </div>
  );
}

interface ReferralItemProps {
  item: ReferralsItem;
}

export function ReferralItem({ item }: ReferralItemProps) {
  const isPending = !item.isSuccess;
  const isSuccessful = item.isSuccess;
  const reward = item.rewardAmount;

  return (
    <li className="bl-border bl-border-divider bl-py-3 bl-px-4 bl-flex bl-items-center bl-justify-between bl-text-sm lg:bl-text-lg lg:bl-p-6">
      <span className="bl-text-primary">{item.address}</span>
      {isPending && (
        <div className="bl-flex bl-items-center bl-gap-1 lg:bl-gap-2">
          <ExchangeIcon className="bl-size-5 lg:bl-size-6" />
          <span className="bl-text-white">Complete 1 transaction on Bitlayer</span>
        </div>
      )}
      {isSuccessful && (
        <div className="bl-flex bl-items-center bl-gap-1 lg:bl-gap-2">
          <CheckSquareIcon className="bl-size-4 bl-text-[#11d000] lg:bl-size-5" />
          <span className="bl-text-white">Successful</span>
        </div>
      )}
      {reward && (
        <div className="bl-flex bl-items-center bl-gap-1 lg:bl-gap-2">
          <PointIcon className="bl-size-7" />
          <span className="bl-text-white">{reward.toLocaleString('en-US')}</span>
        </div>
      )}
    </li>
  );
}

export function ReferralList() {
  const { data: inviteInfo, trigger: nextPageTrigger, rootRef, refetch } = useInviteList();
  const myInviteList = inviteInfo?.pages.map((page) => page.data.list).flat() || [];
  const { isSigned } = useLoginContext();

  useChange(isSigned, () => {
    refetch();
  });
  const { t } = useTranslation();
  return (
    <div
      className="bl-border bl-border-[#363636] bl-mt-3.5 bl-relative bl-py-4 lg:bl-py-12 lg:bl-mt-8"
      style={{
        background:
          'linear-gradient(169deg, rgba(31, 32, 34, 0.70) -0.22%, rgba(0, 0, 0, 0.70) 91.66%), #080808',
      }}
    >
      <h3 className="bl-text-white bl-font-bold lg:bl-text-[30px] bl-px-4 lg:bl-px-12">
        {t('pages.userCenter.invite.my')}
      </h3>
      <div
        ref={rootRef}
        className="bl-w-full bl-h-[220px] lg:bl-h-[400px] bl-overflow-auto bl-scrollbar-thin bl-scrollbar-track-[#2e2e2e] bl-scrollbar-thumb-primary"
      >
        <ul className="bl-mt-3 bl-space-y-2.5 lg:bl-space-y-4 bl-px-4 lg:bl-px-12">
          {myInviteList.map((item, index) => (
            <ReferralItem key={index} item={item} />
          ))}
        </ul>
        <div className="bl-w-full bl-h-3" ref={nextPageTrigger}></div>
      </div>
      <CornerMarkGroup className="[--bl-space:0.35rem] lg:[--bl-space:1.25rem]" />
    </div>
  );
}

interface InviteDialogProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  inviteText?: string;
  inviteLink: string;
  children?: React.ReactNode;
}

export function InviteDialog({
  inviteText = shareText,
  children,
  inviteLink,
  open,
  onOpenChange,
}: InviteDialogProps) {
  const { t } = useTranslation();
  const { open: openWindow } = useWindowOpen();
  const searchParams = new URLSearchParams({
    text: inviteText,
    url: inviteLink,
  });

  const handleShareTg = () => {
    const url = `https://t.me/share/url?${searchParams}`;
    openWindow(url);
  };

  const handleShareX = () => {
    const url = `https://twitter.com/intent/post?${searchParams}`;
    openWindow(url);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent
        className="bl-py-7 bl-px-7 lg:bl-px-16 lg:bl-border bl-border-primary lg:bl-bg-black"
        overlayClassName="bl-blur"
      >
        <DialogCloseRounded className="bl-bg-black bl-border-secondary bl-text-secondary hover:bl-border-primary bl-absolute bl-top-2 bl-right-4" />
        <h3 className="bl-text-[22px] bl-text-white bl-text-center bl-uppercase">
          {t('pages.userCenter.invite.invite')}
        </h3>
        <div className="bl-w-[339px] bl-h-[488px] bl-relative">
          <img
            src={staticAsset('/images/user-center/referral-invite-bg.c3a553d4cc.png')}
            alt=""
            className="bl-size-full bl-object-cover"
          />
          <div className="bl-absolute bl-size-full bl-top-0 bl-left-0 bl-p-8">
            <img src="/images/bitlayer-logo.png" alt="bitlayer" className="bl-h-5 bl-mb-4" />
            <div className="bl-text-white bl-text-[30px]/[110%] bl-font-bold">
              <div className="bl-uppercase">{t('pages.userCenter.invite.discover')}</div>
              <div className="bl-flex bl-items-center bl-gap-1 bl-uppercase">
                {t('pages.userCenter.invite.and')} <PointIcon className="bl-size-10" />
              </div>
            </div>

            <div
              className="bl-size-[138px] bl-flex bl-items-center bl-justify-center bl-absolute bl-left-1/2 -bl-translate-x-1/2 bl-bottom-20"
              style={{
                background:
                  'linear-gradient(180deg, rgba(0, 0, 0, 0.40) 0%, rgba(0, 0, 0, 0.80) 100%)',
              }}
            >
              <ClientOnly>
                {() => <QrCode value={inviteLink} size={130} color="#aeb5c5" />}
              </ClientOnly>
              <CornerMarkGroup className="[--bl-space:0.25rem]" />
            </div>
          </div>
        </div>
        <div className="bl-flex bl-justify-center bl-items-center bl-gap-1">
          <span className="bl-max-w-80 bl-text-nowrap bl-overflow-clip bl-overflow-ellipsis">
            {inviteLink}
          </span>
          <CopyButton text={inviteLink} iconClassName="bl-size-5 lg:bl-size-5 bl-text-primary" />
        </div>
        <div className="bl-grid bl-grid-cols-2 bl-gap-4">
          <Button overlayFrom="none" className="bl-w-full bl-gap-2" onClick={handleShareTg}>
            <TelegramFillIcon className="bl-size-5" />
            <div>Telegram</div>
          </Button>
          <Button overlayFrom="none" className="bl-w-full bl-gap-2" onClick={handleShareX}>
            <XFillIcon className="bl-size-5" />
            <div>Twitter</div>
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
