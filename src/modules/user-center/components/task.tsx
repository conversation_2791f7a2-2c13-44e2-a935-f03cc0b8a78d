import { ChevronDownIcon, ChevronUpIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import CheckSquareIcon from '@/components/icons/CheckSquareIcon';
import { ReactNode, useEffect, useMemo, useState } from 'react';
import { TaskDetail, TaskListData } from '../types';
import { BadgeItem } from './badge';
import { Text } from '@/components/ui/text';
import { SbtDialog } from '@/modules/user-center/components/bybit-sbt-dialog';
import { GoplusSbtDialog } from '@/modules/user-center/components/goplus-sbt-dialog';
import { BtcDialog } from '@/modules/mining-gala/components/btc-dialog.tsx';
import { chain } from '@/modules/user-center/config';
import LoadingIcon from '@/components/icons/LoadingIcon.tsx';
import { useAtomValue, useSetAtom } from 'jotai';
import { HelmetDialog } from '@/modules/btcfi/components/helmet-dialog';
import { TaskItemProps as TaskProps } from '@/modules/btcfi/types';
import { InfoWarning } from '@/modules/btcfi/components/info-warning';
import dayjs from 'dayjs';

import {
  UseTaskActionOptions,
  UserCenterTaskContext,
  useBindX,
  useTaskAction,
  dailyAtom,
} from '@/modules/user-center/hooks/task';

import { useDailyCheck, useClaimSolv } from '@/modules/btcfi/hooks/task';

import XIcon from '../icons/x-icon';
// import ExchangeIcon from '../icons/exchange-icon';
import UploadIcon, { UploadRightIcon } from '../icons/upload-icon';
import DownloadIcon from '../icons/download-icon';
import { BitcoinIcon, DollarIcon, USDCIcon, USDTIcon } from '../icons/coin-icons';
import { Trans, useTranslation } from 'react-i18next';
// import { staticAsset } from '@/lib/static';
import BucketsIcon from '../icons/buckets-icon';
import MacaronIcon from '../icons/macaron-icon';
import BybitIcon from '../icons/bybit-icon';
import BullishIcon from '../icons/bullish-icon';
import ChaingeIcon from '../icons/chainge-icon';
import DailyCheckIcon from '../icons/daily-check-icon';
import AvalonIcon from '../icons/avalon-icon';
import TotalTxnIcon from '../icons/total-txn-icon';
import TotalBridgedInIcon from '../icons/total-bridged-in-icon';
import EyeIcon from '../icons/eye-icon';
import BridgeIcon from '../icons/bridge-icon';
import HelmetIcon from '../icons/helmet-icon';
import MasonIcon from '../icons/mason-icon';
import GoplusIcon from '../icons/goplus-icon';
import GalxeIcon from '../icons/galxe-icon';

import { useToast } from '@/hooks/toast';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useLoginContext } from './login';
import {
  Dialog,
  DialogCloseRounded,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { CornerMarkGroup } from '@/components/ui/corner-mark';
import useChange from '@react-hook/change';
import { stagger, useAnimate } from 'framer-motion';
import { staticAsset } from '@/lib/static';
import { useAccount } from '@/hooks/wallet/account';

type TaskIcon = React.FC<React.SVGProps<SVGSVGElement>>;

const taskIconMap: Record<string, TaskIcon> = {
  twitter: XIcon,
  x: XIcon,
  bridge: BridgeIcon,
  send: UploadIcon,
  receive: DownloadIcon,
  transaction: UploadRightIcon,
  usdt: USDTIcon,
  usdc: USDCIcon,
  dollar: DollarIcon,
  bitcoin: BitcoinIcon,
  macaron: MacaronIcon,
  bybit: BybitIcon,
  bullish: BullishIcon,
  chainge: ChaingeIcon,
  btcfi_dailycheckin: DailyCheckIcon,
  avalon: AvalonIcon,
  total_txn: TotalTxnIcon,
  total_bridged_in: TotalBridgedInIcon,
  bitlayer: EyeIcon,
  btcfi_helmet_nft: HelmetIcon,
  meson_stake_solv: MasonIcon,
  goplus_sbt: GoplusIcon,
  galxe: GalxeIcon,
};

const getTaskIcon = (type: string): TaskIcon => {
  return taskIconMap[type] || BitcoinIcon;
};

export const Dot = ({ className }: { className?: string }) => {
  return <div className={cn('bl-size-3 bl-rounded-full bl-bg-primary', className)}></div>;
};

export const DailyCheckContent = ({ task }: TaskItemProps) => {
  return (
    <div className="bl-gap-1 md:bl-gap-1 bl-flex">
      {task.action?.payload &&
        task.action?.payload.progress_cfg?.map((item, index) => {
          return (
            <div
              key={index}
              className="bl-min-w-[28px] md:bl-min-w-[56px] bl-flex bl-flex-col bl-justify-center bl-items-center bl-text-center"
            >
              <div
                className={cn(
                  'bl-border-b-[1px] bl-text-[10px] md:bl-text-base bl-border-[#CFCFCF] bl-text-[#CFCFCF] bl-w-full',
                  {
                    'bl-line-through bl-text-black bl-border-black':
                      item.key <= Number(task.extraData.cur_done_progress),
                  },
                  {
                    'bl-border-[#A10000]': task.extraData.interrupted_at === item.key,
                  },
                )}
              >
                D{item.key}
              </div>
              {
                <div
                  className={cn('bl-flex-center bl-text-[8px] md:bl-text-xs bl-h-6', {
                    'md:bl-text-base': task.extraData.cur_done_progress === item.key - 1,
                  })}
                >
                  {(item.key === 7 || item.key - 1 === task.extraData.cur_done_progress) && (
                    <>
                      <img
                        src="/images/user-center/bitlayer-golden.png"
                        alt="point"
                        className="bl-size-4 lg:bl-size-4"
                      />
                      <div className="bl-text-[#818181] bl-font-medium">
                        +{item.value.toLocaleString('en-US')}
                      </div>
                    </>
                  )}
                </div>
              }
            </div>
          );
        })}
    </div>
  );
};

export const GrowthContent = ({ task }: TaskItemProps) => {
  const { address } = useAccount({ network: chain.networkType });

  if (
    task.extraData == undefined ||
    task.action.payload?.progress_cfg == undefined ||
    task.extraData.cur_done_progress == undefined ||
    task.extraData.cur_claimed_progress == undefined ||
    !address
  ) {
    return null;
  }

  const curDone = task.extraData.cur_done_progress;
  const claimed = task.extraData.cur_claimed_progress;
  const preSymbol = task.title === 'Total TXN' ? 'X' : '$';
  const progressCfg = task.action.payload.progress_cfg;
  const cfgLength = progressCfg.length;

  const formatValue = (value: number) => {
    const valueNum = Number(value);

    if (valueNum > 0) {
      return value >= 1000
        ? parseFloat((valueNum / 1000).toFixed(2)).toString() + 'K'
        : parseFloat(valueNum.toFixed(2)).toString();
    }
    return '0';
  };

  const calculateProgress = (curDone: number, item: { key: number }, index: number) => {
    if (!progressCfg || index + 1 >= cfgLength) {
      return 0;
    }
    const progress = ((curDone - item.key) / (progressCfg[index + 1].key - item.key)) * 100;
    return progress < 0 ? 0 : progress;
  };

  return (
    <div className="bl-flex bl-flex-row bl-w-full lg:bl-w-[420px] bl-overflow-hidden">
      {progressCfg.map((item, index) => {
        const isIn =
          (progressCfg && curDone >= item.key && curDone <= progressCfg[index + 1]?.key) ||
          (index == 0 && curDone <= item.key) ||
          (index == cfgLength - 1 && curDone >= item.key);

        const nextItemKey = progressCfg[index + 1]?.key;

        return (
          <div
            key={index}
            className="bl-flex bl-flex-col bl-w-full bl-text-[10px] lg:bl-text-[12px]"
            style={{ zIndex: cfgLength - index }}
          >
            <div className="bl-min-w-7 lg:bl-min-w-8 bl-flex bl-justify-start bl-items-center bl-text-center">
              <span
                className={cn({
                  'bl-line-through bl-text-secondary': curDone > item.key,
                })}
              >
                {preSymbol + formatValue(item.key)}
                {index == 0 && task.title === 'Total TXN' && <span>(Times)</span>}
              </span>
            </div>
            <div className="bl-flex bl--translate-y-1 bl-justify-start bl-relative bl-border-secondary">
              {index < cfgLength - 1 && (
                <>
                  <div
                    className="bl-absolute bl-left-0 bl-translate-y-2 bl-h-[1px] bl-bg-black"
                    style={{
                      width:
                        isIn && nextItemKey
                          ? `${((curDone - item.key) / (nextItemKey - item.key)) * 100}%`
                          : curDone > item.key
                            ? '100%'
                            : '0%',
                    }}
                  ></div>
                  <div
                    className="bl-absolute bl-right-0 bl-translate-y-2 bl-h-[1px] bl-bg-secondary"
                    style={{
                      width:
                        isIn && nextItemKey
                          ? `${(1 - (curDone - item.key) / (nextItemKey - item.key)) * 100}%`
                          : curDone <= item.key
                            ? '100%'
                            : '0%',
                    }}
                  ></div>
                </>
              )}

              <span
                className={cn('bl-w-2 bl-h-2 bl-translate-y-1 bl-rounded-full', {
                  'bl-bg-secondary': curDone <= item.key,
                  'bl-bg-black': curDone > item.key,
                })}
              ></span>
              {isIn && (
                <div
                  className="bl-absolute bl-translate-y-[1px] bl-leading-4 bl-bg-black bl-text-white bl-px-2 bl-rounded-full"
                  style={{
                    left: `${calculateProgress(curDone, item, index)}%`,
                  }}
                >
                  {preSymbol + formatValue(curDone)}
                </div>
              )}
            </div>
            <div
              className={cn('bl-flex-row bl-flex bl-items-center', {
                'bl-text-secondary': claimed < item.key,
                'bl-text-black': claimed >= item.key,
              })}
            >
              {index == 0 && (
                <img
                  src="/images/user-center/bitlayer-golden.png"
                  alt="point"
                  className="bl-size-2 lg:bl-size-4"
                />
              )}
              +{formatValue(item.value)}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export function UserCenterTaskProvider({ children }: { children?: ReactNode }) {
  const [bindXOpen, setBindXOpen] = useState(false);
  return (
    <UserCenterTaskContext.Provider
      value={{
        bindXOpen,
        setBindXOpen,
      }}
    >
      {children}
      <BindXDialog open={bindXOpen} onOpenChange={setBindXOpen} />
    </UserCenterTaskContext.Provider>
  );
}

interface TaskItemProps {
  task: TaskDetail;
  showIcon?: boolean;
  showProgress?: boolean;
  taskActionOptions?: UseTaskActionOptions;
  showPoints?: boolean;
  isGrowth?: boolean;
}

export const CompletedTaskItem = ({ task, showIcon = true }: TaskItemProps) => {
  const Icon = getTaskIcon(task.logoType);
  const rewardPoints =
    task.action.type === 'check_white_list'
      ? task.extraData?.rewards.toLocaleString()
      : task.rewardPoints.toLocaleString('en-US');

  return (
    <li className="bl-flex bl-min-h-12 bl-justify-between bl-items-center lg:bl-min-h-18 bl-bg-[#F3F3F3] md:bl-corner-cutout md:bl-corner-cutout-tl-3">
      <div
        className={cn(
          'bl-h-full bl-px-6 md:bl-px-9 bl-flex bl-flex-row bl-justify-start bl-items-center',
        )}
      >
        <div className={cn('bl-flex-center bl-items-center bl-text-black ')}>
          {showIcon && (
            <Icon className="bl-size-5 lg:bl-size-10 bl-min-w-5 bl-shrink-0 bl-text-black/30" />
          )}
          <div className="bl-grow bl-pl-3">
            <div className="bl-text-xs lg:bl-text-xl/normal bl-line-clamp-1">{task.title}</div>
          </div>
        </div>
      </div>
      <div
        className={cn(
          'bl-h-full bl-w-24 lg:bl-w-60 bl-min-w-0 bl-shrink-0 bl-flex bl-items-center bl-justify-between bl-px-2.5 lg:bl-px-10',
        )}
      >
        <div className="bl-flex bl-items-center bl-gap1 lg:bl-gap-2">
          <img
            src="/images/user-center/bitlayer-golden.png"
            alt="point"
            className="bl-size-4 lg:bl-size-7"
          />
          <div className="bl-text-xs lg:bl-text-xl bl-text-[#818181] bl-w-16 md:bl-w-[62px] bl-font-medium">
            +{rewardPoints}
          </div>
          <CheckSquareIcon className="bl-text-[#11D000] bl-size-3.5 lg:bl-size-9" />
        </div>
      </div>
    </li>
  );
};

const DailyContent = ({ task }: { task: TaskDetail }) => {
  const [isLoading] = useState(false);
  const recheckList = task.extraData?.rechecked_in_days?.split(',');
  const missList = task.extraData?.missing_days?.split(',');
  return (
    <div className="bl-flex bl-w-full bl-flex-wrap bl-bg-[#F3F3F3] md:bl-h-[74px]">
      {task?.action.payload?.progress_cfg?.map((item, index) => {
        return (
          <div
            key={index}
            className={cn(
              'bl-w-1/4 md:bl-w-[calc(100%/7)] bl-mt-4 bl-mb-3 md:bl-h-12 bl-flex bl-flex-col bl-justify-center bl-items-center bl-border-l bl-border-black',
              {
                'bl-border-0': index === 0,
                'bl-border-0 md:bl-border-l': index === 4,
                'bl-w-1/2 md:bl-w-[calc(100%/7)]': index === 6,
              },
            )}
          >
            <div
              className={cn('bl-text-xs md:bl-text-xl bl-uppercase bl-text-black', {
                'bl-text-primary': Number(item.key) === Number(task?.extraData?.today_progress),
                'bl-text-black': recheckList?.includes(String(item.key)),
              })}
            >
              {recheckList?.includes(String(item.key)) ? (
                <span>Recheck</span>
              ) : missList?.includes(String(item.key)) ? (
                <span>Missed</span>
              ) : (
                <span>D{item.key}</span>
              )}
            </div>
            <div
              className={cn(
                'bl-text-xs bl-text-[#B0B0B0] bl-flex bl-items-center bl-gap-1 md:bl-text-lg',
                {
                  'bl-text-black': item.key === 7,
                  'bl-text-primary': Number(item.key) <= Number(task.extraData.cur_done_progress),
                  'bl-text-[#B0B0B0]': recheckList?.includes(String(item.key)),
                },
              )}
            >
              {Number(task.extraData.cur_done_progress) + 1 === Number(item.key) && isLoading ? (
                <img src="/images/btcfi/loading.gif" className="bl-size-3 md:bl-size-6" alt="" />
              ) : (
                <>
                  <span>{item.value}BTR</span>{' '}
                  {Number(item.key) <= Number(task.extraData.cur_done_progress) && (
                    <CheckSquareIcon className="bl-size-3" />
                  )}
                </>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export const TaskItem = ({
  task,
  showIcon = true,
  showProgress = true,
  taskActionOptions,
  showPoints = true,
  isGrowth = false,
}: TaskItemProps) => {
  const Icon = getTaskIcon(task.logoType);
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const { isSigned } = useLoginContext();
  const { mutate: checkDaily, data } = useDailyCheck();
  const { mutate: claimSolv, data: solvData } = useClaimSolv();
  const [taskLoading, setTaskLoading] = useState(false);
  const [noBalance, setNoBalance] = useState(false);
  const [helmetOpen, setHelmetOpen] = useState(false);
  const isDailyCheck = task.action.type === 'on_chain_gas';
  const isHelmet = task.action.type === 'check_helmet';
  const isSove = task.action.type === 'redirect_on_chain';
  const isAfter =
    isSove && dayjs(Number(task.extraData.end_timestamp) * 1000).isAfter(dayjs(), 'day');

  const dailyInfo = useAtomValue(dailyAtom);
  const setDaily = useSetAtom(dailyAtom);

  const isEnded = task.status === 2;

  // console.log(isEnded, 'isEnded');

  const showClaiming =
    (task.title === 'Total TXN' && dailyInfo.isTxClaiming) ||
    (task.title === 'Total Bridged In' && dailyInfo.isBridgeClaiming);

  useEffect(() => {
    if (isDailyCheck && data?.isNotBalance) {
      setNoBalance(true);
    }
  }, [data]);

  useEffect(() => {
    if (isSove && solvData?.isNotBalance) {
      setNoBalance(true);
    }
  }, [solvData]);

  const handleDailyCheck = (task: TaskDetail) => {
    setDaily((prev) => ({ ...prev, isDailyLoading: true }));
    checkDaily(
      {
        projectId: String(task.action?.payload?.project_id),
      },
      {
        onSuccess: (result) => {
          setNoBalance(result.isNotBalance);
          setDaily((prev) => ({ ...prev, isDailyLoading: !result.isNotBalance }));
        },
      },
    );
  };

  const handleSolv = () => {
    if (isAfter) {
      window.open('https://app.free.tech/direct/pell-solvbtc', '_blank');
    } else {
      if (task?.canClaim) {
        setTaskLoading(true);
        claimSolv(
          {
            task_id: String(task.taskId),
            projectId: String(task.action?.payload?.project_id),
          },
          {
            onSuccess: (result) => {
              setNoBalance(result.isNotBalance);
            },
          },
        );
      }
    }
  };

  const CompleteButton = () => {
    const actions = useTaskAction(task, taskActionOptions);
    if (!isSigned) {
      return null;
    }

    const result = isEnded ? {} : actions;
    const Comp = actions.href ? 'a' : 'button';

    return (
      <Comp
        className={cn(
          'bl-group bl-bg-white bl-h-full bl-border-2 bl-border-black bl-w-24 lg:bl-w-60 bl-min-w-0 bl-shrink-0 bl-duration-200',
          { 'hover:bl-bg-primary': !isEnded },
        )}
        {...result}
      >
        <div
          className={cn(
            'bl-h-full bl-flex bl-items-center bl-justify-center bl-text-white bl-text-xs lg:bl-text-xl bl-font-medium bl-bg-black',
            'lg:bl-corner-cutout bl-corner-cutout-br-4',
            { 'group-hover:bl-text-primary bl-duration-200': !isEnded },
          )}
        >
          {isGrowth && showClaiming ? (
            <LoadingIcon className="bl-animate-spin" />
          ) : isEnded ? (
            t('pages.userCenter.task.ended')
          ) : task.canClaim ? (
            t('pages.userCenter.task.claim')
          ) : (
            t('pages.userCenter.task.complete')
          )}
        </div>
      </Comp>
    );
  };

  const DailyCheckButton = useMemo(() => {
    if (!isSigned || task.isCompleted) {
      return <span />;
    }
    return (
      <Text onClick={() => handleDailyCheck(task)}>
        <button className="bl-group bl-bg-white hover:bl-bg-primary bl-h-full bl-border-2 bl-border-black bl-w-24 lg:bl-w-60 bl-min-w-0 bl-shrink-0 bl-duration-200">
          <div
            className={cn(
              'bl-h-full bl-flex bl-items-center bl-justify-center bl-text-white bl-text-xs lg:bl-text-xl bl-font-medium bl-bg-black',
              'lg:bl-corner-cutout bl-corner-cutout-br-4',
              'group-hover:bl-text-primary bl-duration-200',
            )}
          >
            {dailyInfo.isDailyLoading ? (
              <LoadingIcon className="bl-animate-spin" />
            ) : (
              t('pages.userCenter.task.complete')
            )}
          </div>
        </button>
      </Text>
    );
  }, [isSigned, task, dailyInfo]);

  const SbtButton = useMemo(() => {
    const Com = task.action.type === 'check_goplus_sbt' ? GoplusSbtDialog : SbtDialog;
    return (
      <Com open={open} onOpenChange={setOpen}>
        <button className="bl-group bl-bg-white hover:bl-bg-primary bl-h-full bl-border-2 bl-border-black bl-w-24 lg:bl-w-60 bl-min-w-0 bl-shrink-0 bl-duration-200">
          <div
            className={cn(
              'bl-h-full bl-flex bl-items-center bl-justify-center bl-uppercase bl-text-white bl-text-xs lg:bl-text-xl bl-font-medium bl-bg-black',
              'lg:bl-corner-cutout bl-corner-cutout-br-4',
              'group-hover:bl-text-primary bl-duration-200',
            )}
          >
            {t('pages.userCenter.task.check')}
          </div>
        </button>
      </Com>
    );
  }, [open, setOpen]);

  const HelmetButton = useMemo(() => {
    return (
      <button
        onClick={() => setHelmetOpen(true)}
        className="bl-group bl-bg-white hover:bl-bg-primary bl-h-full bl-border-2 bl-border-black bl-w-24 lg:bl-w-60 bl-min-w-0 bl-shrink-0 bl-duration-200"
      >
        <div
          className={cn(
            'bl-h-full bl-flex bl-items-center bl-justify-center bl-uppercase bl-text-white bl-text-xs lg:bl-text-xl bl-font-medium bl-bg-black',
            'lg:bl-corner-cutout bl-corner-cutout-br-4',
            'group-hover:bl-text-primary bl-duration-200',
          )}
        >
          {t('pages.userCenter.task.check')}
        </div>
      </button>
    );
  }, []);

  useEffect(() => {
    const _isLoading = task.extraData?.is_tx_pending || false;
    setTaskLoading(_isLoading);
  }, [task]);

  const MasonButton = useMemo(() => {
    const disabled = !task.canClaim && !isAfter;

    return (
      <button
        onClick={handleSolv}
        className={cn(
          'bl-group bl-bg-white hover:bl-bg-primary bl-h-full bl-border-2 bl-border-black bl-w-24 lg:bl-w-60 bl-min-w-0 bl-shrink-0 bl-duration-200',
          {
            'bl-bg-secondary hover:bl-bg-secondary bl-border-0 bl-cursor-not-allowed': disabled,
          },
        )}
        disabled={taskLoading || disabled}
      >
        <div
          className={cn(
            'bl-h-full bl-flex bl-items-center bl-justify-center bl-uppercase bl-text-white bl-text-xs lg:bl-text-xl bl-font-medium bl-bg-black',
            'lg:bl-corner-cutout bl-corner-cutout-br-4',
            'group-hover:bl-text-primary bl-duration-200',
            {
              'bl-bg-secondary bl-border-secondary bl-text-gray-50 group-hover:bl-text-gray-50':
                disabled,
            },
          )}
        >
          {taskLoading ? (
            <img src="/images/btcfi/loading.gif" className="bl-size-3 md:bl-size-6" alt="" />
          ) : !isAfter && task.canClaim ? (
            t('pages.userCenter.task.claim')
          ) : (
            t('pages.userCenter.task.complete')
          )}
        </div>
      </button>
    );
  }, [task, taskLoading]);

  const actionButtonMap: { [key: string]: React.ReactElement } = {
    check_goplus_sbt: SbtButton,
    check_bybit: SbtButton,
    on_chain_gas: DailyCheckButton,
    check_helmet: HelmetButton,
    redirect_on_chain: MasonButton,
  };

  const ButtonComponent = actionButtonMap[task.action.type] || <CompleteButton />;
  return (
    <>
      <li
        className={cn('bl-flex bl-h-12 bl-relative bl-overflow-visible lg:bl-h-18 bl-w-full', {
          'bl-h-24': isGrowth,
        })}
      >
        {isDailyCheck && (
          <div className="bl-absolute bl-text-xs md:bl-text-base md:bl-flex bl-flex-center bl-z-10 bl-w-[96px] bl-h-5 md:bl-w-[140px] md:bl-h-[30px] bl-top-[-12px] md:bl-top-[-20px] bl-text-white bl-bg-black bl-corner-cutout bl-corner-cutout-tl-1 md:bl-corner-cutout-tl-3">
            BTCFI Carnival
          </div>
        )}
        <div
          className={cn(
            'bl-h-full bl-text-xl bl-relative bl-flex-1 bl-text-black bl-bg-[#F3F3F3] lg:bl-corner-cutout bl-corner-cutout-tl-3',
            { 'lg:bl-corner-cutout-tl-0 bl-overflow-visible': isDailyCheck },
          )}
        >
          <div
            className={cn(
              'bl-min-h-12 md:bl-min-h-18  lg:bl-corner-cutout bl-corner-cutout-tl-3 bl-relative bl-top-px bl-left-px bl-px-6 md:bl-px-9 bl-flex bl-justify-between bl-gap-4 lg:bl-gap-12 bl-items-center',
              {
                'lg:bl-px-0 lg:bl-pl-10 bl-px-0 bl-pl-6 lg:bl-gap-3 bl-flex-col bl-gap-0 lg:bl-flex-row bl-justify-center bl-items-start lg:bl-justify-between lg:bl-items-center bl-translate-y-2 lg:bl-translate-y-0':
                  isGrowth,
              },
            )}
          >
            <div className={cn('bl-flex bl-gap-4 bl-items-center')}>
              {showIcon && <Icon className="bl-size-5 lg:bl-size-9 bl-min-w-4" />}
              <div className="bl-text-xs bl-flex bl-items-center lg:bl-text-xl/normal bl-line-clamp-1 bl-gap-2">
                <span>{task.title}</span>
                {isHelmet && (
                  <InfoWarning text="Each helmet can only receive benefits once a week at one wallet address" />
                )}
                <img
                  src="/images/mining-gala/btr.png"
                  className={cn('bl-size-4 md:bl-size-6', { 'bl-hidden': !isDailyCheck })}
                  alt="btr"
                />
              </div>
            </div>
            {isGrowth ? (
              <GrowthContent task={task} />
            ) : (
              <div
                className={cn('bl-flex bl-items-center bl-gap-1 lg:bl-gap-3.5', {
                  'bl-hidden': isDailyCheck,
                })}
              >
                {showProgress && (
                  <div className="bl-text-xs lg:bl-text-xl">
                    <span>{task.completedCount}</span>
                    <span>/{task.targetCount}</span>
                  </div>
                )}
                {showPoints && (
                  <div className="bl-flex bl-items-center bl-gap-1 lg:bl-gap-2 bl-relative -bl-left-1">
                    <img
                      src="/images/user-center/bitlayer-golden.png"
                      alt="point"
                      className="bl-size-4 lg:bl-size-7"
                    />
                    <div className="bl-text-[10px] lg:bl-text-xl bl-text-[#818181] bl-font-medium">
                      +{task.rewardPoints.toLocaleString('en-US')}
                    </div>
                  </div>
                )}
              </div>
            )}
            {task.action.type === 'check_bybit' && (
              <div className="bl-flex bl-items-center bl-gap-1 lg:bl-gap-2 bl-relative -bl-left-1">
                <img
                  src={staticAsset('/bybit-honor-badge/badge.gif')}
                  alt="point"
                  className="bl-size-3 lg:bl-size-5"
                />

                <div className="bl-text-[10px] lg:bl-text-xl bl-text-[#818181] bl-font-medium">
                  +1
                </div>
              </div>
            )}
          </div>
        </div>
        {isSigned &&
          (task.isCompleted && isDailyCheck ? (
            <div className="bl-w-24 lg:bl-w-60 bl-flex bl-pr-16 bl-bg-[#F3F3F3] bl-justify-end bl-items-center">
              <CheckSquareIcon className={cn('bl-text-[#11D000] bl-size-5 lg:bl-size-8')} />
            </div>
          ) : (
            ButtonComponent
          ))}
        {isDailyCheck && <BtcDialog open={noBalance} onOpenChange={setNoBalance} />}
      </li>
      {isDailyCheck && <DailyContent task={task} />}
      {isHelmet && (
        <HelmetDialog
          light
          task={task as TaskProps}
          open={helmetOpen}
          onOpenChange={setHelmetOpen}
        />
      )}
    </>
  );
};

interface TaskTabTriggerProps {
  label: string;
  background?: string;
  active?: boolean;
  showBadge?: boolean;
  onClick?: () => void;
}

export const TaskTabTrigger = ({
  label,
  active,
  background = '/images/user-center/tab-bg.png',
  showBadge = false,
  onClick,
}: TaskTabTriggerProps) => {
  return (
    <button
      className={cn(
        'bl-group bl-relative bl-w-full bl-h-20 bl-border bl-border-[#393d44] bl-text-left bl-text-[32px] bl-font-light focus-visible:bl-outline-none',
        'bl-bg-no-repeat bl-bg-cover bl-bg-center bl-duration-300',
        {
          'bl-font-medium bl-text-white bl-text-[40px] bl-h-96 bl-border-white': active,
        },
      )}
      style={{
        backgroundImage: `url(${background})`,
      }}
      disabled={active}
      onClick={onClick}
    >
      {showBadge && <Dot className="bl-absolute -bl-top-1.5 -bl-right-1.5" />}
      <div
        className={cn('bl-w-full bl-h-full bl-flex bl-items-center bl-px-6 bl-duration-300', {
          'bl-bg-black/60 group-hover:bl-bg-black/40': !active,
        })}
      >
        {label}
      </div>
    </button>
  );
};

interface TaskListProps {
  tasks: TaskDetail[];
}

const TaskList = ({ tasks }: TaskListProps) => {
  return (
    <ul className="bl-w-full bl-space-y-3 lg:bl-space-y-6">
      {tasks.map((task) => {
        const isTwoStep = [
          'check',
          'check_points',
          'check_bybit',
          'check_goplus_sbt',
          'check_white_list',
          'redirect_on_galxe',
        ].includes(task.action.type);
        const isHelmet = task?.action?.type === 'check_helmet';
        const isGrowth = ['Total TXN', 'Total Bridged In'].includes(task.title);
        const isDailyCheck = task.action.type === 'on_chain_gas';
        const isSolv = task.action.type === 'redirect_on_chain';
        if (task.isCompleted && !isGrowth && !isDailyCheck && !isHelmet) {
          return <CompletedTaskItem task={task} key={task.taskId} />;
        }
        return (
          <TaskItem
            task={task}
            key={task.taskId}
            showPoints={!isTwoStep && !isGrowth && !isHelmet && !isSolv}
            showProgress={!isTwoStep && !isGrowth && !isHelmet && !isSolv}
            isGrowth={isGrowth}
          />
        );
      })}
    </ul>
  );
};

export interface TaskTabAPI {
  open: (index: number) => void;
}

interface TaskTabsProps {
  tasks: TaskListData;
  defaultTab?: number;
  setApi?: (api: TaskTabAPI) => void;
}

const TaskFolder = ({
  label,
  children,
  defaultOpen = false,
}: {
  label: string;
  children?: ReactNode;
  defaultOpen?: boolean;
}) => {
  const [open, setOpen] = useState(false);
  const toggle = () => setOpen(!open);

  useEffect(() => {
    setOpen(defaultOpen);
  }, [defaultOpen]);
  return (
    <div className="bl-border bl-border-x-0 bl-border-secondary md:bl-px-4 bl-py-2 md:bl-py-8">
      <button
        className="bl-h-11 bl-w-full bl-text-xl bl-text-black bl-uppercase bl-flex bl-items-center bl-justify-center bl-gap-3"
        onClick={toggle}
      >
        <span>{label}</span>
        <ChevronDownIcon
          className={cn('bl-size-4 bl-duration-200', {
            'bl-rotate-180': open,
          })}
        />
      </button>
      <div
        className={cn('bl-h-0 bl-w-full bl-duration-200 bl-overflow-hidden', {
          'bl-h-auto bl-max-h-[1000rem] bl-pt-4 md:bl-pt-8': open,
        })}
      >
        {children}
      </div>
    </div>
  );
};

export const TaskFolders = ({ tasks }: TaskTabsProps) => {
  const { t } = useTranslation();

  return (
    <div className="bl-space-y-3.5 bl-w-full md:bl-w-[942px]">
      <TaskFolder label={t('pages.userCenter.tabs.dailyTask')} defaultOpen>
        <TaskList tasks={getSortedTasks(tasks.dailyTasks)} />
      </TaskFolder>
      <TaskFolder label={t('pages.userCenter.tabs.farmingOnBitlayer')}>
        <TaskList tasks={getSortedTasks(tasks.newRacerTasks)} />
      </TaskFolder>
      <TaskFolder label="Growth Tasks">
        <TaskList tasks={getSortedTasks(tasks.advanceTasks)} />
      </TaskFolder>
    </div>
  );
};

export const EcoTaskFolders = ({ tasks }: TaskTabsProps) => {
  const { t } = useTranslation();

  useEffect(() => { 
    console.log('tasks:', tasks)
    console.log('ecoTasks:', getSortedTasks(tasks.ecoTasks))
  }, [tasks])

  return (
    <div className="bl-space-y-3.5 bl-w-full bl-justify-center bl-flex">
      <div className="md:bl-w-[942px] bl-w-full">
        {tasks.ecoTasks && (
          <TaskFolder label={t('pages.userCenter.tabs.ecoTasks')} defaultOpen>
            <TaskList tasks={getSortedTasks(tasks.ecoTasks)} />
          </TaskFolder>
        )}
      </div>
    </div>
  );
};

export const ContinuesTaskItem = ({ task }: TaskItemProps) => {
  const { toast } = useToast();
  const { onClick } = useTaskAction(task, {
    onSuccess: () => {
      const message = (
        <div className="bl-px-4 bl-text-xl bl-text-black">
          <Trans
            i18nKey="pages.userCenter.task.pointsAcquired"
            values={{ points: task.rewardPoints.toLocaleString('en-US') }}
            components={{ span: <span className="bl-text-white" /> }}
          />
        </div>
      );
      toast(message);
    },
  });

  const RewardLayer = () => {
    if (task.rewardPoints === 0) {
      return null;
    }

    return (
      <div className="bl-hidden md:bl-flex bl-flex-col bl-items-center bl-gap-1">
        <ChevronUpIcon className="bl-text-primary bl-size-5" />
        <div className="bl-flex bl-items-center bl-text-xl bl-text-white">
          <img
            src="/images/user-center/bitlayer-golden.png"
            alt="point"
            className="bl-size-4 lg:bl-size-7"
          />
          <div>+{task.rewardPoints.toLocaleString('en-US')}</div>
        </div>
      </div>
    );
  };

  const handleClick = () => {
    if (task.rewardPoints > 0) {
      onClick();
    }
  };

  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger asChild>
          <button
            className="bl-relative bl-flex bl-items-center bl-justify-center bl-size-7 lg:bl-size-10 bl-bg-[#d2d2d2]/20 focus-visible:bl-outline-none bl-duration-200 bl-text-secondary hover:bl-text-white"
            onClick={handleClick}
          >
            <BucketsIcon className="bl-size-4 lg:bl-size-7" />
            {task.rewardPoints > 0 && (
              <Dot className="bl-absolute bl-top-0 bl-right-0 bl-translate-x-1/2 -bl-translate-y-1/2" />
            )}
          </button>
        </TooltipTrigger>
        <TooltipContent
          side="bottom"
          showArrow={false}
          className="bl-border-0 bl-bg-transparent bl-py-1"
        >
          <RewardLayer />
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};

interface BadgeListProps {
  badges: TaskListData['myBadge'];
}

export const BadgeList = ({ badges }: BadgeListProps) => {
  const [scope, animate] = useAnimate();
  useEffect(() => {
    animate(
      '.badge-item',
      {
        opacity: [0, 1],
        x: [40, 0],
      },
      {
        duration: 0.5,
        delay: stagger(0.1),
      },
    );
  }, [animate]);

  return (
    <div
      ref={scope}
      className="bl-w-full bl-grid lg:bl-grid-cols-3 bl-gap-2 lg:bl-gap-4 bl-px-3 bl-pb-3 lg:bl-px-12"
    >
      {badges.map((badge) => (
        <BadgeItem key={badge.badgeId} badge={badge} />
      ))}
    </div>
  );
};

interface BindXDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function BindXDialog({ open, onOpenChange }: BindXDialogProps) {
  const { t } = useTranslation();
  const { mutate, data: url, isPending } = useBindX();

  useChange(open, (next) => {
    if (next) {
      mutate();
    }
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="md:bl-w-[460px] bl-p-0 bl-bg-primary">
        <DialogHeader className="bl-relative">
          <DialogTitle>{t(`pages.userCenter.twitter.bindTitle`)}</DialogTitle>
          <DialogCloseRounded className="bl-absolute bl-top-0 bl-right-3" />
        </DialogHeader>
        <div className="bl-px-6 bl-pt-6 bl-flex bl-flex-col bl-items-center bl-text-black">
          <XIcon className="bl-size-30 bl-mb-3" />
          <p className="bl-text-center lg:bl-text-lg">{t(`pages.userCenter.twitter.bindDesc`)}</p>
        </div>
        <DialogFooter className="bl-flex bl-flex-col bl-items-center bl-px-6">
          <Button variant="dark" className="bl-w-40" disabled={!url || isPending} asChild>
            <a href={url} target="_blank" rel="noreferrer">
              <span>{t(`pages.userCenter.twitter.bindAction`)}</span>
            </a>
          </Button>
          <p className="bl-text-white bl-text-sm bl-text-center">
            {t(`pages.userCenter.twitter.tips`)}
          </p>
        </DialogFooter>
        <CornerMarkGroup variant="dark" positions={['tl', 'bl', 'br']} />
      </DialogContent>
    </Dialog>
  );
}


export const getSortedTasks = (sourceTasks: TaskDetail[]) => {
  const _sourceTasks = sourceTasks || [];
  const doingList = _sourceTasks.filter(task => (task.status === 1 && task.isCompleted === false));
  const completedList = _sourceTasks.filter(task => task.isCompleted);
  const endedList = _sourceTasks.filter(task => (task.status === 2 && task.isCompleted === false));
  const sortedTasks = [...doingList, ...completedList, ...endedList];
  return sortedTasks;
}