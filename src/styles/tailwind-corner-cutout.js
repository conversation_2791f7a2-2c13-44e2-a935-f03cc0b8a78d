const plugin = require('tailwindcss/plugin');

module.exports = plugin(function ({ addComponents, matchUtilities, theme }) {
  addComponents({
    '@supports (mask-composite: intersect) and (not (-webkit-touch-callout: none))': {
      '.corner-cutout': {
        mask:
          'linear-gradient(calc(180deg - var(--tw-corner-cutout-deg, 45deg)), transparent var(--tw-corner-cutout-tl, 0), white var(--tw-corner-cutout-tl, 0)),' +
          'linear-gradient(calc(-180deg + var(--tw-corner-cutout-deg, 45deg)), transparent var(--tw-corner-cutout-tr, 0), white var(--tw-corner-cutout-tr, 0)),' +
          'linear-gradient(calc(-1 * var(--tw-corner-cutout-deg, 45deg)), transparent var(--tw-corner-cutout-br, 0), white var(--tw-corner-cutout-br, 0)),' +
          'linear-gradient(var(--tw-corner-cutout-deg, 45deg), transparent var(--tw-corner-cutout-bl, 0), white var(--tw-corner-cutout-bl, 0))',
        maskRepeat: 'no-repeat',
        maskComposite: 'intersect',
      },
    }
  });

  const corners = ['tl', 'tr', 'br', 'bl'];
  corners.forEach(function (corner) {
    const key = `corner-cutout-${corner}`;
    const cssVar = `--tw-corner-cutout-${corner}`;
    matchUtilities(
      {
        [key]: (value) => ({
          [cssVar]: value,
        }),
      },
      { values: theme('spacing') },
    );
  });

  matchUtilities(
    {
      'corner-cutout-size': (value) =>
        corners.reduce((acc, corner) => {
          acc[`--tw-corner-cutout-${corner}`] = value;
          return acc;
        }, {}),
    },
    { values: theme('spacing') },
  );
});
