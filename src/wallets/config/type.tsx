import { Abi, Address, Chain, ChainContract, erc20Abi } from 'viem';
export type { Chain } from 'viem';

export interface PageData<T> {
  total: number;
  list: T[];
}

export type GetHisReq = {
  pageNo: number;
  pageSize: number;
  from: string; //钱包地址
  senderAppId: number;
  receiverAppId: number;
  sourceType: string;
};

export interface BaseResp<T> {
  code: number;
  msg: string;
  data: T;
}

export type TokenType = 'erc20' | 'brc20' | 'rune' | 'native' | 'erc721' | 'erc1155';

interface ZkBridgeTokenConfig {
  poolId: number;
}

interface CCIPTokenConfig {
  receiver?: 'router' | 'eoa';
  pool: Address;
}

export interface BasicToken {
  id: string;
  name: string; //不同链的tokenName要相同
  symbol: string;
  type: TokenType;
  originalType?: TokenType;
  icon?: string | React.FC<React.SVGProps<SVGSVGElement>>;
  decimals: number; // 币种的本身的精度
  isNative: boolean;
  zkbridge?: ZkBridgeTokenConfig;
  ccip?: CCIPTokenConfig;
}

export type ERC20TokenAbi = typeof erc20Abi;

export interface ERC20Token<TAbi extends Abi = ERC20TokenAbi> extends BasicToken {
  isNative: false;
  type: 'erc20';
  contractAddress: Address;
  permitable?: boolean;
  permitVersion?: '1' | '2';
  permitName?: string;
  continuouslyApprove?: boolean;
  abi?: TAbi;
}

export interface BRC20Token extends BasicToken {
  isNative: false;
  type: 'brc20';
  tick: string;
}

export interface RuneToken extends BasicToken {
  isNative: false;
  type: 'rune';
  runeId: string;
}

export interface NativeToken extends BasicToken {
  isNative: true;
  type: 'native';
  wrappedTokens?: Record<string, ERC20Token>;
}

export type Token = NativeToken | ERC20Token | BRC20Token | RuneToken;

export enum NetworkType {
  btc = 'btc',
  evm = 'evm',
  tron = 'tron',
  sui = 'sui',
}

export interface ZkBridgeConfig {
  appId: number;
  lazyerZeroChainId?: number;
}

export interface CCIPConfig {
  selector: string;
  gasLimit?: number;
  gasLimitOverride?: (gas: number) => number;
}

export interface BaseChainType {
  id: string;
  name: string;
  aliases?: Record<string, string>;
  symbol: string;
  chain: Chain;
  icon?: string | React.FC<React.SVGProps<SVGSVGElement>>;
  testnet?: boolean;
  networkType: NetworkType;
  contracts?: { [key: string]: ChainContract };
  zkbridge?: ZkBridgeConfig;
  ccip?: CCIPConfig;
  [x: string | number | symbol]: unknown;
}

export enum AssetTypes {
  BTC = 'BTC',
  ERC20 = 'ERC20',
  ERC721 = 'ERC721',
  ERC1155 = 'ERC1155',
  NFT = 'NFT',
}
