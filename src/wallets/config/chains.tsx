import { type Chain } from 'viem';
import BTCIcon from '@/components/icons/coins/BTCIcon';
import BNBIcon from '@/components/icons/coins/BNBIcon';
import BitlayerChainIcon from '@/components/icons/BitlayerChainIcon';
import ETHIcon from '@/components/icons/coins/ETHIcon';
import TRXIcon from '@/components/icons/coins/TRXIcon';
import { BaseChainType, NetworkType } from '@/wallets/config/type';
import {
  mainnet,
  sepolia,
  bsc,
  bscTestnet as bsc_TestNet,
  arbitrum,
  avalanche,
  avalancheFuji,
  plumeMainnet as plume,
  plumeTestnet as plumeTestnetChain,
} from 'wagmi/chains';
import ARBIcon from '@/components/icons/coins/ARBIcon';
import AVAXIcon from '@/components/icons/coins/AVAXIcon';
import SUIIcon from '@/components/icons/coins/SUIIcon';
import PlumeIcon from '@/components/icons/PlumeIcon';

export const btcTestnetChain = {
  id: 1,
  rpcUrls: {
    default: { http: ['https://testnet-rpc.zkbridge.com/bitcoin'] },
  },
  name: 'Bitcoin Testnet3',
  nativeCurrency: {
    name: 'BTC',
    symbol: 'BTC',
    decimals: 8,
  },
  blockExplorers: {
    default: { name: 'Explorer', url: 'https://mempool.space/testnet' },
  },
} as const satisfies Chain;

export const btcTestnet: BaseChainType = {
  id: 'btc_testnet',
  name: 'Bitcoin Testnet3',
  symbol: 'BTC_TEST',
  chain: btcTestnetChain,
  // icon: 'https://img.zkbridge.com/jssdk/btc_test.png',
  icon: BTCIcon,
  testnet: true,
  networkType: NetworkType.btc,
};

export const btcMainnetChain = {
  id: 0,
  rpcUrls: {
    default: { http: ['https://mainnet-rpc.zkbridge.com/bitcoin'] },
  },
  name: 'Bitcoin Mainnet',
  nativeCurrency: {
    name: 'BTC',
    symbol: 'BTC',
    decimals: 8,
  },
  blockExplorers: {
    default: { name: 'Explorer', url: 'https://mempool.space' },
  },
} as const satisfies Chain;

export const btcMainnet: BaseChainType = {
  id: 'btc_mainnet',
  name: 'Bitcoin',
  symbol: 'BTC',
  chain: btcMainnetChain,
  icon: BTCIcon,
  testnet: false,
  networkType: NetworkType.btc,
};

export const bitlayerTestnetChain = {
  id: 200810,
  rpcUrls: {
    default: { http: ['https://testnet-rpc.bitlayer.org'] },
  },
  name: 'Bitlayer Testnet',
  nativeCurrency: {
    name: 'BTC',
    symbol: 'BTC',
    decimals: 18,
  },
  custom: {
    iconUrls: ['https://www.bitlayer.org/images/bitlayer_testnet.png'],
  },
  testnet: true,
  blockExplorers: {
    default: { name: 'Explorer', url: 'https://testnet.btrscan.com' },
  },
} as const satisfies Chain;

export const bitlayerTestnet: BaseChainType = {
  id: 'bitlayer_testnet',
  name: 'Bitlayer Testnet',
  symbol: 'BITLAYER_TEST',
  chain: bitlayerTestnetChain,
  icon: BitlayerChainIcon,
  testnet: true,
  networkType: NetworkType.evm,
  contracts: {
    bridge: { address: import.meta.env.VITE_BITLAYER_TESTNET_BRIDGE },
    zkBridgeLightning: { address: '******************************************' },
    ccip: { address: '******************************************' },
  },
  zkbridge: {
    appId: 128,
    lazyerZeroChainId: -1,
  },
  ccip: {
    selector: '3789623672476206327',
  },
};

export const bitlayerMainnetChain = {
  id: 200901,
  rpcUrls: {
    default: { http: ['https://rpc.bitlayer.org'] },
  },
  name: 'Bitlayer Mainnet',
  nativeCurrency: {
    name: 'BTC',
    symbol: 'BTC',
    decimals: 18,
  },
  custom: {
    iconUrls: ['https://www.bitlayer.org/images/bitlayer-icon.png'],
  },
  blockExplorers: {
    default: { name: 'Explorer', url: 'https://www.btrscan.com' },
  },
} as const satisfies Chain;

export const bitlayerMainnet: BaseChainType = {
  id: 'bitlayer_mainnet',
  name: 'Bitlayer',
  symbol: 'BITLAYER',
  chain: bitlayerMainnetChain,
  icon: BitlayerChainIcon,
  testnet: false,
  networkType: NetworkType.evm,
  contracts: {
    bridge: { address: '******************************************' },
    zkBridgeLightning: { address: '******************************************' },
    ccip: { address: '******************************************' },
  },
  zkbridge: {
    appId: 36,
  },
  ccip: {
    selector: '7937294810946806131',
  },
};

export const sepoliaTestnet: BaseChainType = {
  id: 'sepolia',
  name: 'Sepolia Testnet',
  symbol: 'SEPOLIA',
  chain: sepolia,
  icon: ETHIcon,
  testnet: true,
  networkType: NetworkType.evm,
  contracts: {
    bridge: { address: '******************************************' },
    zkBridgeLightning: { address: '******************************************' },
    ccip: { address: '******************************************' },
  },
  zkbridge: {
    appId: 119,
    lazyerZeroChainId: 10161,
  },
  ccip: {
    selector: '16015286601757825753',
  },
};

export const bscTestnet: BaseChainType = {
  id: 'bsc_testnet',
  name: 'BSC TestNet',
  symbol: 'BNB_TEST',
  chain: bsc_TestNet,
  icon: BNBIcon,
  testnet: true,
  networkType: NetworkType.evm,
};

export const bscMainnet: BaseChainType = {
  id: 'bsc',
  name: 'BNB Chain',
  symbol: 'BNB',
  chain: bsc,
  icon: BNBIcon,
  testnet: false,
  networkType: NetworkType.evm,
  contracts: {
    ccip: { address: '******************************************' },
  },
  ccip: {
    selector: '11344663589394136015',
  },
};

export const ethMainnet: BaseChainType = {
  id: 'ethereum',
  name: 'Ethereum',
  aliases: {
    // right: 'Ethereum (Beta)',
  },
  symbol: 'ETH',
  chain: mainnet,
  icon: ETHIcon,
  testnet: false,
  networkType: NetworkType.evm,
  contracts: {
    zkBridgeLightning: { address: '******************************************' },
    ccip: { address: '******************************************' },
    ccipNative: { address: '******************************************' },
  },
  zkbridge: {
    appId: 2,
  },
  ccip: {
    selector: '5009297550715157269',
    gasLimit: 200000,
    gasLimitOverride: (gas) => Math.ceil(gas / 10000) * 10000,
  },
};

export const avalancheMainnet: BaseChainType = {
  id: 'avalanche',
  name: 'Avalanche',
  symbol: 'AVAX',
  chain: avalanche,
  icon: AVAXIcon,
  testnet: false,
  networkType: NetworkType.evm,
  contracts: {
    ccip: { address: '******************************************' },
  },
  ccip: {
    selector: '6433500567565415381',
  },
};

export const avalancheTestnet: BaseChainType = {
  id: 'avalanche_testnet',
  name: 'Avalanche Testnet',
  symbol: 'AVAX_TEST',
  chain: avalancheFuji,
  icon: AVAXIcon,
  testnet: true,
  networkType: NetworkType.evm,
};

export const plumeMainnet: BaseChainType = {
  id: 'plume',
  name: 'Plume',
  symbol: 'PLUME',
  chain: plume,
  icon: PlumeIcon,
  testnet: false,
  networkType: NetworkType.evm,
  contracts: {
    ccip: { address: '0x5C4f4622AD0EC4a47e04840db7E9EcA8354109af' },
  },
  ccip: {
    selector: '17912061998839310979',
  },
};

export const plumeTestnet: BaseChainType = {
  id: 'plume_testnet',
  name: 'Plume Testnet',
  symbol: 'PLUME_TEST',
  chain: plumeTestnetChain,
  icon: PlumeIcon,
  testnet: true,
  networkType: NetworkType.evm,
};

export const tronMainnet: BaseChainType = {
  id: 'tron',
  name: 'Tron',
  symbol: 'TRON',
  chain: {
    id: 0,
    rpcUrls: {
      default: { http: ['https://api.trongrid.io'] },
    },
    name: 'Tron',
    nativeCurrency: {
      name: 'TRX',
      symbol: 'TRX',
      decimals: 6,
    },
    blockExplorers: {
      default: { name: 'Explorer', url: 'https://tronscan.org' },
    },
  },
  icon: TRXIcon,
  testnet: false,
  networkType: NetworkType.tron,
};

export const suiMainnet: BaseChainType = {
  id: 'sui',
  name: 'Sui',
  symbol: 'SUI',
  chain: {
    id: 0,
    rpcUrls: {
      default: { http: ['https://fullnode.mainnet.sui.io'] },
    },
    name: 'Sui',
    nativeCurrency: {
      name: 'SUI',
      symbol: 'SUI',
      decimals: 9,
    },
    blockExplorers: {
      default: { name: 'Explorer', url: 'https://explorer.sui.io' },
    },
  },
  icon: SUIIcon,
  testnet: false,
  networkType: NetworkType.sui,
};

export const suiTestnet: BaseChainType = {
  id: 'sui_testnet',
  name: 'Sui Testnet',
  symbol: 'SUI_TEST',
  chain: {
    id: 0,
    rpcUrls: {
      default: { http: ['https://fullnode.testnet.sui.io'] },
    },
    name: 'Sui Testnet',
    nativeCurrency: {
      name: 'SUI',
      symbol: 'SUI',
      decimals: 9,
    },
    blockExplorers: {
      default: { name: 'Explorer', url: 'https://explorer.testnet.sui.io' },
    },
  },
  icon: SUIIcon,
  testnet: true,
  networkType: NetworkType.sui,
};

export const arbitrumMainnet: BaseChainType = {
  id: 'arbitrum',
  name: 'Arbitrum',
  symbol: 'ARBITRUM',
  chain: arbitrum,
  icon: ARBIcon,
  testnet: false,
  networkType: NetworkType.evm,
};

export const chainMap: { [key: string]: BaseChainType } = {
  btc_testnet: btcTestnet,
  btc_mainnet: btcMainnet,
  bitlayer_testnet: bitlayerTestnet,
  bitlayer_mainnet: bitlayerMainnet,
  ethereum: ethMainnet,
  sepolia: sepoliaTestnet,
  tron: tronMainnet,
  bsc_testnet: bscTestnet,
  bsc_mainnet: bscMainnet,
  arbitrum: arbitrumMainnet,
  sui_mainnet: suiMainnet,
  sui_testnet: suiTestnet,
  avalanche_mainnet: avalancheMainnet,
  avalanche_testnet: avalancheTestnet,
  plume_mainnet: plumeMainnet,
  plume_testnet: plumeTestnet,
};

export const mainChainToTestChainMap: { [key: string]: BaseChainType } = {
  btc_mainnet: btcTestnet,
  bitlayer_mainnet: bitlayerTestnet,
  ethereum: sepoliaTestnet,
};

export const chainSymbolMap: { [key: string]: BaseChainType } = {
  [btcTestnet.symbol]: btcTestnet,
  [btcMainnet.symbol]: btcMainnet,
  [bitlayerTestnet.symbol]: bitlayerTestnet,
  [bitlayerMainnet.symbol]: bitlayerMainnet,
  [sepoliaTestnet.symbol]: sepoliaTestnet,
  [ethMainnet.symbol]: ethMainnet,
  [tronMainnet.symbol]: tronMainnet,
  [bscTestnet.symbol]: bscTestnet,
  [bscMainnet.symbol]: bscMainnet,
  [arbitrumMainnet.symbol]: arbitrumMainnet,
  bitlayer_testnet: bitlayerTestnet,
  bitlayer_mainnet: bitlayerMainnet,
  ethereum: ethMainnet,
  ethereum_sepolia: sepoliaTestnet,
  avalanche: avalancheMainnet,
  bsc_mainnet: bscMainnet,
  bsc_testnet: bscTestnet,
  plume: plumeMainnet,
  plume_mainnet: plumeMainnet,
};

export const chainIdMap: { [key in NetworkType]: Record<number, BaseChainType> } = {
  [NetworkType.btc]: {
    0: btcMainnet,
    1: btcTestnet,
  },
  [NetworkType.evm]: {
    [bitlayerTestnetChain.id]: bitlayerTestnet,
    [bitlayerMainnetChain.id]: bitlayerMainnet,
    [mainnet.id]: ethMainnet,
    [sepolia.id]: sepoliaTestnet,
    [avalanche.id]: avalancheMainnet,
    [bsc.id]: bscMainnet,
    [plume.id]: plumeMainnet,
  },
  [NetworkType.tron]: {
    [tronMainnet.chain.id]: tronMainnet,
  },
  [NetworkType.sui]: {
    [suiMainnet.chain.id]: suiMainnet,
    [suiTestnet.chain.id]: suiTestnet,
  },
};

export const hostChains = [bitlayerMainnet, bitlayerTestnet];
export const guestChains = [
  btcMainnet,
  ethMainnet,
  btcTestnet,
  sepoliaTestnet,
  avalancheMainnet,
  bscMainnet,
  plumeMainnet,
];

export const zkbridgeChainMap: Record<number, BaseChainType> = {
  2: ethMainnet,
  36: bitlayerMainnet,
  119: sepoliaTestnet,
  128: bitlayerTestnet,
};
