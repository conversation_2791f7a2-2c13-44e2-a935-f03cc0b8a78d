import { injected } from '@wagmi/core';
import tp from 'tp-js-sdk';
import { SwitchChainError, UserRejectedRequestError } from 'viem';

const chainIdMap: Record<number, { ns: string; alias: string }> = {
  1: { ns: 'ethereum', alias: 'eth' },
  200901: { ns: 'ethereum', alias: 'bitlayer' },
};

const switchChain = async (chainId: number) => {
  const { data } = await tp.getCurrentWallet();
  const chain = chainIdMap[chainId];
  if (!chain) {
    throw new SwitchChainError(new Error(`Unsupported chain ${chainId}`));
  }

  const { alias } = chain;
  if (data?.blockchain === alias) {
    return;
  }

  // The need to switch between different types of chains requires calling the tp-js-sdk.
  const { result } = await tp.getWallet({ walletTypes: [alias], switch: true });
  if (result === false) {
    throw new UserRejectedRequestError(new Error('User rejected to switch'));
  }
};

const connector = injected({
  target() {
    let provider: any = undefined;
    if (typeof window !== 'undefined' && window.tokenpocket?.ethereum) {
      provider = window.tokenpocket.ethereum;
    }
    return {
      id: 'pro.tokenpocket',
      name: 'TokenPocket',
      provider,
    };
  },
});

const inTokenPocketApp = () => {
  return window.navigator.userAgent.includes('TokenPocket');
};

const connectorProxyHandler = {
  get: function (target: any, prop: any, receiver: any) {
    switch (prop) {
      case 'connect':
        return async (...args: any[]) => {
          // If we are in the TokenPocket app and the ethereum provider is not available
          // we should use the tp-js-sdk to switch the chain.
          // Then the page will reload and the provider will be available.
          if (inTokenPocketApp() && !window.tokenpocket?.ethereum) {
            const { chainId } = args[0];
            await switchChain(chainId);
          }
          return Reflect.apply(target[prop], target, args);
        };
      case 'switchChain':
        return async (...args: any[]) => {
          if (inTokenPocketApp()) {
            const { chainId } = args[0];

            // The wagmi storage should only be updated if the chain switch is successful
            await switchChain(chainId);
          }
          return Reflect.apply(target[prop], target, args);
        };
    }
    return Reflect.get(target, prop, receiver);
  },
};

// The return value of injected is a function that creates a connector.
// Using a Proxy we can intercept the connector functions.
const createConnectorHandler = {
  apply(target: any, _thisArg: any, args: any[]) {
    const connector = target(...args);
    return new Proxy(connector, connectorProxyHandler);
  },
};

// Use another Proxy to intercept the creation of the connector, so we can
// inject our own logic to switch the chain if we are in the TokenPocket app.
export const tokenPocket = new Proxy(connector, createConnectorHandler);
