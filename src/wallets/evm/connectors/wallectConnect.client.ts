import { walletConnect } from 'wagmi/connectors';
import { RouterCtrl } from '@walletconnect/modal-core';
import type { WalletData } from '@walletconnect/modal-core';
import { isMobile } from 'react-device-detect';
import { atom } from 'jotai';
import { store } from '@/store';

export const defaultConnectState: string = '';
export const walletConnectStateAtom = atom(defaultConnectState);

export const getWalletConnectState = () => store.get(walletConnectStateAtom);
export const setWalletConnectState = (state: string) => store.set(walletConnectStateAtom, state);

const qrWalletMap: Record<string, WalletData> = {
  imToken: {
    id: 'ef333840daf915aafdc4a004525502d6d49d77bd9c65e0642dbaefb3c2893bef',
    name: 'imToken',
    image_id: '985994d9-15ff-4502-0c61-c27f0b65d100',
    app: {
      browser: 'https://token.im/',
      ios: 'https://apps.apple.com/us/app/imtoken2/id1384798940',
      android: 'https://play.google.com/store/apps/details?id=im.token.app',
    },
    mobile: {
      native: 'imtokenv2://',
    },
  },
  binance: {
    id: '8a0ee50d1f22f6651afcae7eb4253e52a3310b90af5daef78a8c4929a9bb99d4',
    name: 'Binance Wallet',
    image_id: 'f2a106fd-ad47-47db-28f4-f64f50918700',
    app: {
      browser: 'https://www.binance.com/en/download/',
      ios: 'https://apps.apple.com/au/app/binance-buy-bitcoin-crypto/id1436799971',
      android: 'https://play.google.com/store/apps/details?id=com.binance.dev',
    },
    mobile: {
      native: 'binancev2://',
    },
  },
};

const connector = walletConnect({
  projectId: 'af2938e2d16d40386a87b41441febf06',
  showQrModal: true,
  isNewChainsStale: false,
  qrModalOptions: {
    themeMode: 'dark',
    explorerRecommendedWalletIds: [
      '8a0ee50d1f22f6651afcae7eb4253e52a3310b90af5daef78a8c4929a9bb99d4',
      '19177a98252e07ddfc9af2083ba8e07ef627cb6103467ffebb3f8f4205fd7927',
      'ecc4036f814562b41a5268adc86270fba1365471402006302e70169465b7ac18',
      '5864e2ced7c293ed18ac35e0db085c09ed567d67346ccb6f58a0327a75137489',
      'ef333840daf915aafdc4a004525502d6d49d77bd9c65e0642dbaefb3c2893bef',
    ],
  },
});

const connectorProxyHandler = {
  get: function (target: any, prop: any, receiver: any) {
    switch (prop) {
      case 'onDisplayUri':
        return (...args: any[]) => {
          if (!isMobile) {
            const wallectConnectState = store.get(walletConnectStateAtom);
            if (wallectConnectState) {
              RouterCtrl.setData({
                Wallet: qrWalletMap[wallectConnectState],
              });
              RouterCtrl.reset('MobileQrcodeConnecting');
            }
          }
          return Reflect.apply(target[prop], target, args);
        };
    }
    return Reflect.get(target, prop, receiver);
  },
};

const createConnectorHandler = {
  apply(target: any, _thisArg: any, args: any[]) {
    const connector = target(...args);
    return new Proxy(connector, connectorProxyHandler);
  },
};

export const walletConnectConnector = new Proxy(connector, createConnectorHandler);
