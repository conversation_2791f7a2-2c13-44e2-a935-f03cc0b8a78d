import { walletConnectConnector } from './wallectConnect.client';
import { gateWallet } from './gatewallet';
import { tokenPocket } from './token-pocket.client';
import { deBox } from './deBox';
// import { uxuyWallet } from './uxuy';
// import { tomoTGWallet } from './tomo';
import { binance } from './binance.client';
import { safepal } from './safepal';

export {
  walletConnectConnector,
  tokenPocket,
  deBox,
  // uxuyWallet,
  gateWallet,
  // tomoTGWallet,
  binance,
  safepal,
};
