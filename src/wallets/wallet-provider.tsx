import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { WagmiProvider, http, createConfig, fallback, unstable_connector } from 'wagmi';
import {
  avalanche,
  bsc,
  bscTestnet,
  mainnet,
  plumeMainnet,
  plumeTestnet,
  sepolia,
} from 'wagmi/chains';
import { bitlayerMainnetChain, bitlayerTestnetChain } from '@/wallets/config/chains';
import { Provider as BTCWalletProvider } from '@/wallets/bitcoin/provider';
import {
  MartianWallet,
  OkxWallet,
  SuietWallet,
  SuiWallet,
  WalletProvider as SuiWalletProvider,
} from '@suiet/wallet-kit';
import {
  tokenPocket,
  walletConnectConnector,
  deBox,
  binance,
  safepal,
  gateWallet,
  // tomoTGWallet,
  // uxuyWallet,
} from './evm/connectors';
import { injected } from '@wagmi/core';

export const config = createConfig({
  chains: [
    avalanche,
    bitlayerMainnetChain,
    bitlayerTestnetChain,
    mainnet,
    sepolia,
    bsc,
    bscTestnet,
    plumeMainnet,
    plumeTestnet,
  ],
  multiInjectedProviderDiscovery: true,
  connectors: [
    walletConnectConnector || injected,
    tokenPocket || injected,
    deBox,
    gateWallet,
    binance ? binance() : injected,
    safepal,
    // uxuyWallet,
    // tomoTGWallet,
  ],
  transports: {
    [bitlayerTestnetChain.id]: http(),
    [bitlayerMainnetChain.id]: http(),
    [sepolia.id]: http('https://sepolia.drpc.org'),
    // [sepolia.id]: http('https://rpc.ankr.com/eth_sepolia'),
    [mainnet.id]: fallback([
      unstable_connector(injected),
      http('https://eth.llamarpc.com'),
      http('https://eth.drpc.org'),
      http('https://eth.blockrazor.xyz'),
      http('https://api.zan.top/eth-mainnet'),
    ]),
    [bsc.id]: fallback([
      unstable_connector(injected),
      http('https://bsc-pokt.nodies.app'),
      http('https://bsc-mainnet.public.blastapi.io'),
      http('https://bsc-dataseed1.defibit.io'),
      http('https://bsc.blockrazor.xyz'),
    ]),
    [bscTestnet.id]: http(),
    [avalanche.id]: http(),
    [plumeMainnet.id]: http(),
    [plumeTestnet.id]: http(),
    // [mainnet.id]: webSocket('wss://ethereum-rpc.publicnode.com'),
  },
  ssr: true,
});

declare module 'wagmi' {
  interface Register {
    config: typeof config;
  }
}

const queryClient = new QueryClient();

export default function WalletProvider({ children }: { children: React.ReactNode }) {
  return (
    <QueryClientProvider client={queryClient}>
      <WagmiProvider config={config}>
        <BTCWalletProvider>
          <SuiWalletProvider defaultWallets={[SuiWallet, OkxWallet, SuietWallet, MartianWallet]}>
            {children}
          </SuiWalletProvider>
        </BTCWalletProvider>
      </WagmiProvider>
    </QueryClientProvider>
  );
}
