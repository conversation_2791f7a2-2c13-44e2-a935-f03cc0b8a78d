import type { LoaderFunctionArgs } from '@remix-run/cloudflare';
import { json } from '@remix-run/react';

export const loader = async ({ params }: LoaderFunctionArgs) => {
  const id = params.id;

  if (id) {
    return json(
      {
        name: 'Bitlayer BitVM Badge',
        description:
          'Bitlayer BitVM Badge is a special SBT to honor users participating in the Booster Campaign. Users minting the badge are eligible to share the prize pool of BTR.',
        external_url: `https://static.bitlayer.org/binance-booster/${id}.png`,
        image: `https://static.bitlayer.org/binance-booster/${id}.png`,
        attributes: [],
      },
      {
        status: 200,
        headers: {
          'Cache-Control': 'no-store',
        },
      },
    );
  } else {
    return json({}, { status: 404 }); // Return 404 if id is not provided
  }
};
