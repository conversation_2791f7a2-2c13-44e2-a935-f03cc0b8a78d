import { useEffect, useState } from 'react';
import { AnimatePageLayout } from '@/components/ui/page';
import { Title } from '@/components/ui/title';
import { cn } from '@/lib/utils';
import { Carousel, CarouselApi, CarouselContent, CarouselItem } from '@/components/ui/carousel';
import { useMediaQuery } from '@react-hook/media-query';
import { ClientOnly } from 'remix-utils/client-only';
import { staticAsset } from '@/lib/static';
import { useBinanceBoosterMint } from '@/modules/binance-booster/useMint';
import { Button } from '@/components/ui/button';
import { ArrowLeftIcon, ArrowRightIcon, LoaderIcon } from 'lucide-react';
import { ReadableError, UserRejectedError } from '@/modules/bridge/transactions/errors';
import { useToast } from '@/hooks/toast';
import { useTranslation } from 'react-i18next';
import { json, <PERSON> } from '@remix-run/react';
import { useAccount } from 'wagmi';
import { Switch<PERSON>hainB<PERSON>on, WalletConnector } from '@/components/featured/wallet';
import { chain } from '@/modules/user-center/config';
import { ClockOutlineIcon } from '@/components/icons/ClockIcon';
import Countdown, { zeroPad } from 'react-countdown';
import { fromUnixTime } from 'date-fns';
import GroupIcon from '@/components/icons/Group';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);

type BadgeItemData = {
  title: string;
  image: string;
  desc: string;
  tokenId: bigint;
  availableAt: number;
};

const FinishedTime = '2025-07-30T09:00:00Z'

// Carousel data for the 5 items
const badgesData: BadgeItemData[] = [
  {
    title: 'Bitlayer BitVM x BNB Chain',
    image: staticAsset('/images/tokens/bitvm-bnb.52ef3701de.png'),
    desc: `Bitlayer BitVM x BNB Chain Badge is a special SBT to honor users participating in the Bitlayer BitVM & Binance Wallet Booster Campaign. Users minting the badge are eligible to share the prize pool of BTR.`,
    tokenId: 0n,
    availableAt: dayjs.utc('2025-07-24T00:00:00Z').valueOf(),
  },
  {
    title: 'Bitlayer BitVM x Sui',
    image: staticAsset('/images/tokens/bitvm-sui.6a1db2f37f.png'),
    desc: `Bitlayer BitVM x Sui Badge is a special SBT to honor users participating in the Bitlayer BitVM & Binance Wallet Booster Campaign. Users minting the badge are eligible to share the prize pool of BTR.`,
    tokenId: 1n,
    availableAt: dayjs.utc('2025-07-25T00:00:00Z').valueOf(),
  },
  {
    title: 'Bitlayer BitVM x Avalanche',
    image: staticAsset('/images/tokens/bitvm-avalanche.de0ff98673.png'),
    desc: `Bitlayer BitVM x Avalanche Badge is a special SBT to honor users participating in the Bitlayer BitVM & Binance Wallet Booster Campaign. Users minting the badge are eligible to share the prize pool of BTR.`,
    tokenId: 2n,
    availableAt: dayjs.utc('2025-07-26T00:00:00Z').valueOf(),
  },
  {
    title: 'Bitlayer BitVM x Plume',
    image: staticAsset('/images/tokens/bitvm-plume.550bee642a.png'),
    desc: `Bitlayer BitVM x Plume Badge is a special SBT to honor users participating in the Bitlayer BitVM & Binance Wallet Booster Campaign. Users minting the badge are eligible to share the prize pool of BTR.`,
    tokenId: 3n,
    availableAt: dayjs.utc('2025-07-27T00:00:00Z').valueOf(),
  },
  {
    title: 'Bitlayer BitVM x Starknet',
    image: staticAsset('/images/tokens/bitvm-starknet.67c9153fc0.png'),
    desc: `Bitlayer BitVM x Starknet Badge is a special SBT to honor users participating in the Bitlayer BitVM & Binance Wallet Booster Campaign. Users minting the badge are eligible to share the prize pool of BTR.`,
    tokenId: 4n,
    availableAt: dayjs.utc('2025-07-28T00:00:00Z').valueOf(),
  },
];

const mobileBadges: BadgeItemData[] = [
  badgesData[0], // BNB
  badgesData[1], // Sui
  badgesData[2], // Avalanche
  badgesData[3], // Plume
  badgesData[4], // Starknet
];

const LearnMoreLink =
  'https://bitlayerlabs.notion.site/Introducing-Bitlayer-x-Binance-Wallet-Booster-Campaign-22cc4d44b10a80fe8716ca64177925cf?source=copy_link';
const HowToMintLink =
  'https://bitlayerlabs.notion.site/Tutorial-Booster-Campaign-Phase1-238c4d44b10a80fe8957e785c6a92cf6?source=copy_link';

export function loader() {
  return json({
    now: new Date().getTime(),
  });
}

// Individual Item Component
const CarouselItemBox = ({
  item,
  itemIndex,
  activeIndex,
  isActive = false,
  minted,
  onClick,
}: {
  item: BadgeItemData;
  itemIndex: number;
  activeIndex: number;
  isActive?: boolean;
  minted: boolean;
  onClick?: () => void;
}) => {
  return (
    <button
      className={cn(
        'bl-group',
        'bl-bg-black bl-border-[0.8px] bl-border-secondary',
        'bl-transition-all bl-duration-300 bl-ease-in-out',
        'bl-flex bl-flex-col bl-cursor-pointer bl-relative',
        'bl-focus:outline-none bl-focus:ring-2 bl-focus:ring-primary',
        'bl-w-[148px] bl-h-[160px] md:bl-w-[166px] md:bl-h-[180px]', // Fixed base size
        {
          'bl-scale-[1.35] bl-z-20': isActive,
        },
      )}
      onClick={onClick}
    >
      {/* Title Section */}
      <div className="bl-min-h-0 bl-shrink-0 bl-h-7.5 bl-bg-primary/20 bl-flex bl-items-center bl-justify-center bl-w-full">
        <div className="bl-text-white bl-font-medium bl-text-xs">{item.title}</div>
      </div>

      {/* Image Section */}
      <div className="bl-flex-1 bl-flex bl-items-center bl-justify-center bl-w-full">
        <div className="bl-flex bl-items-center bl-justify-center bl-w-[120px] bl-h-[120px]">
          <img
            src={item.image}
            alt={item.title}
            className="bl-w-full bl-h-full bl-object-contain"
          />
        </div>
      </div>

      {/* Minted Badge */}
      {minted ? (
        <div className="bl-text-xs/none md:bl-text-sm/none bl-text-[#040405] bl-bg-[#AEB5C5] bl-absolute bl-left-0 bl-bottom-0 bl-py-[2px] bl-px-2 bl-uppercase">
          Minted
        </div>
      ) : null}

      <div
        className={cn(
          'bl-size-[calc(100%+2px)] bl-absolute -bl-top-px -bl-left-px bl-duration-300',
          'bl-from-black bl-via-20% bl-via-black/70 bl-to-black/25 bl-pointer-events-none',
          {
            'bl-opacity-0': isActive,
            'group-hover:bl-opacity-40': !isActive,
            'bl-bg-gradient-to-l': itemIndex < activeIndex,
            'bl-bg-gradient-to-r': itemIndex > activeIndex,
          },
        )}
      ></div>
    </button>
  );
};

const isStart = (availableAt: number, startTime?: string) => {
  const _startTime = startTime ? dayjs.utc(`${startTime}T00:00:00Z`).valueOf() :  dayjs.utc(startTime).valueOf()

  return availableAt <= _startTime
}

// end time: 2025-07-14T00:00:00Z
const isFinished = () => {
  const finishedTime = dayjs.utc(FinishedTime).valueOf()
  const now = dayjs.utc().valueOf()

  return finishedTime <= now
}

const renderer = ({ completed, ...props }: any) => {
  const { days, hours, minutes, seconds } = props;
  if (completed) {
    window.location.reload();
    return <></>;
  } else {
    return (
      <div className="bl-flex bl-items-center bl-gap-2 bl-text-[#1B1E21]">
        <ClockOutlineIcon className="bl-size-6" />
        <div className="bl-flex bl-items-center md:bl-text-2xl bl-font-normal">
          {days > 0 && <span className="bl-uppercase">{days}d:</span>}
          {(days > 0 || hours > 0) && <span>{zeroPad(hours, 2)}h:</span>}
          {(days > 0 || hours > 0 || minutes > 0) && <span>{zeroPad(minutes, 2)}m:</span>}
          <span className="bl-uppercase">{zeroPad(seconds, 2)}s</span>
        </div>
      </div>
    );
  }
};

export const ToastContent = ({ children }: {children?: React.ReactNode;}) => { 
  return <div className='bl-w-full bl-flex bl-justify-center'>{children}</div>
}

const ActionButton = ({ badge }: { badge: BadgeItemData }) => {
  const { toast } = useToast();
  const { t } = useTranslation();
  const { address, chain: currentChain } = useAccount();
  const { mintAsync, isMinting, balances, allTotalSupply, maxMintAmount } = useBinanceBoosterMint();

  const onMint = async () => {
    try {
      await mintAsync(badge.tokenId);
    } catch (e: unknown) {
      if (e instanceof UserRejectedError) {
        console.warn('User rejected');
        toast(<ToastContent>User rejected</ToastContent>);
      } else if (e instanceof ReadableError) {
        let message = e.message;
        if (message === 'insufficientFunds') {
          message = 'insufficientGas';
        }
        toast(<ToastContent>{ t(`pages.bridge.errors.${message}`)}</ToastContent>);
      } else if (e instanceof Error && e.message.includes('HTTP request failed')) {
        toast(<ToastContent>{ t(`pages.bridge.errors.insufficientGas`)}</ToastContent>);
      } else {
        toast(<ToastContent>Mint Failed</ToastContent>);
      }
    }
  };

  if (!address) {
    return (
      <WalletConnector chain={chain}>
        <Button size="lg" overlayFrom="none">
          <span>{t('common.connect')}</span>
        </Button>
      </WalletConnector>
    );
  }

  if (currentChain?.id !== chain.chain.id) {
    return <SwitchChainButton size="lg" overlayFrom="none" chain={chain} />;
  }

  if (!isStart(badge.availableAt)) {
    return (
      <Button size="lg" overlayFrom="none" disabled variant="secondary">
        <Countdown date={fromUnixTime(Number(badge.availableAt) / 1000)} renderer={renderer} />
      </Button>
    );
  }

  if (balances[Number(badge.tokenId)] > 0) {
    return (
      <Button size="lg" overlayFrom="none" disabled variant="secondary">
        <span>Minted</span>
      </Button>
    );
  }

  // '2025-07-28T00:00:00+08:00'
  if (isFinished()) {
    return (
      <Button size="lg" overlayFrom="none" disabled variant="secondary">
        <span>Finished</span>
      </Button>
    );
  }

  if (allTotalSupply[Number(badge.tokenId)] >= maxMintAmount) {
    return (
      <Button size="lg" overlayFrom="none" disabled variant="secondary">
        <span>Finished</span>
      </Button>
    );
  }

  return (
    <Button size="lg" onClick={onMint} overlayFrom="none" disabled={isMinting} variant="default">
      {isMinting && <LoaderIcon className="bl-animate-spin bl-size-4.5 bl-mr-1.5" />}
      <span>Mint</span>
    </Button>
  );
};

// Desktop Layout - All items visible horizontally
const DesktopLayout = () => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const { balances } = useBinanceBoosterMint();

  const handleClickPrev = () => {
    if (currentIndex === 0) return;
    setCurrentIndex(currentIndex - 1);
  };

  const handleClickNext = () => {
    if (currentIndex === badgesData.length - 1) return;
    setCurrentIndex(currentIndex + 1);
  };

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const now = urlParams.get('now') || '';
    badgesData.forEach((badge, index) => {
      if (isStart(badge.availableAt, now)) {
        setCurrentIndex(index);
      }
    });
  }, []);

  return (
    <div className="bl-w-full">
      <div className="bl-w-full bl-flex bl-items-center bl-justify-center bl-px-8 bl-gap-12">
        <button
          className="bl-size-8 bl-rounded-full bl-bg-primary bl-flex bl-items-center bl-justify-center bl-min-w-0 bl-shrink-0"
          onClick={handleClickPrev}
        >
          <ArrowLeftIcon className="bl-size-4 bl-text-black" />
        </button>
        <div className="bl-flex bl-items-center bl-justify-center bl-gap-0.5 bl-max-w-7xl bl-relative">
          {badgesData.map((item, index) => (
            <div key={index} className="bl-relative bl-flex bl-justify-center bl-items-center">
              <CarouselItemBox
                item={item}
                itemIndex={index}
                activeIndex={currentIndex}
                isActive={currentIndex === index}
                onClick={() => setCurrentIndex(index)}
                minted={balances[Number(item.tokenId)] > 0}
              />
            </div>
          ))}
        </div>
        <button
          className="bl-size-8 bl-rounded-full bl-bg-primary bl-flex bl-items-center bl-justify-center bl-min-w-0 bl-shrink-0"
          onClick={handleClickNext}
        >
          <ArrowRightIcon className="bl-size-4 bl-text-black" />
        </button>
      </div>
      <div className="bl-mx-auto bl-flex bl-flex-col bl-items-center bl-justify-center bl-mt-16">
        <p className="bl-max-w-lg bl-text-center bl-text-white bl-mb-8">
          {badgesData[currentIndex].desc}
        </p>

        <ClientOnly>{() => <ActionButton badge={badgesData[currentIndex]} />}</ClientOnly>

        <a
          href={HowToMintLink}
          className="bl-text-secondary bl-underline bl-text-base bl-mt-4 bl-capitalize"
          target="_blank"
          rel="noreferrer"
        >
          How to mint?
        </a>
      </div>
    </div>
  );
};

// Mobile Layout - Carousel with one item visible
const MobileLayout = () => {
  const [api, setApi] = useState<CarouselApi>();
  const [selectedIndex, setSelectedIndex] = useState(0);
  const { balances } = useBinanceBoosterMint();

  useEffect(() => {
    if (!api) return;

    const onSelect = () => {
      setSelectedIndex(api.selectedScrollSnap());
    };

    api.on('select', onSelect);
    onSelect();

    const urlParams = new URLSearchParams(window.location.search);
    const now = urlParams.get('now') || '';
    mobileBadges.forEach((badge, index) => {
      if (isStart(badge.availableAt, now)) {
        api?.scrollTo(index);
      }
    });

    return () => {
      api.off('select', onSelect);
    };
  }, [api]);

  return (
    <div className="bl-w-full bl-flex bl-flex-col bl-items-center">
      <Carousel
        setApi={setApi}
        className="bl-w-full"
        opts={{
          loop: false,
          align: 'center',
        }}
      >
        <CarouselContent className="bl-mx-[calc(50%-83px)]">
          {mobileBadges.map((item, index) => (
            <CarouselItem
              key={index}
              className="bl-basis-[166px] bl-flex bl-justify-center bl-py-8 bl-pl-0"
            >
              <CarouselItemBox
                item={item}
                itemIndex={index}
                activeIndex={selectedIndex}
                isActive={selectedIndex === index}
                minted={balances[Number(item.tokenId)] > 0}
              />
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>

      {/* Dots indicator */}
      <div className="bl-flex bl-justify-center bl-gap-1.5 bl-mt-8">
        {mobileBadges.map((_, index) => (
          <button
            key={index}
            className={cn('bl-size-1.5 bl-rounded-full bl-bg-[#595959] bl-duration-300', {
              'bl-bg-primary': selectedIndex === index,
            })}
            onClick={() => api?.scrollTo(index)}
          />
        ))}
      </div>

      <p className="bl-max-w-xs bl-text-center bl-text-xs bl-text-white bl-h-20 bl-my-6">
        {mobileBadges[selectedIndex].desc}
      </p>

      <ClientOnly>{() => <ActionButton badge={mobileBadges[selectedIndex]} />}</ClientOnly>

      <a
        href={HowToMintLink}
        className="bl-text-secondary bl-underline bl-text-xs bl-mt-4 bl-capitalize"
        target="_blank"
        rel="noreferrer"
      >
        How to mint?
      </a>
    </div>
  );
};

// Main Component
const ResponsiveCarousel = () => {
  const isMobile = useMediaQuery('(max-width: 768px)');

  return <ClientOnly>{() => (isMobile ? <MobileLayout /> : <DesktopLayout />)}</ClientOnly>;
};

export default function BinanceBoosterPage() {
  return (
    <AnimatePageLayout>
      <div className="bl-min-h-screen bl-bg-[url('/images/binance-booster-bg.png')] bl-bg-center bl-bg-no-repeat bl-text-foreground">
        {/* Header Section */}
        <section className="bl-container bl-pt-32 md:bl-pt-24 bl-relative">
          <div className="bl-flex bl-flex-col bl-items-center bl-text-center">
            {/* Logos */}
            <div className="bl-flex bl-items-center bl-justify-center bl-gap-2 lg:bl-gap-5 bl-w-full bl-relative">
              <img
                src="/images/bitlayer-logo.png"
                alt="Bitlayer Logo"
                className="bl-h-5 lg:bl-h-10"
              />
              <div className="bl-w-px bl-h-10 lg:bl-h-20 bl-opacity-40 bl-bg-gradient-to-b bl-from-primary bl-via-white bl-to-transparent"></div>
              <img
                src="/images/binance-wallet.png"
                alt="Binance Wallet Logo"
                className="bl-h-5 lg:bl-h-10"
              />
              <div
                className="bl-absolute bl-bottom-0 bl-left-1/2 -bl-translate-x-1/2 bl-w-4/5 bl-h-px bl-bg-gradient-to-r bl-from-white bl-via-primary bl-to-white bl-opacity-40"
                style={{
                  maskImage:
                    'linear-gradient(to right, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 1) 50%, rgba(0, 0, 0, 0.2) 100%)',
                }}
              ></div>
            </div>

            {/* Title */}
            <Title
              variant="white"
              size="default"
              className="bl-text-3xl md:bl-text-5xl lg:bl-text-6xl bl-font-bold bl-leading-tight bl-mt-10 lg:bl-mt-16"
            >
              Bitlayer BitVM X Binance Booster Campaign
            </Title>

            {/* Subtitle */}
            <div className="bl-text-base md:bl-text-xl lg:bl-text-2xl bl-max-w-4xl bl-mt-3 lg:bl-mt-6">
              Participate in the first phase of the Booster Campaign now with Binance Wallet!
            </div>
          </div>

          <Button
            variant="outline-3"
            size="xs"
            noOverlay
            className="bl-absolute bl-top-20 bl-right-4 md:bl-top-32 md:bl-right-10 hover:bl-text-primary bl-flex bl-items-center bl-gap-2 bl-capitalize bl-group "
            asChild
          >
            <Link to={LearnMoreLink} target="_blank" rel="noreferrer">
              <GroupIcon className="bl-text-white bl-size-4 group-hover:bl-text-primary" />
              <span className="bl-text-sm/none group-hover:bl-text-primary">Learn More</span>
            </Link>
          </Button>
        </section>

        {/* Main Carousel Section */}
        <section className="lg:bl-container bl-py-8 md:bl-py-18">
          <div className="bl-flex bl-justify-center">
            <ResponsiveCarousel />
          </div>
        </section>
      </div>
    </AnimatePageLayout>
  );
}

export { ErrorBoundary } from '@/components/featured/error-boundary';
