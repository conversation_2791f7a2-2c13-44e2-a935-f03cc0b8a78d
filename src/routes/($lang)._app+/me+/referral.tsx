import LinkIcon from '@/components/icons/LinkIcon';
import QRCodeIcon from '@/components/icons/QRCodeIcon';
import { But<PERSON> } from '@/components/ui/button';
import { AnimatePageLayout } from '@/components/ui/page';
import { useManagedWallet } from '@/hooks/header';
import { useAccount } from '@/hooks/wallet/account';
import { staticAsset } from '@/lib/static';
import i18nServer from '@/modules/i18n.server';
import { commitSession, getSession } from '@/modules/session';
import { createAPIClient } from '@/modules/user-center/api.server';
import { LoginProvider, useLoginContext } from '@/modules/user-center/components/login';
import { NumberBlock } from '@/modules/user-center/components/number-block';
import { InviteInfo } from '@/modules/user-center/types';

import {
  InviteDialog,
  ReferralInfo,
  ReferralList,
  ReferralRewards,
  TaskItem,
} from '@/modules/user-center/components/referral';
import { SectionTitle } from '@/modules/user-center/components/section-title';
import { UserCenterTaskProvider } from '@/modules/user-center/components/task';
import { UserProfile } from '@/modules/user-center/components/user-profile';
import { chain } from '@/modules/user-center/config';
import { PointIcon } from '@/modules/user-center/icons/reward-icons';
import { LoaderFunctionArgs, json, redirect } from '@remix-run/cloudflare';
import { useLoaderData } from '@remix-run/react';
import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import useChange from '@react-hook/change';
import { useNavigate } from '@/components/i18n/navigate';

interface LoaderResponse {
  title: string;
  address?: string;
  profile: {
    daysOnBitlayer?: number;
    txn?: string;
    bridgedInUsd?: string;
    totalPoints?: number;
    gemsPoints?: number;
  };
  error?: string;
  inviteInfo: InviteInfo;
}

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  const token = session.get('user.token') as string | undefined;
  const address = session.get('user.address') as string | undefined;
  const t = await i18nServer.getFixedT(request);
  const title = t('navigation.links.userCenter');

  if (!token) {
    return redirect('/me');
  }

  const api = createAPIClient(token);

  try {
    const { data: userInfo } = await api.getUserInfo();
    const { data: inviteInfo } = await api.getInviteInfo();

    const error = session.get('error') as string | undefined;

    return json<LoaderResponse>(
      {
        title,
        address,
        profile: {
          daysOnBitlayer: userInfo.daysToBitlayer,
          txn: userInfo.txCount.toLocaleString('en-US'),
          bridgedInUsd: userInfo.bridgedInUsd,
          totalPoints: userInfo.totalPoints,
          gemsPoints: userInfo.gemsPoints,
        },
        error,
        inviteInfo,
      },
      {
        headers: {
          'Set-Cookie': await commitSession(session),
        },
      },
    );
  } catch (error) {
    throw new Error('Failed to load data from server');
  }
}

export default function ReferralPage() {
  useManagedWallet({ chain });
  const { address } = useAccount({ network: chain.networkType });
  const loaderData = useLoaderData<typeof loader>();

  return (
    <LoginProvider walletAddress={address} sessionAddress={loaderData.address}>
      <UserCenterTaskProvider>
        <ReferralPageMain />
      </UserCenterTaskProvider>
    </LoginProvider>
  );
}

function ReferralPageMain() {
  const { t } = useTranslation();
  const { address } = useAccount({ network: chain.networkType });
  const { isSigned } = useLoginContext();
  const loaderData = useLoaderData<typeof loader>();
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const [inviteLink, setInviteLink] = useState('');

  const data = Object.assign({}, loaderData, {
    profile: isSigned ? loaderData.profile : {},
  });

  const linkPath = `/invitation/${loaderData.inviteInfo.referCode}`;
  useEffect(() => {
    const host = window.location.origin;
    setInviteLink(`${host}${linkPath}`);
  }, [linkPath]);

  const navigate = useNavigate();
  useChange(isSigned, (current, prev) => {
    if (prev && !current) {
      navigate('/me');
    }
  });

  return (
    <AnimatePageLayout>
      <section className="bl-container lg:bl-w-[1440px] bl-pt-24">
        <div className="bl-flex bl-justify-between">
          <UserProfile address={address} daysOnBitlayer={data.profile.daysOnBitlayer} />
          <div className="bl-flex bl-gap-1.5 lg:bl-gap-3.5">
            <div className="bl-flex bl-flex-col lg:bl-flex-row bl-gap-1.5 lg:bl-gap-3.5">
              <NumberBlock label={t('pages.userCenter.txn')} value={data.profile.txn} />
              <NumberBlock
                label={t('pages.userCenter.bridged')}
                value={data.profile.bridgedInUsd ? `$${data.profile.bridgedInUsd}` : undefined}
              />
            </div>
          </div>
        </div>
      </section>
      <section className="lg:bl-container lg:bl-w-[1440px] bl-mt-2 bl-mb-12 bl-relative">
        <div className="bl-w-full bl-aspect-[2/1] lg:bl-aspect-[18/7]">
          <img
            src={staticAsset('/images/user-center/banner-referral.3f49b253b6.gif')}
            alt=""
            className="bl-size-full bl-object-cover"
          />
        </div>
        <div className="bl-absolute bl-top-0 bl-left-0 bl-size-full bl-flex bl-items-center bl-px-9 lg:bl-px-30">
          <div className="lg:bl-w-2/3">
            <h2 className="bl-text-[32px]/[110%] lg:bl-text-[88px]/[110%] bl-text-white bl-font-bold bl-uppercase bl-mb-6">
              <div>{t('pages.userCenter.invite.discover')}</div>
              <div className="bl-flex bl-items-center bl-gap-2">
                {t('pages.userCenter.invite.and')}
                <PointIcon className="bl-size-10 lg:bl-size-20 bl-inline-block" />
              </div>
            </h2>
            <InviteDialog
              inviteLink={inviteLink}
              open={inviteDialogOpen}
              onOpenChange={setInviteDialogOpen}
            >
              <Button overlayFrom="none" className="bl-w-32 bl-text-base lg:bl-w-60 lg:bl-text-lg">
                <span>{t('pages.userCenter.invite.invite')}</span>
              </Button>
            </InviteDialog>
          </div>
        </div>
      </section>
      <section className="bl-container lg:bl-w-[1440px] bl-my-12 bl-flex bl-justify-center">
        <div className="bl-w-[822px]">
          <div className="bl-w-full bl-flex bl-flex-col bl-items-center bl-mb-3 lg:bl-px-16">
            <SectionTitle className="bl-mb-8">{t('pages.userCenter.invite.refer')}</SectionTitle>
            <p className="bl-text-base bl-text-center lg:bl-text-xl bl-font-light">
              {t('pages.userCenter.invite.shareText')}
            </p>
          </div>
          <div className="bl-border-y bl-border-divider bl-py-6 bl-space-y-3 lg:bl-px-16 lg:bl-py-8 lg:bl-space-y-6">
            <ReferralInfo
              icon={QRCodeIcon}
              label="Referral Code"
              value={loaderData.inviteInfo.referCode}
            />
            <ReferralInfo icon={LinkIcon} label="Referral Link" value={inviteLink} />
          </div>
          <div className="bl-mt-6 lg:bl-px-16 lg:bl-mt-9">
            <ReferralRewards
              value={loaderData.inviteInfo.referralRewards}
              className="bl-mb-5 lg:bl-mb-10"
            />
            <TaskItem
              task={loaderData.inviteInfo.referTask}
              showIcon={false}
              showProgress={false}
            />
            <ReferralList />
          </div>
        </div>
      </section>
    </AnimatePageLayout>
  );
}
