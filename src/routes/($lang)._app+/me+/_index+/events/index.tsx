import { AnimatePageLayout } from '@/components/ui/page';
import { useCallback, useEffect } from 'react';
import { useAccount } from '@/hooks/wallet/account';
import { useMatches, useRevalidator } from '@remix-run/react';
import useChange from '@react-hook/change';
import { useLoginContext } from '@/modules/user-center/components/login';
import { chain } from '@/modules/user-center/config';
import { LoaderResponse } from '../_layout';
import GuessingCard from '@/modules/user-center/components/guessing-card';
import SuperCard from '@/modules/user-center/components/super-card';
import { EcoTaskFolders } from '@/modules/user-center/components/task';

export default function Events() {
  const { address } = useAccount({ network: chain.networkType });
  const { isSigned, login } = useLoginContext();
  const revalidator = useRevalidator();

  const matches = useMatches();
  const loaderData = matches?.find(
    (match) => match.id === 'routes/($lang)._app+/me+/_index+/_layout',
  )?.data as LoaderResponse;

  const data = Object.assign({}, loaderData, {
    profile: isSigned ? loaderData.profile : {},
  });

  const handleLogin = useCallback(() => {
    if (!address) {
      return;
    }
    login(address, {
      onSuccess: () => {
        revalidator.revalidate();
      },
    });
  }, [address, login, revalidator]);

  useEffect(() => {
    if (!isSigned) {
      return;
    }
    const revalidate = () => {
      revalidator.revalidate();
    };
    window.addEventListener('focus', revalidate);
    return () => {
      window.removeEventListener('focus', revalidate);
    };
  }, [isSigned, revalidator]);

  useChange(address, (current, prev) => {
    if (prev === undefined && current !== undefined && !isSigned) {
      handleLogin();
    }
  });

  if (!data) {
    return null;
  }

  return (
    <AnimatePageLayout>
      <div className="bl-container bl-pt-20 bl-bg-white">
        <div className="bl-flex bl-flex-col bl-justify-center bl-gap-8 md:bl-px-24">
          {/* <SuperCard /> */}
          <GuessingCard guessingList={data.guessingList} loaderData={loaderData} />
          <EcoTaskFolders tasks={data.tasks!} />
        </div>
      </div>
    </AnimatePageLayout>
  );
}
