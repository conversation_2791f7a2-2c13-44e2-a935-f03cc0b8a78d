import React, { useEffect, useState } from 'react';
import * as zod from 'zod';
import { Form, json, useActionData, useLocation, useNavigate } from '@remix-run/react';
import { ActionFunctionArgs } from '@remix-run/cloudflare';
import { zodResolver } from '@hookform/resolvers/zod';
import { getValidatedFormData, useRemixForm } from 'remix-hook-form';
import { Button } from '@/components/ui/button';
import { FormProvider } from '@/components/ui/form';

import {
  RowLayout,
  SelectField,
  SelectOption,
  SingleCheckboxField,
  TextAreaField,
  TextField,
  UploadField,
} from '@/components/ui/form-field';
import { Text } from '@/components/ui/text';
import { Title } from '@/components/ui/title';
import { createAPI } from '@/lib/api';
import { cn } from '@/lib/utils';
import CornerMark from '@/components/ui/corner-mark';
import { AnimatePageLayout } from '@/components/ui/page';
import { APIClientError, APIServerError } from '@/lib/api/client';
import i18nServer from '@/modules/i18n.server';
import { useTranslation, Trans } from 'react-i18next';
const registerI18nextKey = 'pages.readyplayone.setp.register';

/**
 * Prepare the form schema and resolver.
 *
 * This schema is used to validate the form data.
 */
const schema = zod.object({
  // project
  name: zod
    .string()
    .min(1, { message: `${registerI18nextKey}.schemaName` })
    .max(200),
  twitter: zod.string().max(200).optional(),
  website: zod
    .string()
    .min(1, { message: `${registerI18nextKey}.schemaWebsite` })
    .max(200),
  logo: zod.object({ id: zod.number().optional(), name: zod.string().optional() }).optional(),
  pitch_deck: zod.object({ id: zod.number().optional(), name: zod.string().optional() }).optional(),
  category: zod.string().optional(),
  stage: zod.string().optional(),
  description: zod.string().max(500).optional(),
  builder_type: zod.string().optional(),
  project_demo: zod
    .string()
    .min(1, { message: `${registerI18nextKey}.schemaProjectDemo` })
    .max(200)
    .optional(),

  fund_info: zod.string().max(500).optional(),
  // lock_up_address: zod
  //   .string()
  //   .min(1, { message: `${registerI18nextKey}.schemaName` })
  //   .max(200),
  // contract_address: zod
  //   .string()
  //   .min(1, { message: `${registerI18nextKey}.schemaName` })
  //   .max(200),
  // coin_address: zod
  //   .string()
  //   .min(1, { message: `${registerI18nextKey}.schemaName` })
  //   .max(200),
  // defilama_address: zod
  //   .string()
  //   .min(1, { message: `${registerI18nextKey}.schemaName` })
  //   .max(200),

  // team
  team: zod.string().max(500).optional(),

  // contact
  contact_name: zod
    .string()
    .min(1, { message: `${registerI18nextKey}.schemaContact` })
    .max(200),
  contact_email: zod.string().email(),
  contact_telegram: zod
    .string()
    .min(1, { message: `${registerI18nextKey}.schemaTelegram` })
    .max(200),
  contact_twitter: zod.string().max(200).optional(),
  agree: zod.literal(true, { required_error: `${registerI18nextKey}.schemaAgree` }),
});

// FormData is the type of the form data.
// It is used by TypeScript to infer the type of the form data.
type FormData = zod.infer<typeof schema>;

// The resolver is used to validate the form data and return the errors.
const resolver = zodResolver(schema);

export const action = async ({ request, context }: ActionFunctionArgs) => {
  const t = await i18nServer.getFixedT(request);
  const {
    errors,
    data: form,
    receivedValues: defaultValues,
  } = await getValidatedFormData<FormData>(request, resolver);

  // await new Promise((resolve) => setTimeout(resolve, 5000));

  if (!form) {
    return json({
      errors,
      defaultValues,
      error: t(`${registerI18nextKey}.formValidError`),
    });
  }
  console.log('form data', form);

  const project = {
    name: form.name,
    twitter: form.twitter,
    website: form.website,
    logo: form.logo?.id,
    category: form.category || undefined,
    stage: form.stage || undefined,
    description: form.description,
    team_information: form.team,
    contact_name: form.contact_name,
    contact_email: form.contact_email,
    contact_telegram: form.contact_telegram,
    contact_twitter: form.contact_twitter,
    // lock_up_address: form.lock_up_address,
    // contract_address: form.contract_address,
    // coin_address: form.coin_address,
    // defilama_address: form.defilama_address,
    builder_type: form.builder_type,
    pitch_deck: form.pitch_deck?.id,
    project_demo: form.project_demo,
    fund_info: form.fund_info,
  };

  try {
    const api = await createAPI(context);
    const { data } = await api.strapi.createProject(project);
    console.log('created project data', data);
    return json({ code: 0, defaultValues, error: '' });
  } catch (e) {
    console.error(e);
    if (e instanceof APIClientError) {
      return json({
        code: 1,
        defaultValues,
        error: t(`${registerI18nextKey}.apiClientError`, { code: e.code }),
      });
    } else if (e instanceof APIServerError) {
      return json({
        code: -1,
        defaultValues,
        error: t(`${registerI18nextKey}.apiServerError`, { code: e.code }),
      });
    }
  }
};

const FormSection = ({
  children,
  index,
  title,
  className,
}: {
  index?: string;
  title?: string;
  children?: React.ReactNode;
  className?: string;
}) => {
  return (
    <section
      className={cn(
        'bl-w-full bl-p-4 md:bl-p-7 bl-space-y-8 bl-border-b bl-border-divider',
        className,
      )}
    >
      {index && title && (
        <div className="bl-text-center">
          <Title variant="default" size="xs">
            {index}
          </Title>
          <Text variant="white" size="lg">
            {title}
          </Text>
        </div>
      )}
      {children}
    </section>
  );
};

interface ExpandedButtonContentProps {
  submitted: boolean;
  tokens?: string;
  error?: string;
}

const ExpandedButtonContent = ({ submitted, error }: ExpandedButtonContentProps) => {
  return (
    <div className="bl-text-white bl-flex bl-w-full bl-h-full bl-overflow-hidden bl-items-center bl-justify-center bl-relative">
      <div className="bl-relative bl-z-10 bl-px-4 md:bl-px-36 bl-whitespace-normal">
        {submitted ? (
          error && <Text variant="error">{error}</Text>
        ) : (
          <div className="bl-text-white">Submitting...</div>
        )}
      </div>
      <img
        src="/images/bitlayer.gif"
        className="bl-h-[194px] bl-aspect-[37/33] bl-opacity-30 bl-absolute bl-top-1/2 bl-left-1/2 -bl-translate-x-1/2 -bl-translate-y-1/2"
        alt="bitlayer"
      />
    </div>
  );
};

export default function ParticipatePage() {
  const { t } = useTranslation('', { keyPrefix: registerI18nextKey });

  useEffect(() => {
    const element = document.getElementById('register-step');
    element?.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    });
  }, []);

  const { state } = useLocation();
  const data = useActionData<typeof action>();
  const form = useRemixForm<FormData>({
    mode: 'onSubmit',
    resolver,
    defaultValues: {
      name: '',
      twitter: '',
      website: '',
      logo: {},
      pitch_deck: {},
      category: '',
      stage: '',
      description: '',
      team: '',
      contact_name: '',
      contact_email: '',
      contact_telegram: '',
      contact_twitter: '',
      builder_type: state?.type === 'experienced' ? 'Experienced Builder' : 'New Builder',
    },
  });

  const { formState } = form;
  const categorySelect: string[] = t('formOne.category.selects', { returnObjects: true });
  const stageSelect: string[] = t('formOne.stage.selects', { returnObjects: true });
  const navigate = useNavigate();
  const [submitted, setSubmitted] = useState(false);
  const [expanded, setExpanded] = useState(false);

  useEffect(() => {
    if (formState.isValid && formState.isSubmitting) {
      setExpanded(true);
    }
  }, [formState]);

  useEffect(() => {
    if (!data) {
      return;
    }
    setSubmitted(true);
    if (!data?.error) {
      navigate('/airdrop/ready-player-one/steps/launch');
    }
  }, [data]);

  const handleReturn = () => {
    setExpanded(false);
    setSubmitted(false);
  };

  return (
    <AnimatePageLayout>
      <section
        id="register-step"
        className="bl-w-screen bl-flex bl-flex-col bl-items-center bl-justify-center bl-text-left"
      >
        <FormProvider {...form}>
          <Form
            method="post"
            onSubmit={form.handleSubmit}
            className="bl-w-full md:bl-w-[900px] bl-space-y-8 bl-border bl-border-divider bl-relative"
            style={{
              background:
                'linear-gradient(169deg, rgba(31, 32, 34, 0.70) -0.22%, rgba(0, 0, 0, 0.70) 91.66%), #080808',
            }}
          >
            <FormSection index="01" title={t('formOne.title')}>
              <SelectField
                name="builder_type"
                label={t('formOne.builderType.label')}
                placeholder={t('formOne.builderType.placeholder')}
                control={form.control}
                disabled={formState.isSubmitting || submitted}
                options={[
                  {
                    value: 'New Builder',
                    label: t('newBuilderType'),
                  },
                  {
                    value: 'Experienced Builder',
                    label: t('experiencedBuilderType'),
                  },
                ]}
              />
              <RowLayout>
                <TextField
                  name="name"
                  label={t('formOne.name.label')}
                  placeholder={t('formOne.name.placeholder')}
                  spellCheck={false}
                  autoComplete="off"
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                  required
                />
                <TextField
                  name="twitter"
                  label={t('formOne.twitter.label')}
                  placeholder={t('formOne.twitter.placeholder')}
                  spellCheck={false}
                  autoComplete="off"
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                />
              </RowLayout>

              <RowLayout>
                <TextField
                  name="website"
                  label={t('formOne.website.label')}
                  placeholder={t('formOne.website.placeholder')}
                  spellCheck={false}
                  autoComplete="off"
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                  required
                />
                <UploadField
                  name="logo"
                  label={
                    <Trans
                      i18nKey={`${registerI18nextKey}.formOne.logo.label`}
                      components={{
                        span: <span className="bl-ml-2 bl-text-secondary/50" />,
                      }}
                    />
                  }
                  placeholder={t('formOne.logo.placeholder')}
                  accept="image/*"
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                />
              </RowLayout>

              <RowLayout>
                <TextAreaField
                  name="description"
                  className="md:bl-col-span-2"
                  inputClassName="bl-resize-none"
                  label={t('formOne.description.label')}
                  placeholder={t('formOne.description.placeholder')}
                  spellCheck={true}
                  autoComplete="off"
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                />
              </RowLayout>
              <RowLayout>
                <TextAreaField
                  name="fund_info"
                  className="md:bl-col-span-2"
                  inputClassName="bl-resize-none"
                  label={t('formOne.fundInfo.label')}
                  placeholder={t('formOne.fundInfo.placeholder')}
                  spellCheck={true}
                  autoComplete="off"
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                />
              </RowLayout>

              <RowLayout>
                <SelectField
                  name="category"
                  label={t('formOne.category.label')}
                  placeholder={t('formOne.category.placeholder')}
                  options={categorySelect.map(
                    (value: string) => ({ value, label: value }) as SelectOption,
                  )}
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                />
                <SelectField
                  name="stage"
                  label={t('formOne.stage.label')}
                  placeholder={t('formOne.stage.placeholder')}
                  options={stageSelect.map(
                    (value: string) => ({ value, label: value }) as SelectOption,
                  )}
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                />
              </RowLayout>
              <RowLayout>
                <TextField
                  name="project_demo"
                  label={t('formOne.projectDemo.label')}
                  placeholder={t('formOne.projectDemo.placeholder')}
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                />
                <UploadField
                  name="pitch_deck"
                  label={
                    <Trans
                      i18nKey={`${registerI18nextKey}.formOne.pitchDeck.label`}
                      components={{
                        span: <span className="bl-ml-2 bl-text-secondary/50" />,
                      }}
                    />
                  }
                  placeholder={t('formOne.pitchDeck.placeholder')}
                  accept=".pdf"
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                />
              </RowLayout>
              {/* <RowLayout>
                <TextField
                  name="lock_up_address"
                  label={
                    <Trans
                      i18nKey={`${registerI18nextKey}.formOne.lockUp.label`}
                      components={{
                        span: <span className="bl-ml-2 bl-text-secondary/50" />,
                      }}
                    />
                  }
                  placeholder={t('formOne.lockUp.placeholder')}
                  spellCheck={false}
                  autoComplete="off"
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                  required
                />
                <TextField
                  name="contract_address"
                  label={
                    <Trans
                      i18nKey={`${registerI18nextKey}.formOne.contract.label`}
                      components={{
                        span: <span className="bl-ml-2 bl-text-secondary/50" />,
                      }}
                    />
                  }
                  placeholder={t('formOne.contract.placeholder')}
                  spellCheck={false}
                  autoComplete="off"
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                  required
                />
              </RowLayout> */}
              {/* <RowLayout>
                <TextField
                  name="coin_address"
                  label={
                    <Trans
                      i18nKey={`${registerI18nextKey}.formOne.coin.label`}
                      components={{
                        span: <span className="bl-ml-2 bl-text-secondary/50" />,
                      }}
                    />
                  }
                  placeholder={t('formOne.coin.placeholder')}
                  spellCheck={false}
                  autoComplete="off"
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                  required
                />
                <TextField
                  name="defilama_address"
                  label={
                    <Trans
                      i18nKey={`${registerI18nextKey}.formOne.defilama.label`}
                      components={{
                        span: <span className="bl-ml-2 bl-text-secondary/50" />,
                      }}
                    />
                  }
                  placeholder={t('formOne.defilama.placeholder')}
                  spellCheck={false}
                  autoComplete="off"
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                  required
                />
              </RowLayout> */}
            </FormSection>
            <FormSection index="02" title={t('formTwo.title')}>
              <RowLayout>
                <TextAreaField
                  name="team"
                  className="md:bl-col-span-2"
                  inputClassName="bl-resize-none"
                  label={t('formTwo.team.label')}
                  placeholder={t('formTwo.team.placeholder')}
                  spellCheck={true}
                  autoComplete="off"
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                />
              </RowLayout>
            </FormSection>
            <FormSection index="03" title={t('formThree.title')}>
              <RowLayout>
                <TextField
                  name="contact_name"
                  label={t('formThree.name.label')}
                  placeholder={t('formThree.name.placeholder')}
                  spellCheck={false}
                  autoComplete="off"
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                  required
                />
                <TextField
                  name="contact_email"
                  type="email"
                  label={t('formThree.email.label')}
                  placeholder={t('formThree.email.placeholder')}
                  spellCheck={false}
                  autoComplete="off"
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                  required
                />
              </RowLayout>
              <RowLayout>
                <TextField
                  name="contact_telegram"
                  label={t('formThree.telegram.label')}
                  placeholder={t('formThree.telegram.placeholder')}
                  spellCheck={false}
                  autoComplete="off"
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                  required
                />
                <TextField
                  name="contact_twitter"
                  label={t('formThree.twitter.label')}
                  placeholder={t('formThree.twitter.placeholder')}
                  spellCheck={false}
                  autoComplete="off"
                  control={form.control}
                  disabled={formState.isSubmitting || submitted}
                />
              </RowLayout>
            </FormSection>
            <FormSection className="bl-flex bl-flex-col bl-items-center !bl-pb-16">
              <div className="bl-flex bl-flex-col bl-items-center bl-mb-5">
                <Text variant="white" size="md">
                  {t('consent.title')}
                </Text>
                <SingleCheckboxField
                  name="agree"
                  label={t('consent.label')}
                  control={form.control}
                />
              </div>
              <Button
                variant={expanded ? 'outline' : 'secondary'}
                className={cn(
                  'bl-w-full bl-duration-500 bl-py-0 bl-transition-all disabled:bl-opacity-100',
                  {
                    'bl-h-40 bl-border-primary': expanded,
                  },
                )}
                outlineClassName={cn({
                  'bl-border-primary': expanded,
                })}
                disabled={expanded}
                type="submit"
              >
                {expanded ? (
                  <ExpandedButtonContent submitted={submitted} error={data?.error} />
                ) : (
                  <span>
                    <Trans i18nKey={'common.submit'} />
                  </span>
                )}
              </Button>
              <div
                className={cn(
                  'bl-w-full bl-overflow-hidden bl-h-0 bl-duration-500 bl-opacity-0 bl-transition-all',
                  {
                    'bl-h-fit bl-opacity-100': submitted && data?.error,
                  },
                )}
              >
                <Button
                  type="button"
                  variant="secondary"
                  className="bl-w-full"
                  onClick={handleReturn}
                >
                  <span>
                    <Trans i18nKey={'common.return'} />
                  </span>
                </Button>
              </div>
            </FormSection>
            <CornerMark position="tl" />
            <CornerMark position="tr" />
            <CornerMark position="bl" />
            <CornerMark position="br" />
          </Form>
        </FormProvider>
      </section>
    </AnimatePageLayout>
  );
}
