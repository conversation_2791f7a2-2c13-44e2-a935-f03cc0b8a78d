import { Button } from '@/components/ui/button';
import DiamondShape from '@/components/icons/DiamondShape';
import { AnimatePageLayout } from '@/components/ui/page';
import { estimateFeesPerGas, writeContract } from '@wagmi/core';
import { useTranslation } from 'react-i18next';
import { config } from '@/wallets/wallet-provider';
import { useAccount, useReadContract, useChainId } from 'wagmi';
// import { LoaderIcon } from 'lucide-react';
import { useDialog } from '@/hooks/dialog';
import { useParams } from '@remix-run/react';
import { Text } from '@/components/ui/text';
import CornerMark from '@/components/ui/corner-mark';
import { Title } from '@/components/ui/title';
import { Marquee } from '@/components/ui/marquee';
import OGCardABI from '@/wallets/abi/OgNft/OGCardABI.json';
import { useEffect, useState, useRef, useCallback } from 'react';
import { hashAddress } from '@/wallets/utils';
import Pause from '@/components/ui/pauseIcon';
import Play from '@/components/ui/playIcon';
import { motion } from 'framer-motion';
import { LoadingState } from '@/components/featured/page-loader';
import { useMediaQuery } from '@react-hook/media-query';
import { useAtom } from 'jotai';
import { useManagedWallet } from '@/hooks/header';
import { chainMap } from '@/wallets/config/chains';
import DiscordIcon from '@/components/icons/DiscordIcon';
import XIcon from '@/components/icons/XIcon';
import MediumIcon from '@/components/icons/MediumIcon';
import { Application } from '@splinetool/runtime';
import { cn } from '@/lib/utils';
import { ConnectWalletDialog } from '@/components/featured/wallet';
import dayjs from 'dayjs';
import WhiteDialog from './iswhiteList.dialog';
import { useCountDown } from 'ahooks';

let intervalId: ReturnType<typeof setInterval>;

export const CONTRACT_ADDRESS_TEST = '******************************************';
export const CONTRACT_ADDRESS_MAIN = '******************************************';

const DiamondGroup = ({ className }: { className?: string }) => {
  return (
    <div className={cn('bl-relative bl-flex bl-flex-nowrap bl-w-18', className)}>
      <DiamondShape className="bl-w-4 bl-absolute bl-left-0" width={17} height={16} />
      <DiamondShape className="bl-w-4 bl-absolute bl-left-3" width={17} height={16} />
      <DiamondShape className="bl-w-4 bl-absolute bl-left-6" width={17} height={16} />
      <DiamondShape className="bl-w-4 bl-absolute bl-left-9" width={17} height={16} />
      <DiamondShape className="bl-w-4 bl-absolute bl-left-12" width={17} height={16} />
      <DiamondShape className="bl-w-4 bl-absolute bl-left-15" width={17} height={16} />
    </div>
  );
};

const LoadingDiamondMarquee = () => {
  return (
    <div
      className="bl-w-full bl-h-[16px] bl-overflow-hidden"
      style={{
        WebkitMaskSize: '100% 100%',
        WebkitMaskImage: 'linear-gradient(to right, transparent, #fff,  #aaa)',
      }}
    >
      <Marquee baseVelocity={100} transformX={[0, 36]} className="bl-relative bl-text-primary">
        <DiamondGroup />
        <DiamondGroup />
      </Marquee>
    </div>
  );
};

const Loading = ({ width }: { width: string }) => {
  return (
    <div className="bl-relative bl-w-[310px] md:bl-w-[615px] bl-overflow-hidden bl-pt-12">
      <div className="bl-absolute -bl-z-10 bl-top-0 bl-left-0 bl-w-full bl-h-full bl-overflow-hidden bl-bg-gradient-to-b bl-from-[#311705]/80 bl-duration-200"></div>
      <div className="bl-absolute bl-top-0 bl-bottom-0 bl-w-full bl-flex bl-items-center bl-justify-start">
        <div className="bl-bg-[#43220E] bl-w-[286px] md:bl-w-full  bl-h-[25px] bl-flex bl-items-center bl-mx-[10px] ">
          <div className="bl-h-[16px] bl-relative bl-flex bl-items-center" style={{ width }}>
            <LoadingDiamondMarquee />
          </div>
        </div>
      </div>
      <CornerMark position="tl" className="bl-top-0 bl-left-0" />
      <CornerMark position="tr" className="bl-top-0 bl-right-0" />
      <CornerMark position="bl" className="bl-bottom-0 bl-left-0" />
      <CornerMark position="br" className="bl-bottom-0 bl-right-0" />
    </div>
  );
};

const element =  {
  href: 'https://element.market/collections/bitlayer-lucky-helmet',
  logo: '/images/element-square.png',
  title: 'Element',
}

const tradePlatform = [
  {
    href: 'https://www.okx.com/web3/marketplace/nft/collection/bitlayer/bitlayer-lucky-helmet/',
    logo: '/images/okx.svg',
    title: 'OKX',
  },
  {
    href: 'https://bitmagic.one/details/******************************************',
    logo: '/images/bitmagic.svg',
    title: 'Bitmagic',
  },
  {
    href: 'https://dapp.paddlefi.com/',
    logo: '/images/paddle.svg',
    title: 'Paddle Finance',
  },
];

export default function OgnftBanner() {
  const [isPlay, setIsPlay] = useState(false);
  const [loaded] = useAtom(LoadingState);
  const isMobile = useMediaQuery('(max-width: 640px)');
  const [loading, setLoading] = useState(false);
  const { address } = useAccount();
  const audioRef = useRef(null);
  const chainId = useChainId();
  const { open: openDialog } = useDialog();
  const [whiteListDialog, setWhiteListDialog] = useState(false);
  const [targetDate, setTargetDate] = useState<number>();
  const [mintSuccess, setMintSuccess] = useState(false);
  const [isMintting, setIsMintting] = useState(false);
  const { t } = useTranslation();
  const i18nKey = 'pages.luckyhelmet';

  const [, formattedRes] = useCountDown({
    targetDate,
  });
  const { days, hours, minutes, seconds } = formattedRes;

  const params = useParams();
  const network = params.network === 'testnet' ? 'bitlayer_testnet' : 'bitlayer_mainnet';

  const CONTRACT_ADDRESS =
    params.network === 'testnet' ? CONTRACT_ADDRESS_TEST : CONTRACT_ADDRESS_MAIN;
  const chain = chainMap[network];

  const halndleWhiteListDialog = useCallback(
    () => setWhiteListDialog(!whiteListDialog),
    [whiteListDialog],
  );

  const handleClickConnect = () => {
    openDialog({
      content: ({ close }) => (
        <ConnectWalletDialog
          title={t('common.connect')}
          description={t('common.connectDesc')}
          chain={chain}
          close={close}
        />
      ),
    });
  };

  useManagedWallet({
    chain: chain,
  });

  const { data: mintedNum = 0, refetch: fetchMintedNum } = useReadContract({
    abi: OGCardABI.abi,
    address: CONTRACT_ADDRESS,
    functionName: 'totalSupply',
    args: [],
  });

  const { data: startNum } = useReadContract({
    abi: OGCardABI.abi,
    address: CONTRACT_ADDRESS,
    functionName: 'whitelistMintStartAt',
    args: [],
  });

  const { data: endNum } = useReadContract({
    abi: OGCardABI.abi,
    address: CONTRACT_ADDRESS,
    functionName: 'whitelistMintEndAt',
    args: [],
  });

  const { data: isWhitelist = false } = useReadContract({
    abi: OGCardABI.abi,
    address: CONTRACT_ADDRESS,
    functionName: 'isWhitelist',
    args: [address],
  });

  const { data: total = 0 } = useReadContract({
    abi: OGCardABI.abi,
    address: CONTRACT_ADDRESS,
    functionName: 'MaxSupply',
    args: [],
  });

  const { data: isminted = false, refetch: fetchIsMinted } = useReadContract({
    abi: OGCardABI.abi,
    address: CONTRACT_ADDRESS,
    functionName: 'minted',
    args: [hashAddress(address)],
  });

  useEffect(() => {
    if (startNum) {
      setTargetDate(Number(startNum) * 1000);
    }
  }, [startNum]);

  const endTime = dayjs(Number(endNum) * 1000).format('YYYY-MM-DD HH:mm:ss');
  const isNotStart = dayjs(Number(startNum) * 1000).isAfter(dayjs());
  const isFinished = dayjs(endTime).isBefore(dayjs()) || (mintedNum === total && Number(total) > 0);

  const getFeesPerGas = async () => {
    const feesPerGas = await estimateFeesPerGas(config);
    return feesPerGas;
  };

  useEffect(() => {
    const url = isMobile
      ? 'https://prod.spline.design/T3dy7eto8AQ3WGWR/scene.splinecode'
      : 'https://prod.spline.design/iNyaYG4MqMw4Fmw1/scene.splinecode';
    const canvas = document.getElementById('canvas3d') as HTMLCanvasElement;
    if (canvas) {
      const app = new Application(canvas);
      app.load(url);
    }
  }, [isMobile]);

  const handlePlay = () => {
    if (audioRef.current) {
      if (isPlay) {
        audioRef.current.pause();
        setIsPlay(false);
      } else {
        audioRef.current.play();
        setIsPlay(true);
      }
    }
  };

  // warning: testnest and mainnet has different parameters
  const handleMint = async () => {
    if (isNotStart || isFinished || isMintting) {
      return;
    }
    if (isWhitelist && !isminted) {
      setLoading(true);
      setIsMintting(true);
      const feesPerGas = await getFeesPerGas();
      const baseParam = {
        abi: OGCardABI.abi,
        address: CONTRACT_ADDRESS,
        account: address,
        functionName: 'whitelistMint',
        args: [],
        chainId,
        value: BigInt(0),
      };

      const contractParam =
        params.network === 'testnet'
          ? { ...baseParam }
          : {
              ...baseParam,
              gas: 200000,
              maxFeePerGas: feesPerGas.maxFeePerGas,
              maxPriorityFeePerGas: feesPerGas.maxPriorityFeePerGas,
            };
      await writeContract(config, {
        ...contractParam,
      })
        .then(() => {
          checkUntilMinted();
        })
        .catch((err) => {
          console.log(err);
          setLoading(false);
          setIsMintting(false);
        });
    }
  };

  const showButtonText = () => {
    if (isFinished) {
      return t(`${i18nKey}.finished`);
    }
    if (isNotStart) {
      return `${days * 24 + hours}H:${minutes}M:${seconds}S`;
    }
    if (!isWhitelist) {
      return t(`${i18nKey}.whitelistOnly`);
    } else if (isminted) {
      return t(`${i18nKey}.minted`);
    } else {
      return t(`${i18nKey}.mint`);
    }
  };

  const checkUntilMinted = () => {
    intervalId = setInterval(() => {
      fetchIsMinted();
      fetchMintedNum();
    }, 1000);
  };

  useEffect(() => {
    if (isminted) {
      if (intervalId) {
        clearInterval(intervalId);
        setMintSuccess(true);
        setWhiteListDialog(true);
      }
      setLoading(false);
    }
  }, [isminted]);

  return (
    <AnimatePageLayout>
      <section className="bl-w-screen bl-min-h-[760px] bl-h-screen bl-relative bl-flex bl-flex-col bl-items-center bl-border-b-[1px] bl-border-[#363636]">
        <div
          className="bl-absolute bl-left-0 -bl-z-10 bl-w-screen bl-h-screen bl-bg-cover"
          style={{
            backgroundImage: 'url(/images/tracer/tracer_bg.gif)',
            backgroundPosition: 'center',
            WebkitMaskSize: '100% 100%',
            WebkitMaskImage: 'linear-gradient(180deg, #fff 73.35%, transparent 95.34%)',
          }}
        >
          <audio src="/music/Live-Gamer.mp3" loop ref={audioRef}>
            <track kind="captions" src="" srcLang="" label=""></track>
          </audio>
        </div>
        <div className="bl-z-10 bl-pt-[110px] md:bl-pt-0">
          <canvas id="canvas3d" className=""></canvas>
        </div>
        {loaded && (
          <motion.div
            initial={{ opacity: 0, y: 100 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: isMobile ? 0 : 0.3, type: 'tween', stiffness: 100 }}
            className="bl-absolute bl-top-[300px] md:bl-top-[430px] bl-z-[1] bl-text-center bl-flex bl-flex-col bl-justify-center bl-items-center bl-cursor-pointer"
          >
            <Title
              variant="default"
              className="bl-whitespace-nowrap bl-flex bl-flex-col bl-text-[46px] md:bl-text-[80px]"
            >
              {t(`${i18nKey}.title`)}
            </Title>
          </motion.div>
        )}

        <div className="bl-absolute bl-bottom-[100px] md:bl-bottom-[120px] bl-z-[10] bl-text-center bl-flex bl-flex-col bl-justify-center bl-items-center">
          <div className="bl-w-[338px] md:bl-w-[586px] bl-flex bl-justify-start">
            {loaded && (
              <motion.div
                initial={{ opacity: 0, y: 100 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  duration: 0.5,
                  delay: isMobile ? 0.3 : 0,
                  type: 'tween',
                  stiffness: 100,
                }}
              >
                <Text
                  className="bl-w-[64px] md:bl-w-[84px] bl-h-[28px] md:bl-h-[38px] bl-mb-[10px] md:bl-mb-[98px] bl-bg-primary btn-mask bl-rounded btn-xs bl-scale-x-[-1] bl-flex bl-items-center bl-justify-center bl-group bl-cursor-pointer"
                  onClick={handlePlay}
                >
                  <div className="bl-scale-x-[-1] bl-z-[-10] bl-flex">
                    {isPlay ? (
                      <Pause className="group-hover:bl-fill-white bl-fill-black bl-w-[22px] md:bl-w-[28px] bl-h-[22px] md:bl-h-[28px]" />
                    ) : (
                      <Play className="group-hover:bl-fill-white bl-fill-black bl-w-[16px] md:bl-w-[20px] bl-h-[22px] md:bl-h-[28px]" />
                    )}
                  </div>
                </Text>
              </motion.div>
            )}
          </div>
          {loaded && (
            <motion.div
              initial={{ opacity: 0, y: 100 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6, type: 'tween', stiffness: 100 }}
              className="bl-flex bl-flex-col bl-gap-2 bl-items-center md:bl-items-end"
            >
              <Text
                variant="secondary"
                className="bl-text-center bl-flex-col md:bl-flex-row bl-w-full bl-flex-center bl-whitespace-nowrap bl-normal-case md:bl-pt-[10px] bl-pt-[17px]"
              >
                <Text variant="secondary">
                  <span className="bl-block md:bl-inline">{t(`${i18nKey}.description`)}</span>
                  <a
                    className="bl-inline md:bl-inline bl-text-white bl-duration-200 hover:bl-text-primary"
                    href="https://medium.com/@Bitlayer/bitlayer-announces-the-launch-of-its-first-official-nft-following-mainnet-release-ae7412662b20"
                    target="_blank"
                    rel="noreferrer"
                  >
                    {t(`${i18nKey}.toGet`)}
                  </a>
                </Text>

                <div className="bl-inline-flex bl-ml-[10px] bl-pt-[10px] md:bl-pt-0">
                  <a
                    href="https://twitter.com/BitLayerLabs"
                    className="hover:bl-text-primary"
                    target="_black"
                  >
                    <XIcon
                      className="bl-w-[19px] bl-h-[19px] md:bl-w-[19px] md:bl-h-[19px]"
                      target="_black"
                      opacity={1}
                    />
                  </a>
                  <a
                    href="https://discord.gg/bitlayer"
                    className="hover:bl-text-primary"
                    target="_black"
                  >
                    <DiscordIcon
                      className="bl-w-[26px] bl-h-[19px] md:bl-w-[26px] md:bl-h-[19px] bl-mx-[10px]"
                      opacity={1}
                    />
                  </a>
                  <a
                    href="https://medium.com/@Bitlayer"
                    className="hover:bl-text-primary"
                    target="_black"
                  >
                    <MediumIcon
                      className="bl-w-[34px] bl-h-[19px] md:bl-w-[34px] md:bl-h-[19px]"
                      opacity={1}
                    />
                  </a>
                </div>
              </Text>
              <Button size="xs" variant="secondary" asChild>
                <div>
                  <span className="bl-pr-[14px] bl-block bl-text-black md:bl-inline">
                    {t(`${i18nKey}.finished`)}
                  </span>
                  <span className="bl-text-primary group-hover:bl-text-white">5000</span>
                  <span className="bl-text-black">/5000</span>
                  <span className="bl-pl-[6px] bl-block bl-text-black md:bl-inline">
                    {t(`${i18nKey}.minted`)}
                  </span>
                </div>
              </Button>
            </motion.div>
          )}
          <div>
            <motion.div
                initial={{ opacity: 0, y: 100 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.9 }}
              >
                <div className="bl-flex bl-w-full bl-items-center bl-justify-center bl-pt-8">
                  <div className="bl-w-[279px] bl-border bl-border-card-border bl-p-2 bl-items-center bl-justify-center bl-duration-200 hover:bl-border-primary">
                    <div className="bl-text-2xl bl-text-primary">{t(`${i18nKey}.trade`)}</div>
                  </div>
                </div>
                <div className="bl-w-full bl-items-center bl-justify-center bl-flex bl-flex-col md:bl-flex-row bl-gap-4 md:bl-gap-8 bl-pt-8 md:bl-pt-4">
                  {[element].map((item, index) => (
                    <a key={index} href={item.href} target="_blank" rel="noreferrer">
                      <Button
                        size="md"
                        variant="secondary"
                        asChild
                        className="bl-w-[150px] md:bl-w-[224px] md:bl-mt-[20px]"
                      >
                        <div className="bl-flex bl-flex-row bl-gap-3">
                          <img
                            src={item.logo}
                            alt={item.title}
                            className="bl-min-0 bl-shrink-0 bl-w-[25px] md:bl-w-[30px]"
                          />
                          <div>
                            <span className="bl-text-sm md:bl-text-lg">{item.title}</span>
                          </div>
                        </div>
                      </Button>
                    </a>
                  ))}
                </div>
              </motion.div>
            {loaded && (
              <motion.div
                initial={{ opacity: 0, y: 100 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.9 }}
              >
                <div className="bl-w-full bl-flex bl-justify-center bl-pt-[16px] md:bl-pt-[38px]">
                  <Loading width={(Number(mintedNum) / Number(total)) * 100 + '%'} />
                </div>
                <div className="bl-flex bl-w-full bl-items-center bl-justify-center bl-pt-8">
                  <div className="bl-w-[279px] bl-border bl-border-card-border bl-p-2 bl-items-center bl-justify-center bl-duration-200 hover:bl-border-primary">
                    <div className="bl-text-2xl bl-text-primary">{t(`${i18nKey}.trade`)}</div>
                  </div>
                </div>
                <div className="bl-w-full bl-items-center bl-justify-center bl-flex bl-flex-col md:bl-flex-row bl-gap-4 md:bl-gap-8 bl-pt-8 md:bl-pt-4">
                  {tradePlatform.map((item, index) => (
                    <a key={index} href={item.href} target="_blank" rel="noreferrer">
                      <Button
                        size="md"
                        variant="secondary"
                        asChild
                        className="bl-w-[150px] md:bl-w-[224px] md:bl-mt-[20px]"
                      >
                        <div className="bl-flex bl-flex-row bl-gap-3">
                          <img
                            src={item.logo}
                            alt={item.title}
                            className="bl-min-0 bl-shrink-0 bl-w-[20px] md:bl-w-[25px]"
                          />
                          <div>
                            <span className="bl-text-sm md:bl-text-lg">{item.title}</span>
                          </div>
                        </div>
                      </Button>
                    </a>
                  ))}
                </div>
              </motion.div>
            )}
          </div>
        </div>
        <WhiteDialog
          open={whiteListDialog}
          close={halndleWhiteListDialog}
          isWhitelist={isWhitelist as boolean}
          mintSuccess={mintSuccess}
        />
      </section>
    </AnimatePageLayout>
  );
}
