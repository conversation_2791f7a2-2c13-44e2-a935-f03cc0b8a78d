import { createAPI } from '@/lib/api';
import {
  ActionFunctionArgs,
  UploadHandler,
  json,
  unstable_createMemoryUploadHandler,
  unstable_parseMultipartFormData,
} from '@remix-run/cloudflare';

export const action = async ({ request, context }: ActionFunctionArgs) => {
  const uploadHandler: UploadHandler = (args) => {
    return unstable_createMemoryUploadHandler({
      filter: ({ filename, mimetype, encoding }) => {
        console.log({ filename, mimetype, encoding });
        return true;
      },
      maxPartSize: undefined,
    })(args);
  };

  const formData = await unstable_parseMultipartFormData(request, uploadHandler);

  const api = await createAPI(context);
  const { data } = await api.strapi.upload(formData);
  console.log({ data });
  return json({ code: 0, data });
};

export type UploadFileAction = typeof action;
