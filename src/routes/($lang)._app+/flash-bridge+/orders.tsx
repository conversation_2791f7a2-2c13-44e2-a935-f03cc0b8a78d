import { createAPI } from '@/lib/api/gas-v2';
import { LoaderFunctionArgs, json } from '@remix-run/cloudflare';

export async function loader({ request, context }: LoaderFunctionArgs) {
  const api = await createAPI(context);
  const address = new URL(request.url).searchParams.get('address');
  if (!address) {
    return json([]);
  }

  const resp = await api.getOrderList(address);
  return json(resp.data.list);
}
