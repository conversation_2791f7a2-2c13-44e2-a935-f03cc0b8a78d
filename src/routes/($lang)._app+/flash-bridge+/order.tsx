import { createAPI } from '@/lib/api/gas-v2';
import { getSession } from '@/modules/session';
import { ActionFunctionArgs, redirect } from '@remix-run/cloudflare';

export interface PaymentData {
  asset: string;
  qrcode: string;
  address: string;
}

export async function action({ request, context }: ActionFunctionArgs) {
  const session = await getSession(request);
  const api = await createAPI(context);
  const body = await request.formData();

  const key = session.get('gas.encrypt_key') as string;
  const amount = body.get('amount');
  const address = body.get('address');
  const requestId = body.get('request_id');
  const from_coin = body.get('from_coin');
  const to_coin = body.get('to_coin');
  const language = body.get('language');
  const source = body.get('source') || '';

  if (!key || !amount || !address || !requestId || !from_coin || !to_coin) {
    throw new Response(null, { status: 400, statusText: 'Bad Request' });
  }

  const orderResponse = await api.createOrder(
    {
      target: address.toString(),
      face_value: amount.toString(),
      from_coin: from_coin.toString(),
      to_coin: to_coin.toString(),
      source: source.toString(),
    },
    {
      key,
      requestId: requestId.toString(),
    },
  );
  const sourceParam = source.toString() ? `?source=${source.toString()}` : '';

  const url =
    language !== 'en'
      ? `/${language}/flash-bridge/orders/${orderResponse.data.order_no}${sourceParam}`
      : `/flash-bridge/orders/${orderResponse.data.order_no}${sourceParam}`;

  return redirect(url);
}
