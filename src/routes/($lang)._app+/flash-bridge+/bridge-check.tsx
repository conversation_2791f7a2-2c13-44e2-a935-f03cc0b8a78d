import { createAPI } from '@/lib/api/gas-v2';
import { ActionFunctionArgs, json } from '@remix-run/cloudflare';

export async function action({ request, context }: ActionFunctionArgs) {
  const api = await createAPI(context);
  const body = await request.json<{
    source: string;
    address: string;
  }>();

  const resp = await api.isBirdge({
    source: body.source,
    address: body.address,
  });

  return json(resp.data.hasBridge);
}
