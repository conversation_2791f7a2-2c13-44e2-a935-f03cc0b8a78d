import { COIN_NAME_TYPE } from '@/components/icons/coins';
import { createAPI } from '@/lib/api/gas-v2';
import { ActionFunctionArgs, json } from '@remix-run/cloudflare';

export async function action({ request, context }: ActionFunctionArgs) {
  const api = await createAPI(context);
  const body = await request.json<{
    amount: string[];
    from_coin: string;
    to_coin: string;
  }>();

  const amounts = await api.estimateAmount({
    amount: body.amount,
    from_coin: body.from_coin as COIN_NAME_TYPE,
    to_coin: body.to_coin as COIN_NAME_TYPE,
  });

  const options = amounts.data.face_values.map((value, index) => ({
    value: index,
    amount: value.face_value,
    estimated: value.target_face_value,
  }));

  return json(options);
}
