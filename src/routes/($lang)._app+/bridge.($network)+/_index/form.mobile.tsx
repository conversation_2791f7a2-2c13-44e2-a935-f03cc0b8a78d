import { useCallback, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { TextAreaField } from '@/components/ui/form-field';
import { cn } from '@/lib/utils';
import { useAccount } from '@/hooks/wallet/account';
import { UseEnsureOnChain } from '@/hooks/wallet/chains';
import { useChains, useChainTypes } from '@/modules/bridge/hooks/chains';
import { BaseChainType, NetworkType } from '@/wallets/config/type';
import { useParams } from '@remix-run/react';
import { ChevronDown, LoaderIcon } from 'lucide-react';
import { ChainIcon } from '@/components/featured/wallet';
import { isAddress } from 'viem';
import {
  validate as isBtcAddress,
  Network as ValidationBtcNetwork,
} from 'bitcoin-address-validation';
import { BlackETHIcon } from '@/components/icons/coins/ETHIcon';
import {
  PrimaryDropdownMenu,
  PrimaryDropdownMenuContent,
  PrimaryDropdownMenuItem,
  PrimaryDropdownMenuTrigger,
} from '@/components/featured/primary-downdown';
import CloseIcon from '@/components/icons/CloseIcon';
import { useScroller } from '@/hooks/scroller';
import { Trans, useTranslation } from 'react-i18next';
import {
  EstimateTip,
  FormSection,
  SwapButton,
  WalletConnector,
  chainName,
  bridgeI18nKey,
  MaintenanceTip,
} from '@/modules/bridge/components/common';
import {
  FeeText,
  MobileFromSection,
  SelectTokenField,
  TransferFormProps,
  formatTotalAmount,
} from '@/modules/bridge/components/form';
import { useHeaderWidget } from '@/hooks/header';
import { useTransferHint, useValidate, useWorkflow } from '@/modules/bridge/hooks/transfer';
import { BigNumber } from 'ethers';

const MobileChainSelectionWidget = ({
  value,
  isTarget,
  testnet,
  onChange,
}: {
  value?: string;
  isTarget?: boolean;
  testnet?: boolean;
  onChange?: (chain: BaseChainType) => void;
}) => {
  const options = useChains({ relation: 'guest', testnet, as: isTarget ? 'target' : 'source' });

  const { scrollIntoView } = useScroller();
  const handleClickEVM = () => {
    const el = document.querySelector('#third-party-bridge') as HTMLElement;
    scrollIntoView(el, { offset: -40 });
  };

  const chain = options.find((v) => v.id === value);

  return (
    <PrimaryDropdownMenu>
      <PrimaryDropdownMenuTrigger asChild>
        <button className="bl-flex bl-gap-2 bl-items-center">
          {chain && <ChainIcon icon={chain.icon} className="bl-size-6" />}
          <ChevronDown className="bl-size-4 bl-text-primary bl-duration-200 group-data-[state=open]:bl-rotate-180" />
        </button>
      </PrimaryDropdownMenuTrigger>
      <PrimaryDropdownMenuContent
        className="bl-px-0 bl-text-black bl-w-screen"
        sideOffset={16}
        cornerMarks={false}
      >
        <div className="bl-text-2xl bl-text-center bl-pb-6 bl-border-b bl-border-primary-divider">
          Select Network
        </div>
        <div className="bl-grid bl-gap-2 bl-px-5">
          {options.map((chain) => (
            <PrimaryDropdownMenuItem
              key={chain.id}
              className="bl-border-b bl-border-primary-divider"
              onClick={() => onChange?.(chain)}
            >
              <ChainIcon icon={chain.icon} className="bl-size-7" />
              <div className="bl-grow bl-text-center bl-text-lg">
                {chainName({ chain, as: isTarget ? 'right' : 'left' })}
              </div>
            </PrimaryDropdownMenuItem>
          ))}
          <PrimaryDropdownMenuItem onClick={handleClickEVM}>
            <BlackETHIcon className="bl-size-7" />
            <div className="bl-grow bl-text-center bl-text-lg">Other EVM Chains</div>
          </PrimaryDropdownMenuItem>
        </div>
        <PrimaryDropdownMenuItem className="bl-flex bl-justify-center">
          <Button
            className="bl-size-10 bl-rounded-full bl-border bl-border-black bl-text-black bl-text-base/none hover:bl-text-primary bl-px-0 bl-py-0"
            style={{ WebkitMask: 'none' }}
          >
            <CloseIcon className="bl-size-3.5" />
          </Button>
        </PrimaryDropdownMenuItem>
      </PrimaryDropdownMenuContent>
    </PrimaryDropdownMenu>
  );
};

export const MobileTransferForm = ({
  form,
  fromChain,
  fromChainType,
  leftOptions,
  rightOptions,
  toChain,
  balance,
  tokens,
  state,
  fee,
  max,
  min,
  onSubmit,
  onSwap,
}: TransferFormProps) => {
  const { t } = useTranslation('', { keyPrefix: bridgeI18nKey });
  const { address: senderAddress, chainId } = useAccount({ network: fromChain?.networkType });
  const formBalance = form.watch('amount');
  const tokenId = form.watch('token');
  const token = useMemo(() => tokens.find((item) => item.id === tokenId)!, [tokenId, tokens]);
  const address = form.watch('address');
  const { setValue } = form;

  const left = form.watch('left');
  const right = form.watch('right');

  const params = useParams();
  const testnet = params.network === 'testnet';

  const [[sourceChainType]] = useChainTypes(testnet);

  useEffect(() => {
    if (!senderAddress || !fromChain || !toChain) {
      return;
    }
    if (fromChain.networkType === toChain.networkType && !address) {
      setValue('address', senderAddress);
    }
  }, [fromChain, toChain, address, senderAddress, setValue]);

  useHeaderWidget(
    useCallback(() => {
      const isTarget = sourceChainType === 'host';
      const onChange = (chain: BaseChainType) => {
        const id = chain.id;
        const field = isTarget ? 'right' : 'left';
        setValue(field, id);
      };
      return (
        <MobileChainSelectionWidget
          value={isTarget ? right : left}
          onChange={onChange}
          testnet={testnet}
          isTarget={isTarget}
        />
      );
    }, [setValue, sourceChainType, testnet, left, right]),
  );

  const { ensure } = UseEnsureOnChain();

  const workflow = useWorkflow({ from: senderAddress, fromChain, toChain, token });
  const [isAmountValid, hint1] = useValidate(workflow, {
    fromChain,
    formBalance,
    balance: balance?.value !== undefined ? BigNumber.from(balance.value) : undefined,
    token,
    fee,
    min,
    max,
    state,
    address,
  });
  const transferHint = useTransferHint();

  const [isAddressValid, hint2] = useMemo(() => {
    if (!address) {
      return [false, undefined];
    }

    let isValid = false;
    if (toChain?.networkType === NetworkType.evm && isAddress(address)) {
      isValid = true;
    } else if (toChain?.networkType === NetworkType.btc) {
      const network = toChain.testnet ? ValidationBtcNetwork.testnet : ValidationBtcNetwork.mainnet;
      isValid = isBtcAddress(address, network);
    }

    return [isValid, isValid ? undefined : t('invalidAddress')];
  }, [t, address, toChain]);

  const [canTransfer, hint] = [isAmountValid && isAddressValid, hint1 || hint2];

  const totalAmount = useMemo(() => {
    return formatTotalAmount(formBalance, fee, token);
  }, [formBalance, fee, token]);

  const renderTransferButton = () => {
    const text = transferHint ? t(transferHint) : hint;
    return (
      <Button
        variant="secondary"
        size="lg"
        className="bl-w-full bl-h-9 bl-text-base"
        type="submit"
        disabled={!canTransfer}
      >
        <div className="bl-flex bl-items-center bl-gap-2">
          {state === 'loading' && <LoaderIcon className="bl-size-6 bl-animate-spin" />}
          {text ? text : t('transfer')}
        </div>
      </Button>
    );
  };

  const renderSwitchButton = () => {
    return (
      <Button
        type="button"
        variant="outlineError"
        size="lg"
        overlayVariant="secondary"
        className={cn('bl-w-full bl-h-9 bl-text-base', 'hover:bl-border-primary')}
        outlineClassName="group-hover/button:bl-border-primary"
        onClick={() => ensure({ chain: fromChain })}
      >
        <span>
          <Trans i18nKey="common.switchChain" values={{ chainName: fromChain?.name }} />
        </span>
      </Button>
    );
  };

  const renderActionButton = () => {
    if (!senderAddress) {
      return (
        <WalletConnector chain={fromChain}>
          <Button
            variant="default"
            overlayVariant="outline"
            size="lg"
            className="bl-w-full bl-h-9 bl-text-base bl-text-white"
            type="button"
          >
            <span>
              <Trans i18nKey="common.connect" />
            </span>
          </Button>
        </WalletConnector>
      );
    }

    if (fromChain && fromChain.chain.id !== chainId) {
      return renderSwitchButton();
    }
    return renderTransferButton();
  };

  return (
    <form className="bl-space-y-2.5" onSubmit={onSubmit}>
      <MobileFromSection
        form={form}
        chain={fromChain}
        balance={balance}
        token={token}
        after={
          <div className="bl-absolute -bl-bottom-6 bl-z-10 bl-left-1/2 -bl-translate-x-1/2 bl-rotate-90">
            <SwapButton onSwap={onSwap} fromChain={fromChain} chain={toChain} />
          </div>
        }
      >
        <SelectTokenField
          form={form}
          name="token"
          control={form.control}
          current={token}
          currentChain={fromChainType === 'host' ? toChain : fromChain}
          fromChains={leftOptions}
          toChains={rightOptions}
        />
      </MobileFromSection>
      <FormSection>
        <div className="bl-text-base/5 bl-flex bl-justify-between">
          <div className="bl-flex bl-items-center bl-gap-1">
            <span className="bl-text-secondary bl-opacity-60">{t('to')}</span>
            <span className="bl-text-primary">
              {toChain && chainName({ chain: toChain, as: 'right' })}
            </span>
            <ChainIcon icon={toChain?.icon} className="bl-size-6" />
          </div>
          <EstimateTip network={toChain?.networkType} fromChain={fromChain} toChain={toChain} />
        </div>
        <TextAreaField
          name="address"
          control={form.control}
          placeholder={t('receivepPlaceholder', { chainName: toChain ? toChain.name : '' })}
          className="bl-w-full"
          inputClassName="bl-min-h-4 bl-py-0 bl-text-base/5 placeholder:bl-font-body placeholder:bl-text-base/5 bl-px-0 bl-bg-background focus-visible:bl-text-white focus-visible:bl-bg-background bl-border-0 focus-visible:bl-ring-offset-0 focus-visible:bl-ring-0 focus-visible:bl-ring-transparent bl-transition-colors bl-resize-none"
          spellCheck={false}
          autoResize
        />
      </FormSection>
      <FormSection className="bl-px-4 bl-py-2.5 bl-text-sm/none">
        <div className="bl-space-y-1.5 bl-text-secondary">
          <dl className="bl-flex bl-space-x-1">
            <dt>{t('fee')}</dt>
            <dd className="bl-text-white">
              <FeeText fee={fee} chain={fromChain} />
            </dd>
          </dl>
          <dl className="bl-flex bl-space-x-1">
            <dt>{t('total')}:</dt>
            {formBalance && fee && token ? (
              <dd className="bl-text-primary">{totalAmount}</dd>
            ) : (
              <dd className="bl-text-white">--</dd>
            )}
          </dl>
        </div>
      </FormSection>

      <div className="bl-w-full bl-pt-3">
        {renderActionButton()}
        <MaintenanceTip tokenId={tokenId} />
      </div>
    </form>
  );
};
