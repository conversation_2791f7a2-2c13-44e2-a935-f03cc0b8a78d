import { createAPIClient } from '@/modules/btcfi/api.server';
import { json, LoaderFunctionArgs } from '@remix-run/cloudflare';

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const projectId = url.searchParams.get('project_id');
  const address = url.searchParams.get('address');

  if (!projectId || !address) {
    return json({ error: 'Invalid arguments' }, { status: 400 });
  }

  const api = createAPIClient();
  const data = await api.getBtcfiV2UserProjectReward(Number(projectId), address);

  return json(data);
}
