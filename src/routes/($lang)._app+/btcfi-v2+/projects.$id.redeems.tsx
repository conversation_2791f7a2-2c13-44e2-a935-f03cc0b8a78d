import { createBNAPIClient } from '@/modules/btcfi/api.server';
import { json, LoaderFunctionArgs } from '@remix-run/cloudflare';

export async function loader({ params, request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const { id } = params;
  const api = createBNAPIClient();

  const projectId = Number(id);
  const address = url.searchParams.get('address');

  if (!projectId || !address) {
    return json({ error: 'Invalid arguments' }, { status: 400 });
  }

  const now = Date.now();

  const withdraws = await api.getBtcfiV2UserProjectWithdraws(projectId, address);
  withdraws.list = withdraws.list.map((item) => {
    const expireAt = item.expireAt * 1000;
    const duration = now > expireAt ? 0 : expireAt - now;
    return {
      ...item,
      expireAt,
      duration,
    };
  });
  return json(withdraws.list);
}
