import { AnimatePageLayout } from '@/components/ui/page';
import { Title } from '@/components/ui/title';
import { toPercent } from '@/lib/utils';
import { ProjectCard } from '@/modules/btcfi-v2/components/project';
import { VaultsCard } from '@/modules/btcfi-v2/components/vault';
import { createAPIClient } from '@/modules/btcfi/api.server';
import { json } from '@remix-run/cloudflare';
import { useLoaderData } from '@remix-run/react';
import { useTranslation } from 'react-i18next';

export async function loader() {
  const api = createAPIClient();

  const headline = await api.getBtcfiV2Headline();
  const vaults = await api.getBtcfiV2Vaults();

  return json({
    headline,
    vaults,
  });
}

export default function BTCFiV2Page() {
  const { headline, vaults } = useLoaderData<typeof loader>();
  const { t } = useTranslation();

  return (
    <AnimatePageLayout>
      <div className="bl-w-screen bl-overflow-hidden">
        <section className="bl-container bl-flex bl-flex-col bl-justify-center bl-gap-6 bl-pt-32 bl-mb-8 bl-relative lg:bl-pt-0 lg:bl-mb-0 lg:bl-mt-20 lg:bl-w-[1140px] lg:bl-h-[450px]">
          <div
            className="bl-absolute bl-top-20 -bl-right-24 lg:bl-top-0 lg:bl-right-[-150px]"
            style={{
              maskImage:
                'linear-gradient(to right, transparent, #fff 20%, #fff 80%, transparent), linear-gradient(180deg, transparent, #fff 20%, #fff 70%, transparent)',
              maskComposite: 'intersect',
            }}
          >
            <img
              src="/images/btcfi/title-bg-01.png"
              alt=""
              className="bl-w-[300px] lg:bl-w-[724px]"
            />
          </div>
          <div className="bl-flex bl-gap-3 bl-text-2xl bl-items-center bl-relative bl-z-10">
            <Title className="bl-text-[50px] lg:bl-text-[100px]">
              <span className="bl-text-white">BTC</span>Fi
            </Title>
            <div className="bl-text-base lg:bl-text-2xl">{t('pages.btcfi.subtitle')}</div>
            <img
              src="/images/btcfi/bg-coins-01.png"
              alt=""
              className="bl-absolute -bl-z-10 bl-w-[136px] bl-h-[80px] bl-left-[224px] -bl-top-3 lg:bl-w-[277px] lg:bl-h-[178px] lg:bl-left-[420px] lg:bl-top-2"
            />
          </div>
          <div
            className="bl-bg-black/70 bl-border bl-border-card-border bl-rounded-sm bl-flex bl-items-center bl-px-4 bl-py-1 bl-gap-5 bl-w-fit bl-text-white bl-relative bl-z-10 lg:bl-h-20 lg:bl-px-10 lg:bl-gap-11"
            style={{
              backdropFilter: 'blur(4.449999809265137px)',
            }}
          >
            <div className="bl-flex bl-flex-col bl-gap-1 bl-text-[10px] lg:bl-items-center lg:bl-text-lg lg:bl-flex-row lg:bl-gap-5">
              <span>{t('pages.btcfi.tvl')}</span>
              <Title variant="white" className="bl-text-base md:bl-text-4xl bl-whitespace-nowrap">
                $ {Number(headline.tvl).toLocaleString('en-US', { minimumFractionDigits: 2 })}
              </Title>
            </div>
            <div className="bl-h-[21px] bl-w-px bl-bg-white lg:bl-h-[42px]"></div>
            <div className="bl-flex bl-flex-col bl-gap-1 bl-text-[10px] lg:bl-items-center lg:bl-text-lg lg:bl-flex-row lg:bl-gap-5">
              <span>{t('pages.btcfi.estAPY')}</span>
              <Title className="bl-text-base md:bl-text-4xl bl-whitespace-nowrap">
                {toPercent(headline.apy.min)} <span className="bl-font-mono">~</span>{' '}
                {toPercent(headline.apy.max)}
              </Title>
            </div>
          </div>
        </section>
        <section className="bl-space-y-6 bl-mb-8 lg:bl-mb-10 lg:bl-container lg:bl-w-[1140px]">
          <div className="bl-w-full bl-overflow-auto bl-px-5 lg:bl-px-0">
            <div className="bl-w-fit bl-flex bl-gap-3.5 lg:bl-w-full lg:bl-grid lg:bl-grid-cols-3 lg:bl-gap-7">
              {headline.recommend.map((project) => (
                <ProjectCard key={project.id} project={project} />
              ))}
            </div>
          </div>
        </section>
        <section className="bl-container bl-space-y-6 bl-mb-12 lg:bl-w-[1140px]">
          <VaultsCard vaults={vaults.list} />
        </section>
      </div>
    </AnimatePageLayout>
  );
}
