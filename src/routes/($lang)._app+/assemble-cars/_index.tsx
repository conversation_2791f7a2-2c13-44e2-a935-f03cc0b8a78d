import Stash, { CarType, DefaultCollectionData } from './section.stash';
import Banner from './section.banner';
import { ClientOnly } from 'remix-utils/client-only';
import { commitSession, getSession } from '@/modules/session';
import { LoaderFunctionArgs, json } from '@remix-run/cloudflare';
import { createAPIClient } from '@/modules/raffle/api.server';
import { CarPriceResponse, InfoResponse, RoiResponse } from '@/modules/raffle/types';
import { useLoaderData, useRevalidator } from '@remix-run/react';
import { LoginProvider, useLoginContext } from '@/modules/user-center/components/login';
import { useCallback, useEffect, useState } from 'react';
import { useAccount } from '@/hooks/wallet/account';
import { chain } from '@/modules/user-center/config';
import useChange from '@react-hook/change';
import { PageLoader } from '@/components/featured/page-loader';
import { cn } from '@/lib/utils';
import InviteSection from './section.invite';

interface LoaderResponse {
  address?: string;
  userInfo: InfoResponse;
  carPrice: CarPriceResponse;
  roiData: RoiResponse;
  inviteCode?: string;
}

export async function loader({ request }: LoaderFunctionArgs) {
  const session = await getSession(request);
  const token = session.get('user.token') as string | undefined;
  const address = session.get('user.address') as string | undefined;
  const url = new URL(request.url);

  const refer = url.searchParams.get('refer');
  const referExpiry = session.get('car.referExpiry') as string | undefined;

  if (referExpiry && new Date(referExpiry) < new Date()) {
    session.unset('car.refer');
    session.unset('car.referExpiry');
  }

  if (refer) {
    session.set('car.refer', refer);
    session.set('car.referExpiry', new Date(Date.now() + 1 * 1000).toISOString());
  }

  const api = createAPIClient(token);
  const { data: carPrice } = await api.getCarPrice();
  const { data: roi } = await api.getRoi();

  if (!token) {
    return json<LoaderResponse>({
      address: '0x',
      userInfo: {
        address: '',
        normalCarAmount: '0',
        premiumCarAmount: '0',
        topCarAmount: '0',
        remainFreeDrawAmount: '0',
        itemList: DefaultCollectionData,
        estimateIncome: 0,
        totalCostInUSD: 0,
        roi: 0,
        premiumAssembleUsd: 0,
        topAssembleUsd: 0,
        rebateAmountUsd: 0,
        inviteeCount: '0',
      },
      carPrice,
      roiData: roi,
    });
  }

  try {
    const { data: userInfo } = await api.getUserInfo();
    const { data: inviteInfo } = await api.getInviteInfo();
    return json<LoaderResponse>(
      {
        address,
        userInfo,
        carPrice,
        roiData: roi,
        inviteCode: inviteInfo.referCode,
      },
      {
        headers: {
          'Set-Cookie': await commitSession(session),
        },
      },
    );
  } catch (error) {
    throw new Error('Failed to load data from server');
  }
}

const MainContent = () => {
  const loaderData = useLoaderData<typeof loader>();
  const {
    normalCarAmount = '0',
    premiumCarAmount = '0',
    topCarAmount = '0',
    itemList = DefaultCollectionData,
  } = loaderData.userInfo;

  const { address, active } = useAccount({ network: chain.networkType });
  const { isSigned, isSigning, login, logout } = useLoginContext();
  const revalidator = useRevalidator();
  const [canAssemble, setCanAssemble] = useState<Record<CarType, boolean>>({
    [CarType.Normal]: false,
    [CarType.Premium]: false,
    [CarType.Top]: false,
  });
  const refresh = () => {
    revalidator.revalidate();
  };

  const handleLogin = useCallback(() => {
    if (!address) {
      return;
    }
    // console.log('login', login);
    login(address, {
      onSuccess: () => {
        revalidator.revalidate();
      },
    });
  }, [address, login, revalidator]);

  const handleLogout = useCallback(() => {
    logout(active, {
      onSuccess: () => {
        revalidator.revalidate();
      },
    });
  }, [address, logout, revalidator]);

  useEffect(() => {
    if (!isSigned) {
      return;
    }
    const revalidate = () => {
      revalidator.revalidate();
    };
    window.addEventListener('focus', revalidate);
    return () => {
      window.removeEventListener('focus', revalidate);
    };
  }, [isSigned, revalidator]);

  useChange(address, (current, prev) => {
    if ((prev === undefined && current !== undefined && !isSigned) || prev != current) {
      handleLogin();
    }
  });

  useChange(active, (current, prev) => {
    if (prev === true && current === false && isSigned) {
      handleLogout();
    }
  });

  return (
    <section className="bl-mt-[70px] md:bl-mt-20">
      <div className="md:bl-min-h-[677px]">
        <ClientOnly>
          {() => (
            <Banner
              userInfo={loaderData.userInfo}
              isSigned={isSigned}
              isSigning={isSigning}
              onLogin={handleLogin}
              refresh={refresh}
              canAssemble={canAssemble}
            />
          )}
        </ClientOnly>
      </div>
      <ClientOnly>
        {() => (
          <Stash
            isSigned={isSigned}
            isSigning={isSigning}
            onLogin={handleLogin}
            normalCarAmount={normalCarAmount}
            premiumCarAmount={premiumCarAmount}
            topCarAmount={topCarAmount}
            itemList={itemList}
            refresh={refresh}
            carPrice={loaderData.carPrice}
            userInfo={loaderData.userInfo}
            setCanAssemble={setCanAssemble}
            roiData={loaderData.roiData}
          />
        )}
      </ClientOnly>
      {/* <InviteSection
        isSigned={isSigned}
        userInfo={loaderData.userInfo}
        refresh={refresh}
        onLogin={handleLogin}
        inviteCode={loaderData.inviteCode}
      /> */}
    </section>
  );
};

export default function RafflePage() {
  const loaderData = useLoaderData<typeof loader>();
  const { address } = useAccount({ network: chain.networkType });
  const [visible, setVisible] = useState(false);

  const handleLoad = () => {
    setVisible(true);
  };

  return (
    <LoginProvider walletAddress={address} sessionAddress={loaderData.address}>
      <PageLoader
        onLoad={handleLoad}
        images={[
          '/images/user-center/raffle/10draw-bg.a0451f0a03.png',
          '/images/user-center/raffle/4_graffiti.1f43f2aead.png',
          '/images/user-center/raffle/3_graffiti.b4a4cb5f45.png',
          '/images/user-center/raffle/5_graffiti.2ecba2429a.png',
          '/images/user-center/raffle/assemble-primary.1cf63f8538.png',
          '/images/user-center/raffle/assemble-disabled.83d2125a74.png',
          '/images/user-center/raffle/assemble-hover.413986a946.png',
          '/images/user-center/raffle/car-1.5b5ee4fda4.svg',
          '/images/user-center/raffle/car-0.5b5ee4fda4.svg',
          '/images/user-center/raffle/car-2.5b5ee4fda4.svg',
          '/images/user-center/raffle/Starmohu.9a341bc181.png',
          '/images/user-center/raffle/glisten.5a66f650e1.png',
          '/images/user-center/raffle/noisy_point.8adcfcdf13.png',
          '/images/user-center/raffle/bg.8ec521ed75.png',
          '/images/user-center/raffle/dark_3.d991ee6bf7.png',
          '/images/user-center/raffle/light_3.583b0e7a11.png',
          '/images/user-center/raffle/star-3.bed2deb38f.png',
          '/images/user-center/raffle/noisy-bg.2a61deb446.png',
          '/images/user-center/raffle/star-4.b13bbfbfd3.png',
          '/images/user-center/raffle/star.3f51d40d42.png',
          '/images/user-center/raffle/star-5.a810327a13.png',
          '/images/user-center/raffle/start.3f51d40d42.png',
          '/images/user-center/raffle/collection-title.33c4eac483.png',
          '/images/user-center/raffle/title.21c22190a2.png',
          '/images/user-center/raffle/learn-more/title-m.eadc17abde.png',
          '/images/user-center/raffle/learn-more/cover-2.4bab829b3b.png',
          '/images/user-center/raffle/learn-more/cover-1.04585efe55.png',
          '/images/user-center/raffle/learn-more/cover-3.cf2b356067.png',
          '/images/user-center/raffle/learn-more/title.70c5aaba40.png',
          '/images/user-center/raffle/assemble-cars/assemble_key1_3.5b7b1bcf58.png',
          '/images/user-center/raffle/assemble-cars/assemble_key2_3.f43f54fbaf.png',
          '/images/user-center/raffle/assemble-cars/assemble_key1_5.701c96f04e.png',
          '/images/user-center/raffle/assemble-cars/assemble_key1_4.601b8bb61a.png',
          '/images/user-center/raffle/assemble-cars/assemble_key2_4.40755edb2a.png',
          '/images/user-center/raffle/assemble-cars/assemble_key2_5.a27121a459.png',
          '/images/user-center/raffle/assemble-cars/assemble_key3_5.09ff277547.png',
          '/images/user-center/raffle/assemble-cars/assemble_key3_3.ea51cb2ad0.png',
          '/images/user-center/raffle/assemble-cars/assemble_key4_3.c67993400c.png',
          '/images/user-center/raffle/assemble-cars/assemble_key3_4.eb1d7fb115.png',
          '/images/user-center/raffle/assemble-cars/assemble_key4_4.f8ce872b9e.png',
          '/images/user-center/raffle/assemble-cars/assemble_key4_5.60b71c4d5d.png',
          '/images/user-center/raffle/learn-more/three-cars-2.012d34a639.png',
          '/images/user-center/raffle/assemble-cars/car_assemble_3.2f94212eae.png',
          '/images/user-center/raffle/assemble-cars/car_assemble_5.9f7606eaf3.png',
          '/images/user-center/raffle/assemble-cars/car_got_3.a222813d3c.png',
          '/images/user-center/raffle/assemble-cars/car_got_4.bfbc992d19.png',
          '/images/user-center/raffle/assemble-cars/car_got_5.9e55911ed6.png',
          '/images/user-center/raffle/assemble-cars/car_no_4.5876b18cb4.png',
          '/images/user-center/raffle/assemble-cars/car_no_3.0c9ca6de2e.png',
          '/images/user-center/raffle/assemble-cars/car_no_5.55d497d910.png',
          '/images/user-center/raffle/assemble-cars/chassisrigged_grey_3.4b535ed8d6.png',
          '/images/user-center/raffle/assemble-cars/chassisrigged_color_4.2422134df4.png',
          '/images/user-center/raffle/assemble-cars/chassisrigged_nobg_5.fcd3a38c22.png',
          '/images/user-center/raffle/assemble-cars/chassisrigged_grey_5.de2303593a.png',
          '/images/user-center/raffle/assemble-cars/chassisrigged_color_3.15ffddd127.png',
          '/images/user-center/raffle/assemble-cars/car_assemble_4.8b59c478be.png',
          '/images/user-center/raffle/assemble-cars/chassisrigged_nobg_3.c62bea54b2.png',
          '/images/user-center/raffle/assemble-cars/chassisrigged_nobg_4.05dd576a26.png',
          '/images/user-center/raffle/assemble-cars/chassisrigged_grey_4.8f454bd827.png',
          '/images/user-center/raffle/assemble-cars/discbrake_color_3.2ebac3fff0.png',
          '/images/user-center/raffle/assemble-cars/discbrake_color_4.0b5dff23d7.png',
          '/images/user-center/raffle/assemble-cars/discbrake_grey_4.6f82e712eb.png',
          '/images/user-center/raffle/assemble-cars/discbrake_grey_5.c0088067a8.png',
          '/images/user-center/raffle/assemble-cars/discbrake_nobg_3.76f160289f.png',
          '/images/user-center/raffle/assemble-cars/discbrake_nobg_4.ef732daa76.png',
          '/images/user-center/raffle/assemble-cars/discbrake_grey_3.75ca09730a.png',
          '/images/user-center/raffle/assemble-cars/engine_color_3.1c34534338.png',
          '/images/user-center/raffle/assemble-cars/discbrake_nobg_5.7730bf301f.png',
          '/images/user-center/raffle/assemble-cars/engine_nobg_3.198aaeb3da.png',
          '/images/user-center/raffle/assemble-cars/engine_grey_4.ae0fc84a2e.png',
          '/images/user-center/raffle/assemble-cars/engine_color_4.335518c715.png',
          '/images/user-center/raffle/assemble-cars/engine_grey_3.4f48fd00ce.png',
          '/images/user-center/raffle/assemble-cars/engine_color_5.0c61418072.png',
          '/images/user-center/raffle/assemble-cars/engine_grey_5.49973b9a1c.png',
          '/images/user-center/raffle/assemble-cars/engine_nobg_4.1eeff97a96.png',
          '/images/user-center/raffle/assemble-cars/gascylinder_color_3.2ddb02b7de.png',
          '/images/user-center/raffle/assemble-cars/gascylinder_color_4.808cd2c6df.png',
          '/images/user-center/raffle/assemble-cars/gascylinder_grey_3.63da4676d1.png',
          '/images/user-center/raffle/assemble-cars/engine_nobg_5.2abef39948.png',
          '/images/user-center/raffle/assemble-cars/gascylinder_grey_4.4189f1efac.png',
          '/images/user-center/raffle/assemble-cars/gascylinder_nobg_3.2330fc31d1.png',
          '/images/user-center/raffle/assemble-cars/key1_3.d80ca803f7.png',
          '/images/user-center/raffle/assemble-cars/gascylinder_grey_5.01569c3e21.png',
          '/images/user-center/raffle/assemble-cars/key1_4.025caed96a.png',
          '/images/user-center/raffle/assemble-cars/key1_5.6be3959f39.png',
          '/images/user-center/raffle/assemble-cars/key2_5.e797faa29d.png',
          '/images/user-center/raffle/assemble-cars/key2_3.7defc18e90.png',
          '/images/user-center/raffle/assemble-cars/key3_3.39624aae23.png',
          '/images/user-center/raffle/assemble-cars/key2_4.4a7e9978dc.png',
          '/images/user-center/raffle/assemble-cars/key4_3.c931332061.png',
          '/images/user-center/raffle/assemble-cars/key3_4.5b4580008f.png',
          '/images/user-center/raffle/assemble-cars/gascylinder_nobg_4.a72427c6fa.png',
          '/images/user-center/raffle/assemble-cars/key3_5.0052d9fbfb.png',
          '/images/user-center/raffle/assemble-cars/key4_4.516fde62ae.png',
          '/images/user-center/raffle/assemble-cars/gascylinder_nobg_5.d336db1997.png',
          '/images/user-center/raffle/assemble-cars/seat_grey_3.ab57f753f1.png',
          '/images/user-center/raffle/assemble-cars/key4_5.edbdd99765.png',
          '/images/user-center/raffle/assemble-cars/seat_grey_4.8a92eb8074.png',
          '/images/user-center/raffle/assemble-cars/seat_color_4.505afa1948.png',
          '/images/user-center/raffle/assemble-cars/seat_color_3.0a6309b50a.png',
          '/images/user-center/raffle/assemble-cars/seat_grey_5.3d7dc62372.png',
          '/images/user-center/raffle/assemble-cars/seat_nobg_4.72a54df8e6.png',
          '/images/user-center/raffle/assemble-cars/seat_nobg_3.c6202797a5.png',
          '/images/user-center/raffle/assemble-cars/seat_nobg_5.dc0be7fa2b.png',
          '/images/user-center/raffle/assemble-cars/steeringwheel_grey_5.81899f42c8.png',
          '/images/user-center/raffle/assemble-cars/steeringwheel_color_3.5ab814912b.png',
          '/images/user-center/raffle/assemble-cars/steeringwheel_nobg_3.e29afebe16.png',
          '/images/user-center/raffle/assemble-cars/steeringwheel_grey_4.07ad2f881c.png',
          '/images/user-center/raffle/assemble-cars/steeringwheel_color_4.4478bb2259.png',
          '/images/user-center/raffle/assemble-cars/steeringwheel_nobg_4.501a8d0ecf.png',
          '/images/user-center/raffle/assemble-cars/truckhitch_color_3.6167ccef36.png',
          '/images/user-center/raffle/assemble-cars/truckhitch_grey_3.9d9ea28f47.png',
          '/images/user-center/raffle/assemble-cars/steeringwheel_nobg_5.a9eb7718fd.png',
          '/images/user-center/raffle/assemble-cars/truckhitch_color_4.157f0f7cbc.png',
          '/images/user-center/raffle/assemble-cars/truckhitch_grey_4.9754b9e50a.png',
          '/images/user-center/raffle/assemble-cars/steeringwheel_grey_3.5270e8ff27.png',
          '/images/user-center/raffle/assemble-cars/truckhitch_grey_5.101688bbfc.png',
          '/images/user-center/raffle/assemble-cars/truckhitch_nobg_3.5e645fa36f.png',
          '/images/user-center/raffle/assemble-cars/truckhitch_nobg_4.5198abcb78.png',
          '/images/user-center/raffle/assemble-cars/wheels_color_4.6d45f6f8a7.png',
          '/images/user-center/raffle/assemble-cars/wheels_color_3.24b3a3fc80.png',
          '/images/user-center/raffle/assemble-cars/wheels_color_5.f11fc11572.png',
          '/images/user-center/raffle/assemble-cars/wheels_grey_3.26b56381c3.png',
          '/images/user-center/raffle/assemble-cars/truckhitch_nobg_5.35c85ee4f3.png',
          '/images/user-center/raffle/assemble-cars/wheels_grey_4.a194de204f.png',
          '/images/user-center/raffle/assemble-cars/wheels_grey_5.0c06b8a6df.png',
          '/images/user-center/raffle/assemble-cars/wheels_nobg_4.2b96ef8ca3.png',
          '/images/user-center/raffle/assemble-cars/wheels_nobg_3.ef51868931.png',
          '/images/user-center/raffle/assemble-cars/wheels_nobg_5.7d811cc39b.png',
          '/images/user-center/raffle/assemble-cars/chassisrigged_color_5.c7025d639c.png',
          // '/images/user-center/raffle/learn-more/three-cars-1.d76f79782f.png',
          '/images/user-center/raffle/assemble-cars/discbrake_color_5.6d4c310fb4.png',
          '/images/user-center/raffle/assemble-cars/collection_bg_3.90f7ff2591.png',
          '/images/user-center/raffle/assemble-cars/collection_bg_4.42b6e64913.png',
          '/images/user-center/raffle/assemble-cars/gascylinder_color_5.8bb413b09b.png',
          '/images/user-center/raffle/assemble-cars/seat_color_5.77ce0a90f4.png',
          '/images/user-center/raffle/assemble-cars/truckhitch_color_5.84ab8f68e3.png',
          '/images/user-center/raffle/assemble-cars/steeringwheel_color_5.6c732a5bd8.png',
          // '/images/user-center/raffle/light_5.72a02e3f92.png',
          // '/images/user-center/raffle/dark_5.072dd43e17.png',
          '/images/user-center/raffle/assemble-cars/collection_bg_5.bd439b726d.gif',
          '/images/raffle/car_4.png',
          '/images/raffle/car_5.png',
        ]}
      />
      <div
        className={cn('bl-w-full bl-duration-200 bl-opacity-0', {
          'bl-opacity-100': visible,
        })}
      >
        <MainContent />
      </div>
    </LoginProvider>
  );
}
