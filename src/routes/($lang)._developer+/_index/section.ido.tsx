import { Button } from '@/components/ui/button';
import { CornerMarkGroup } from '@/components/ui/corner-mark';
import { useEffect, useState } from 'react';
import {
  differenceInDays,
  differenceInHours,
  differenceInMinutes,
  differenceInSeconds,
} from 'date-fns';
import { useLoaderData } from '@remix-run/react';
import { loader } from '../_index';

export function IDOSection() {
  const startTime = 1753977600000; // 2025-08-01 00:00:00 GMT+8
  const endTime = 1754582400000; // 2025-08-08 00:00:00 GMT+8

  const { now: serverTime } = useLoaderData<typeof loader>();

  const [days, setDays] = useState(0);
  const [hours, setHours] = useState(0);
  const [minutes, setMinutes] = useState(0);
  const [seconds, setSeconds] = useState(0);
  const [isLessThanDay, setIsLessThanDay] = useState(false);
  const [countdownText, setCountdownText] = useState('will end in');
  const [timeOffset, setTimeOffset] = useState<number | null>(null);

  useEffect(() => {
    // Calculate the time offset between server time and client time when component mounts
    const clientTime = Date.now();
    const calculatedOffset = serverTime - clientTime;
    setTimeOffset(calculatedOffset);
  }, [serverTime]);

  useEffect(() => {
    const updateCountdown = () => {
      // Use client time adjusted with server time offset for authoritative time
      const now = Date.now() + (timeOffset || 0);
      let targetTime: number;

      if (now < endTime) {
        // Between start and end time: countdown to end
        targetTime = endTime;
        setCountdownText('will end in');
      } else {
        // After end time: show zeros
        setDays(0);
        setHours(0);
        setMinutes(0);
        setSeconds(0);
        setIsLessThanDay(false);
        setCountdownText('has ended');
        return;
      }

      const totalDays = differenceInDays(targetTime, now);
      const totalHours = differenceInHours(targetTime, now);
      const totalMinutes = differenceInMinutes(targetTime, now);
      const totalSeconds = differenceInSeconds(targetTime, now);

      if (totalDays < 1) {
        // Less than a day: show hours, minutes, seconds
        setIsLessThanDay(true);
        setDays(0);
        setHours(totalHours);
        setMinutes(totalMinutes % 60);
        setSeconds(totalSeconds % 60);
      } else {
        // More than a day: show days, hours, minutes
        setIsLessThanDay(false);
        setDays(totalDays);
        setHours(totalHours % 24);
        setMinutes(totalMinutes % 60);
        setSeconds(0);
      }
    };

    // Only start the countdown after timeOffset is calculated
    if (timeOffset !== null) {
      // Update immediately
      updateCountdown();

      const timer = setInterval(updateCountdown, 1000);

      return () => clearInterval(timer);
    }
  }, [startTime, endTime, timeOffset]);

  // Helper function to format numbers with leading zeros
  const formatNumber = (num: number) => num.toString().padStart(2, '0');

  // Get the display values based on whether it's less than a day
  const getDisplayValues = () => {
    if (isLessThanDay) {
      return {
        first: { value: formatNumber(hours), label: 'Hours' },
        second: { value: formatNumber(minutes), label: 'Minutes' },
        third: { value: formatNumber(seconds), label: 'Seconds' },
      };
    } else {
      return {
        first: { value: formatNumber(days), label: 'Days' },
        second: { value: formatNumber(hours), label: 'Hours' },
        third: { value: formatNumber(minutes), label: 'Minutes' },
      };
    }
  };

  const displayValues = getDisplayValues();

  return (
    <section className="bl-container bl-flex bl-justify-center bl-mb-20 md:bl-mt-10">
      <div className="bl-relative bl-overflow-hidden bl-w-full bl-border bl-border-[#292929] bl-flex bl-flex-col bl-items-center bl-pt-11 bl-pb-8 lg:bl-w-[957px]">
        <div
          className="bl-size-full bl-absolute bl-top-0 bl-left-0 bl-overflow-hidden bl-z-[-1]"
          style={{
            maskImage:
              'linear-gradient(90deg, transparent 0%, #fff 20%, #fff 80%, transparent 100%), linear-gradient(180deg, transparent 0%, #fff 20%, #fff 80%, transparent 100%)',
            maskComposite: 'intersect',
          }}
        >
          <img
            src="/images/home/<USER>"
            alt="ido"
            className="bl-w-[680px] bl-max-w-max bl-left-1/2 -bl-translate-x-1/2 lg:bl-w-full bl-absolute bl-top-0 bl-opacity-60"
          />
        </div>

        <h3 className="bl-text-[23px] bl-text-white bl-font-bold bl-text-center lg:bl-text-[33px]">
          Bitlayer CoinList <br className="lg:bl-hidden" />
          <span className="bl-text-primary">Token Sale</span>
        </h3>
        <p className="bl-text-lg lg:bl-text-2xl bl-text-white bl-mt-5">{countdownText}</p>
        <div className="bl-grow bl-flex bl-gap-4 lg:bl-pt-4 bl-scale-[0.6] lg:bl-scale-100">
          <div className="bl-flex bl-flex-col bl-items-center bl-gap-1">
            <div className="bl-flex bl-gap-1">
              <NumberBlock>{displayValues.first.value[0]}</NumberBlock>
              <NumberBlock>{displayValues.first.value[1]}</NumberBlock>
            </div>
            <div>{displayValues.first.label}</div>
          </div>

          <ClockPoints />

          <div className="bl-flex bl-flex-col bl-items-center bl-gap-1">
            <div className="bl-flex bl-gap-1">
              <NumberBlock>{displayValues.second.value[0]}</NumberBlock>
              <NumberBlock>{displayValues.second.value[1]}</NumberBlock>
            </div>
            <div>{displayValues.second.label}</div>
          </div>

          <ClockPoints />

          <div className="bl-flex bl-flex-col bl-items-center bl-gap-1">
            <div className="bl-flex bl-gap-1">
              <NumberBlock>{displayValues.third.value[0]}</NumberBlock>
              <NumberBlock>{displayValues.third.value[1]}</NumberBlock>
            </div>
            <div>{displayValues.third.label}</div>
          </div>
        </div>
        <Button
          className="bl-w-30 bl-text-base lg:bl-mt-9 lg:bl-text-xl lg:bl-w-48"
          overlayFrom="none"
          variant="default"
          asChild
        >
          <a href="https://coinlist.co/bitlayer" target="_blank" rel="noreferrer">
            <span>Participate</span>
          </a>
        </Button>
        <p className="bl-text-sm lg:bl-text-base bl-mt-4">August 1st - August 7th</p>

        <CornerMarkGroup />
      </div>
    </section>
  );
}

function ClockPoints() {
  return (
    <div className="bl-flex bl-flex-col bl-justify-center bl-gap-2 bl-h-[96px]">
      <div className="bl-size-[5px] bl-bg-primary"></div>
      <div className="bl-size-[5px] bl-bg-primary"></div>
    </div>
  );
}

function NumberBlock({ children }: { children: React.ReactNode }) {
  return (
    <div className="bl-relative bl-font-hammer-tongs bl-text-primary bl-w-[64px] bl-h-[96px] bl-text-[116px]/[93px]">
      <div className="bl-absolute bl-top-0 bl-w-full bl-h-[calc(50%-2px)]">
        <img
          src="/images/home/<USER>"
          alt=""
          className="bl-absolute bl-w-[91px] bl-h-[74px] bl-top-[-14px] bl-left-[-14px] bl-max-w-max"
        />
        <div className="bl-relative bl-size-full bl-overflow-hidden bl-flex bl-justify-center">
          <div className="bl-relative bl-top-[8px]">{children}</div>
        </div>
      </div>
      <div className="bl-absolute bl-bottom-0 bl-w-full bl-h-[calc(50%-2px)]">
        <img
          src="/images/home/<USER>"
          alt=""
          className="bl-absolute bl-w-[92px] bl-h-[74px] bl-top-[-15px] bl-left-[-14px] bl-max-w-max"
        />
        <div className="bl-relative bl-size-full bl-overflow-hidden bl-flex bl-justify-center">
          <div className="bl-absolute -bl-bottom-[4px]">{children}</div>
        </div>
      </div>
    </div>
  );
}
