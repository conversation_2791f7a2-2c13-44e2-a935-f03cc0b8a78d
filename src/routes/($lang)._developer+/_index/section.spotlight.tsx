import { Button } from '@/components/ui/button';
import { ScrambleSectionBadge } from '@/components/ui/section-badge';
import { Carousel, CarouselApi, CarouselContent, CarouselItem } from '@/components/ui/carousel';
import React, { useCallback, useEffect, useState } from 'react';
import { CornerMarkGroup } from '@/components/ui/corner-mark';
import { NavLink } from '@/components/i18n/link';
import { ArrowLeftIcon } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { cn } from '@/lib/utils';
import { useMediaQuery } from '@react-hook/media-query';
import Autoplay from 'embla-carousel-autoplay';

export const DotButton = (props: any) => {
  const { children, ...restProps } = props;

  return <div {...restProps}>{children}</div>;
};

export default function SpotlightSection() {
  const { t } = useTranslation();
  const isMobile = useMediaQuery('(max-width: 640px)');
  const key = 'pages.home.spotlight';
  const [api, setApi] = React.useState<CarouselApi>();
  const [prevBtnDisabled, setPrevBtnDisabled] = useState(true);
  const [nextBtnDisabled, setNextBtnDisabled] = useState(true);
  const [selectedIndex, setSelectedIndex] = useState(0);

  const onSelect = useCallback((emblaApi: any) => {
    setPrevBtnDisabled(!emblaApi.canScrollPrev());
    setNextBtnDisabled(!emblaApi.canScrollNext());
    setSelectedIndex(emblaApi.selectedScrollSnap());
  }, []);

  const onDotButtonClick = useCallback(
    (index: number) => {
      if (!api) return;
      api.scrollTo(index);
    },
    [api],
  );

  useEffect(() => {
    if (!api) return;
    onSelect(api);
    api.on('reInit', onSelect).on('select', onSelect);
  }, [api, onSelect]);

  useEffect(() => {
    api?.plugins()?.autoplay;
  }, [api]);

  const onAutoToggle = () => {
    const autoplay = api?.plugins()?.autoplay;
    const playOrStop = autoplay?.isPlaying?.() ? autoplay?.stop : autoplay?.play;
    playOrStop?.();
  };

  const plugin = [Autoplay({ playOnInit: true, delay: 3000 })];

  const carouseData = [
    {
      cover: '/images/home/<USER>',
      type: 'Latest Tech',
      title: t(`${key}.slideshow.item1.title`),
      content: t(`${key}.slideshow.item1.content`),
      link: 'https://blog.bitlayer.org/introducing_bitvm_bridge',
      btnText: t('common.learnMore'),
    },
    {
      cover: '/images/home/<USER>',
      // type: t(`${key}.slideshow.item1.type`),
      type: 'Latest Tech',
      title: t(`${key}.slideshow.item2.title`),
      content: t(`${key}.slideshow.item2.content`),
      link: 'https://docs.bitlayer.org/docs/Learn/Bitlayer%20Rollup/overview',
      btnText: t('common.learnMore'),
    },
    {
      cover: '/images/home/<USER>/NFT_Holder_Bonus.png',
      type: t(`${key}.slideshow.item3.type`),
      title: t(`${key}.slideshow.item3.title`),
      content: t(`${key}.slideshow.item3.content`),
      link: 'https://www.bitlayer.org/btcfi-v2',
    },
    {
      cover: '/images/home/<USER>/Super_Racer_Draw.png',
      type: t(`${key}.slideshow.item4.type`),
      title: t(`${key}.slideshow.item4.title`),
      content: t(`${key}.slideshow.item4.content`),
      link: 'https://www.bitlayer.org/assemble-cars',
    },
    {
      cover: '/images/home/<USER>/Dapp_Center.png',
      type: t(`${key}.slideshow.item5.type`),
      title: t(`${key}.slideshow.item5.title`),
      content: t(`${key}.slideshow.item5.content`),
      link: 'https://www.bitlayer.org/ready-player-one/dapps-center',
    },
    {
      cover: '/images/home/<USER>/Racer_Center.png',
      type: t(`${key}.slideshow.item6.type`),
      title: t(`${key}.slideshow.item6.title`),
      content: t(`${key}.slideshow.item6.content`),
      link: 'https://www.bitlayer.org/me',
    },
  ];

  return (
    <section className="bl-w-full" id="spotlight">
      <div className="bl-container md:bl-px-15 bl-mt-20 bl-w-screen bl-pb-20 md:bl-pb-30 bl-flex bl-flex-col bl-items-center bl-overflow-x-hidden">
        <ScrambleSectionBadge text={t(`${key}.spotlight`)} className="bl-z-20" />
        <div
          className="bl-group md:bl-w-[998px] bl-min-h-[383px] md:bl-h-[386px] bl-border bl-border-divider bl-relative bl-mt-6 md:bl-mt-14 bl-mx-5 bl-w-full bl-z-20"
          onMouseEnter={onAutoToggle}
          onMouseLeave={onAutoToggle}
        >
          <Carousel setApi={setApi} className="bl-h-full bl-z-20" plugins={plugin}>
            <CarouselContent className="bl-ml-0 bl-h-full">
              {carouseData.map((item, index) => (
                <CarouselItem
                  key={index}
                  className="bl-px-5 bl-py-8 md:bl-h-[384px] md:bl-w-[996px] md:bl-px-12 md:bl-py-10"
                >
                  <div
                    key={index}
                    className="bl-w-full bl-h-full bl-flex bl-flex-col md:bl-flex-row bl-items-center bl-justify-between"
                  >
                    <div className="bl-w-[294px] bl-h-[192px] md:bl-w-[448px] md:bl-h-[330px]">
                      <img
                        alt=""
                        src={item.cover}
                        className="bl-w-full bl-h-full bl-border-0.5 bl-border-primary-divider bl-border-t-[3px]"
                        onError={(e) => (e.currentTarget.src = '/images/home/<USER>')}
                      />
                    </div>
                    <div className="bl-flex-1 md:bl-h-full bl-flex bl-justify-center">
                      <div className="bl-mt-5 md:bl-mt-0 bl-h-full bl-flex bl-flex-col bl-items-center bl-justify-center bl-max-w-[320px]">
                        <div className="bl-border-y bl-border-primary bl-py-2 bl-px-7 bl-mb-3 md:bl-mb-12 bl-text-white bl-text-sm md:bl-text-xl bl-uppercase">
                          {item.type}
                        </div>
                        <div className="bl-text-white bl-text-xl md:bl-text-2xl bl-mb-2 md:bl-mb-7">
                          {item.title}
                        </div>
                        <div className="bl-flex-1 bl-flex bl-flex-col bl-justify-between bl-items-center bl-gap-2">
                          <div className="bl-text-secondary bl-text-xs md:bl-text-sm bl-text-center">
                            {item.content}
                          </div>
                          <NavLink to={item.link} target="_blank" rel="noreferrer">
                            <Button
                              variant="secondary"
                              keepOverlay
                              className={cn(
                                'bl-w-32 bl-h-9 bl-text-base md:bl-w-60 md:bl-h-12 md:bl-text-2xl bl-rounded-tr-sm md:bl-rounded-tr-md bl-rounded-bl-sm md:bl-rounded-bl-md bl-text-white md:bl-text-secondary-foreground',
                                {
                                  'btn-xs': isMobile,
                                },
                              )}
                            >
                              <div className="bl-text-base md:bl-text-xl bl-flex bl-items-center bl-gap-2 bl-font-body">
                                <span>{item.btnText || t('navigation.footer.Participate')}</span>
                              </div>
                            </Button>
                          </NavLink>
                        </div>
                      </div>
                    </div>
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
          </Carousel>

          <div
            className="bl-absolute bl-top-0 bl-left-0 bl-w-full bl-h-full bl-opacity-100 bl-z-10 bl-bg-[#151515]"
            style={{
              backgroundImage: `url("/images/home/<USER>")`,
              backgroundRepeat: 'repeat',
              backgroundSize: 'contain',
            }}
          ></div>

          <CornerMarkGroup variant="dark" className="bl-z-10 bl-border-primary" />

          <div className="bl-relative bl-top-12 md:bl-top-7 bl-w-fit bl-m-auto bl-flex bl-justify-center bl-items-center bl-h-[28px]">
            <div className="bl-flex bl-gap-2">
              {carouseData.map((_, index) => (
                <DotButton
                  key={index}
                  onClick={() => onDotButtonClick(index)}
                  className={cn(
                    'bl-w-[6px] bl-h-[6px] md:bl-w-2 md:bl-h-2 bl-rounded-full bl-bg-[#595959]',
                    {
                      'bl-bg-primary': index === selectedIndex,
                    },
                  )}
                />
              ))}
            </div>
          </div>

          <div className="bl-py-5 bl-hidden group-hover:md:bl-flex bl-absolute -bl-left-20 -bl-right-20 bl-top-30 bl-z-20 bl-justify-between bl-gap-8 bl-mt-8">
            <div
              onClick={() => api?.scrollPrev()}
              className={cn(
                'bl-group/item bl-cursor-pointer bl-w-10 bl-h-10 bl-flex bl-items-center bl-justify-center bl-rounded-full bl-bg-primary',
                {
                  'bl-w-0 bl-overflow-hidden': prevBtnDisabled,
                },
              )}
            >
              <ArrowLeftIcon
                // color="black"
                className="bl-w-4 bl-h-4 bl-text-black group-hover/item:bl-text-white"
              />
            </div>
            <div
              onClick={() => api?.scrollNext()}
              className={cn(
                'bl-group/item bl-cursor-pointer bl-w-10 bl-h-10 bl-flex bl-items-center bl-justify-center bl-rounded-full bl-bg-primary',
                {
                  'bl-w-0 bl-overflow-hidden': nextBtnDisabled,
                },
              )}
            >
              <ArrowLeftIcon
                // color="black"
                className="bl-w-4 bl-h-4 bl-rotate-180 bl-text-black group-hover/item:bl-text-white"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
