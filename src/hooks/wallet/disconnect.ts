import { useCallback } from 'react';
import { useDisconnect as useWagmiDisconnect } from 'wagmi';
import { useDisconnect as useBtcDisconnect } from '@/wallets/bitcoin/hooks';
import { NetworkType } from '@/wallets/config/type';
import { useWallet } from '@suiet/wallet-kit';

export function useDisconnect() {
  const { disconnect: wagmiDisconnect } = useWagmiDisconnect();
  const { disconnect: btcDisconnect } = useBtcDisconnect();
  const { disconnect: suiDisconnect } = useWallet();

  const handleDisconnect = useCallback(
    (network: NetworkType) => {
      if (network === NetworkType.btc) {
        btcDisconnect();
      } else if (network === NetworkType.evm) {
        wagmiDisconnect();
      } else if (network === NetworkType.sui) {
        suiDisconnect();
      }
    },
    [wagmiDisconnect, btcDisconnect, suiDisconnect],
  );

  return {
    disconnect: handleDisconnect,
  };
}
