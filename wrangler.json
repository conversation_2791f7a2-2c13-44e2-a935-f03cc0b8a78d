{"name": "bitlayer-website-v2", "compatibility_date": "2025-04-01", "compatibility_flags": ["nodejs_compat"], "main": "./server.ts", "assets": {"directory": "./build/client"}, "observability": {"enabled": true}, "upload_source_maps": false, "env": {"development": {"secrets_store_secrets": [{"binding": "CF_TURNSTILE_SECRET_KEY", "store_id": "7a4d3e02a3d14e61978419105dba31aa", "secret_name": "CF_TURNSTILE_SECRET_KEY"}, {"binding": "API_GAS_V2_SECRET", "store_id": "7a4d3e02a3d14e61978419105dba31aa", "secret_name": "API_GAS_V2_SECRET"}, {"binding": "API_STRAPI_TOKEN", "store_id": "7a4d3e02a3d14e61978419105dba31aa", "secret_name": "API_STRAPI_TOKEN"}, {"binding": "API_TEST_NET_CLIENT_SECRET", "store_id": "7a4d3e02a3d14e61978419105dba31aa", "secret_name": "API_TEST_NET_CLIENT_SECRET"}], "vars": {"ACTIVITY_FORM_URL": "https://activity-form.bitlayer.org", "API_GAS_URL": "https://api.bitlayer.org/gas", "API_GAS_V2_URL": "https://api-gasv2.bitlayer.org", "API_RANKING_LIST_URL": "https://api-activity.bitlayer.org", "API_STRAPI_URL": "https://formapi.bitlayer.org/api", "API_TEST_NET_CLIENT_ID": "b2cPv6xDo4uFNhybobZ9", "API_TEST_NET_URL": "https://api-testnet.bitlayer.org", "CF_TURNSTILE_SITE_KEY": "0x4AAAAAAAWS7pOzqlQ5JTns", "SENTRY_DSN": "https://<EMAIL>/4507524415619072"}}, "staging": {"secrets_store_secrets": [{"binding": "CF_TURNSTILE_SECRET_KEY", "store_id": "7a4d3e02a3d14e61978419105dba31aa", "secret_name": "CF_TURNSTILE_SECRET_KEY"}, {"binding": "API_GAS_V2_SECRET", "store_id": "7a4d3e02a3d14e61978419105dba31aa", "secret_name": "API_GAS_V2_SECRET"}, {"binding": "API_STRAPI_TOKEN", "store_id": "7a4d3e02a3d14e61978419105dba31aa", "secret_name": "API_STRAPI_TOKEN"}, {"binding": "API_TEST_NET_CLIENT_SECRET", "store_id": "7a4d3e02a3d14e61978419105dba31aa", "secret_name": "API_TEST_NET_CLIENT_SECRET"}], "vars": {"ACTIVITY_FORM_URL": "https://activity-form.bitlayer.org", "API_GAS_URL": "https://api.bitlayer.org/gas", "API_GAS_V2_URL": "https://api-gasv2.bitlayer.org", "API_RANKING_LIST_URL": "https://api-activity.bitlayer.org", "API_STRAPI_URL": "https://formapi.bitlayer.org/api", "API_TEST_NET_CLIENT_ID": "b2cPv6xDo4uFNhybobZ9", "API_TEST_NET_URL": "https://api-testnet.bitlayer.org", "CF_TURNSTILE_SITE_KEY": "0x4AAAAAAAWS7pOzqlQ5JTns", "SENTRY_DSN": "https://<EMAIL>/4508636174811136"}}, "production": {"secrets_store_secrets": [{"binding": "CF_TURNSTILE_SECRET_KEY", "store_id": "7a4d3e02a3d14e61978419105dba31aa", "secret_name": "CF_TURNSTILE_SECRET_KEY"}, {"binding": "API_GAS_V2_SECRET", "store_id": "7a4d3e02a3d14e61978419105dba31aa", "secret_name": "API_GAS_V2_SECRET"}, {"binding": "API_STRAPI_TOKEN", "store_id": "7a4d3e02a3d14e61978419105dba31aa", "secret_name": "API_STRAPI_TOKEN"}, {"binding": "API_TEST_NET_CLIENT_SECRET", "store_id": "7a4d3e02a3d14e61978419105dba31aa", "secret_name": "API_TEST_NET_CLIENT_SECRET"}], "vars": {"ACTIVITY_FORM_URL": "https://activity-form.bitlayer.org", "API_GAS_URL": "https://api.bitlayer.org/gas", "API_GAS_V2_URL": "https://api-gasv2.bitlayer.org", "API_RANKING_LIST_URL": "https://api-activity.bitlayer.org", "API_STRAPI_URL": "https://formapi.bitlayer.org/api", "API_TEST_NET_CLIENT_ID": "b2cPv6xDo4uFNhybobZ9", "API_TEST_NET_URL": "https://api-testnet.bitlayer.org", "CF_TURNSTILE_SITE_KEY": "0x4AAAAAAAWS7pOzqlQ5JTns", "SENTRY_DSN": "https://<EMAIL>/4508636174811136"}}}}