// Generated by Wrangler on Fri Feb 16 2024 15:52:18 GMT-0600 (Central Standard Time)
// After adding bindings to `wrangler.toml`, regenerate this interface via `npm build-cf-types`
interface Env {
  CF_TURNSTILE_SITE_KEY: string;
  CF_TURNSTILE_SECRET_KEY: SecretsStoreSecret;

  ACTIVITY_FORM_URL: string;

  API_GAS_URL: string;
  API_GAS_V2_SECRET: SecretsStoreSecret;
  API_GAS_V2_URL: string;

  API_RANKING_LIST_URL: string;

  API_STRAPI_TOKEN: SecretsStoreSecret;
  API_STRAPI_URL: string;

  API_TEST_NET_CLIENT_ID: string;
  API_TEST_NET_CLIENT_SECRET: SecretsStoreSecret;
  API_TEST_NET_URL: string;

  SENTRY_DSN: string;
}
